using System.ComponentModel.DataAnnotations;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    /// <summary>
    /// DTO for creating or updating CrossRateProviderConfiguration
    /// </summary>
    public class CreateUpdateCrossRateProviderConfigurationDto
    {
        /// <summary>
        /// The API endpoint URL for the external provider
        /// </summary>
        [Required]
        [Url(ErrorMessage = "Provider Base URL must be a valid URL")]
        [MaxLength(500)]
        public string ProviderBaseUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Authentication token for the provider API
        /// </summary>
        [Required]
        [MaxLength(1000)]
        public string ProviderAccessToken { get; set; } = string.Empty;
        
        /// <summary>
        /// The base currency to use in provider requests
        /// </summary>
        [Required]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Request Base Currency must be exactly 3 characters")]
        public string RequestBaseCurrency { get; set; } = string.Empty;
    }
}
