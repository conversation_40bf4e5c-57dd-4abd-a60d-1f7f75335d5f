﻿using Blazorise.DataGrid;
using Blazorise;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.JSInterop;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService
{
    public partial class PairingRuleMaster
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();

        private bool CanEditPairingRule { get; set; }

        private bool CanApprovePairingRule { get; set; }


        [Parameter]
        public string? PairingRuleId { get; set; }

        public PairingRuleDto PairingRule { get; set; }

        public PairingRuleMaster()
        {
            PairingRule = new PairingRuleDto();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
            await GetPairingRule();
            await SetToolbarItemsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:PairingRules"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            if (!PairingRule.IsApproved)
                Toolbar.AddButton(L["Approve"], async () => { await ApprovePairingRuleAsync(Guid.Parse(PairingRuleId)); }, IconName.Bookmark);



            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanEditPairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Edit);
            CanApprovePairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Approve);
        }


        private async Task GetPairingRule()
        {
            PairingRule = await PairingRulesAppService.GetAsync(Guid.Parse(PairingRuleId));
        }


        private async Task UpdatePairingRuleAsync()
        {
            PairingRuleUpdateDto pairingRuleUpdateDto = new PairingRuleUpdateDto()
            {
                Name = PairingRule.Name,
                Description = PairingRule.Description,
                EffectiveDate = PairingRule.EffectiveDate,
                ConcurrencyStamp = PairingRule.ConcurrencyStamp
            };




            await PairingRulesAppService.UpdateAsync(Guid.Parse(PairingRuleId), pairingRuleUpdateDto);
            await GetPairingRule();

            await Message.Success(L["UpdateComplete"]);
        }



        private async Task ApprovePairingRuleAsync(Guid RuleId)
        {
            if (!await Message.Confirm(L["ApproveConfirmationMessage"])) return;
            await PairingRulesAppService.SetApproveAsync(RuleId);

            await Message.Success(L["ApproveComplete"]);

            await GetPairingRule();

        }


    }
}
