using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public interface IPairingRuleRepository : IRepository<PairingRule, Guid>
    {
        Task<List<PairingRule>> GetListAsync(
            string filterText = null,
            string name = null,
            DateTime? effectiveDateMin = null,
            DateTime? effectiveDateMax = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedBy = null,
            string approvedByName = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            bool? isArchived = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string name = null,
            DateTime? effectiveDateMin = null,
            DateTime? effectiveDateMax = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedBy = null,
            string approvedByName = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            bool? isArchived = null,
            CancellationToken cancellationToken = default);
    }
}