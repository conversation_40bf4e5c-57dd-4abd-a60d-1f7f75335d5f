using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulesUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string Name { get; set; }
        public string? Description { get; set; }
        public DateTime ActivationDate { get; set; }
        public ExchangeRulesScope ExchangeRuleScope { get; set; }
        public virtual bool IsReprintReceiptAllowed { get; set; }
        public virtual double ApprovalLimit { get; set; }

        public virtual double RoundUpFee { get; set; }
        public string ConcurrencyStamp { get; set; }
    }
}