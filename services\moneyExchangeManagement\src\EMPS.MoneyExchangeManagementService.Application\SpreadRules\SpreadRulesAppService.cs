using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.CustomerService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CustomerService;
using EMPS.Shared.Enum;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using static EMPS.CustomerService.Permissions.CustomerServicePermissions;
using Bogus.DataSets;
using System.Data.Entity;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{

    [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Default)]
    public class SpreadRulesAppService : MoneyExchangeManagementServiceAppService, ISpreadRulesAppService
    {
        private readonly IDistributedCache<SpreadRuleExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly ISpreadRuleRepository _spreadRuleRepository;
        private readonly SpreadRuleManager _spreadRuleManager;


        private readonly ISpreadRuleDetailRepository _spreadRuleDetailRepository;
        private readonly SpreadRuleDetailManager _spreadRuleDetailManager;
        private readonly ICurrenciesAppService _currencyAppService;

        public SpreadRulesAppService(ICurrenciesAppService currencyAppService,
            SpreadRuleDetailManager spreadRuleDetailManager,
            ISpreadRuleDetailRepository spreadRuleDetailRepository,
            ISpreadRuleRepository spreadRuleRepository, SpreadRuleManager spreadRuleManager,
            IDistributedCache<SpreadRuleExcelDownloadTokenCacheItem, string> excelDownloadTokenCache)
        {
            _currencyAppService = currencyAppService;
            _spreadRuleDetailManager = spreadRuleDetailManager;
            _spreadRuleDetailRepository = spreadRuleDetailRepository;
            _excelDownloadTokenCache = excelDownloadTokenCache;
            _spreadRuleRepository = spreadRuleRepository;
            _spreadRuleManager = spreadRuleManager;
        }

        public async Task<SpreadRuleDto> GetWithDetailsAsync(Guid id, SpreadRuleType type)
        {
            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(await _spreadRuleRepository.GetWithDetailsAsync(id, type));
        }

        public virtual async Task<PagedResultDto<SpreadRuleDto>> GetListAsync(GetSpreadRulesInput input)
        {
            var totalCount = await _spreadRuleRepository.GetCountAsync(input.FilterText, input.RuleName, input.ActivationDateMin, input.ActivationDateMax, input.Scope, input.Description, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax);
            var items = await _spreadRuleRepository.GetListAsync(input.FilterText, input.RuleName, input.ActivationDateMin, input.ActivationDateMax, input.Scope, input.Description, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<SpreadRuleDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<SpreadRule>, List<SpreadRuleDto>>(items)
            };
        }

        public virtual async Task<SpreadRuleDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(await _spreadRuleRepository.GetAsync(id));
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _spreadRuleRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Create)]
        public virtual async Task<SpreadRuleDto> CreateAsync(SpreadRuleCreateDto input)
        {

            await ValidateSpreadRuleInputAsync(null, input.RuleName, input.ActivationDate);


            var spreadRule = await _spreadRuleManager.CreateAsync(input.RuleName, input.Description,
    input.Scope, input.ActivationDate, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTime,
    input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId, input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate
    );


            // Retrieve currencies
            var result = await _currencyAppService.GetListAsync(new GetCurrenciesInput { Status = true });
            var currencies = result.Items
                .Where(item => item.Code != "SYP")
                .ToList();
            var spreadRuleDetails = new List<SpreadRuleDetail>();

            if (currencies.Any())
            {
                foreach (var currency in currencies)
                {
                    foreach (var type in Enum.GetValues(typeof(SpreadRuleType)))
                    {
                        var spreadRuleDetail = new SpreadRuleDetail(
                                  GuidGenerator.Create(),
                                  spreadRule.Id, // Use the spreadRule ID from the input
                                  currency.Id,
                                  currency.Code,
                                  (SpreadRuleType)type,
                                  0,
                                  0,
                                  0,
                                  0,
                                  0,
                                  0
                              );
                        spreadRuleDetails.Add(spreadRuleDetail); // Add to list
                    }
                }
            }

            // Save all SpreadRuleDetail objects to the repository
            if (spreadRuleDetails.Any())
            {
                await _spreadRuleDetailRepository.InsertManyAsync(spreadRuleDetails); // Assuming an InsertManyAsync method exists
            }

            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Edit)]
        public virtual async Task<SpreadRuleDto> UpdateAsync(Guid id, SpreadRuleUpdateDto input)
        {

            await ValidateSpreadRuleInputAsync(id, input.RuleName, input.ActivationDate);
            var spreadRule = await GetExistingSpreadRuleForEditAsync(id);


            spreadRule = await _spreadRuleManager.UpdateAsync(
       id, input.RuleName, input.Description,
       input.Scope, input.ActivationDate, input.IsApproved, input.ApprovedByUserId,
       input.ApprovedByUserName, input.ApprovedDateTime, input.IsArchived,
       input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId,
       input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate,
       input.ConcurrencyStamp
       );
            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var items = await _spreadRuleRepository.GetListAsync(input.FilterText, input.RuleName, input.ActivationDateMin, input.ActivationDateMax, input.Scope, input.Description, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax);

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(ObjectMapper.Map<List<SpreadRule>, List<SpreadRuleExcelDto>>(items));
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "SpreadRules.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new SpreadRuleExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }




        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Approve)]
        public virtual async Task<SpreadRuleDto> ApproveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            await ValidateSpreadRuleInputAsync(id, input.RuleName, input.ActivationDate);

            var spreadRule = await GetExistingSpreadRuleForEditAsync(id);


            spreadRule = await _spreadRuleManager.UpdateAsync(
            id, input.RuleName, input.Description,
            input.Scope, input.ActivationDate, input.IsApproved, input.ApprovedByUserId,
            input.ApprovedByUserName, input.ApprovedDateTime, input.IsArchived,
            input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId,
            input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate,
            input.ConcurrencyStamp
            );

            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Archive)]
        public virtual async Task<SpreadRuleDto> ArchiveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            try
            {
                var spreadRule = await _spreadRuleRepository.GetAsync(id);
                if (spreadRule == null)
                    throw new UserFriendlyException(L["spreadRuleNotExist"]);
                if (!spreadRule.IsApproved)
                    throw new UserFriendlyException(L["YouCantArchivespreadRuleNotApproved"]);
                var spreadRuleUpdated = await _spreadRuleManager.UpdateAsync(id, input.RuleName, input.Description, input.Scope, input.ActivationDate,
                    input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTime,
                    input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId, input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate,
                    input.ConcurrencyStamp);

                return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
            }
            catch (Exception)
            {
                throw;
            }
        }
        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.UnArchive)]
        public virtual async Task<SpreadRuleDto> UnArchiveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            try
            {
                var spreadRule = await _spreadRuleRepository.GetAsync(id);
                if (spreadRule == null)
                    throw new UserFriendlyException(L["spreadRuleNotExist"]);
                if (!spreadRule.IsApproved)
                    throw new UserFriendlyException(L["YouCantArchivespreadRuleNotApproved"]);



                var spreadRuleUpdated = await _spreadRuleManager.UpdateAsync(id, input.RuleName, input.Description, input.Scope, input.ActivationDate,
                    input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTime,
                    input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId, input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate,
                    input.ConcurrencyStamp);


                return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
            }
            catch (Exception)
            {
                throw;
            }
        }
        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Duplicate)]

        public async Task<SpreadRuleDto> CreateDuplicateAsync(SpreadRuleDto input)
        {
            try
            {

                if (input.ActivationDate == default)
                {
                    throw new UserFriendlyException(L["ActivationDateRequrid"]);
                }


                input.RuleName = input.RuleName + "_Copy";

                var isExistBefor = await _spreadRuleRepository.AnyAsync(a => a.RuleName == input.RuleName);
                if (isExistBefor)
                    throw new UserFriendlyException(L["ThisRuleNameExistBefor"]);


                var spreadRuleCreated = await _spreadRuleRepository.GetAsync(input.Id);
                if (spreadRuleCreated.IsApproved == true)
                {
                    input.IsApproved = false;
                    input.ApprovedByUserName = null;
                    input.ApprovedByUserId = null;
                    input.ApprovedDateTime = null;
                }
                if (spreadRuleCreated.IsArchived == true)
                {
                    input.IsArchived = false;
                    input.ArchivedByUserId = null;
                    input.ArchivedByUserName = null;
                    input.ArchivedDateTime = null;
                }

                var spreadRule = await _spreadRuleManager.CreateAsync(input.RuleName, input.Description,
           input.Scope, input.ActivationDate, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTime,
           input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.UnArchivedByUserId, input.UnArchivedByUserName, input.ArchivedDateTime, input.UnArchivedByDate
           );

                // Retrieve currencies
                var result = await _currencyAppService.GetListAsync(new GetCurrenciesInput { Status = true });
                var currencies = result.Items
                    .Where(item => item.Code != "SYP")
                    .ToList();

                if (input.Id != Guid.Empty)
                {
                    // Retrieve existing details
                    var spreadRuleDetails = (await _spreadRuleDetailRepository.GetQueryableAsync())
                        .Where(detail => detail.SpreadRuleId == input.Id)
                        .ToList();

                    // Existing Currency IDs
                    var existingCurrencyIds = new HashSet<Guid>(spreadRuleDetails.Select(d => d.CurrencyId));
                    var validCurrencyIds = new HashSet<Guid>(currencies.Select(c => c.Id));

                    // Find missing currencies
                    var missingCurrencyIds = validCurrencyIds.Except(existingCurrencyIds).ToList();
                    var newspreadRuleDetails = new List<SpreadRuleDetail>();

                    // Create new details for missing currencies
                    if (missingCurrencyIds.Any())
                    {
                        foreach (var currencyId in missingCurrencyIds)
                        {
                            var currency = currencies.First(c => c.Id == currencyId);

                            foreach (var type in Enum.GetValues<SpreadRuleType>())
                            {
                                var spreadRuleDetail = new SpreadRuleDetail(
                                    GuidGenerator.Create(),
                                    spreadRule.Id, // Use the spreadRule ID from the input
                                    currencyId,
                                    currency.Code,
                                    type,
                                    0,
                                    0,
                                    0,
                                    0,
                                    0,
                                    0
                                );

                                newspreadRuleDetails.Add(spreadRuleDetail);
                            }
                        }

                        // Save all new details to the repository
                        if (newspreadRuleDetails.Any())
                        {
                            await _spreadRuleDetailRepository.InsertManyAsync(newspreadRuleDetails);
                        }
                    }

                    // Process existing details
                    foreach (var detail in spreadRuleDetails.Where(d => validCurrencyIds.Contains(d.CurrencyId)))
                    {
                        await _spreadRuleDetailManager.CreateAsync(
                            spreadRule.Id,
                            detail.CurrencyId,
                            detail.CurrencyCode,
                            detail.Type,
                            detail.BidSpread,
                            detail.BidMaxDiscount,
                            detail.BidMaxMarkdown,
                            detail.AskSpread,
                            detail.AskMaxDiscount,
                            detail.AskMaxMarkup


                        );
                    }
                }


                return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
            }
            catch (BusinessException businessException)
            {
                throw;
            }
        }










        private async Task ValidateSpreadRuleInputAsync(Guid? id, string ruleName, DateTime? activationDate)
        {
            if (string.IsNullOrWhiteSpace(ruleName))
                throw new UserFriendlyException(L["NameRequired"]);

            if (!activationDate.HasValue || activationDate.Value.ToLocalTime() < DateTime.Now.ToLocalTime())
                throw new UserFriendlyException(L["ActivationDateMustBiggerThanDateOfNow"]);

            var isDuplicate = await _spreadRuleRepository.AnyAsync(a =>
                a.RuleName == ruleName && (!id.HasValue || a.Id != id.Value));

            if (isDuplicate)
                throw new UserFriendlyException(L["ThisRuleNameExistBefor"]);
        }

        private async Task<SpreadRule> GetExistingSpreadRuleForEditAsync(Guid id)
        {
            var spreadRule = await _spreadRuleRepository.GetAsync(id)
                              ?? throw new UserFriendlyException(L["ThisSpreadRuleNotFound"]);

            if (spreadRule.IsApproved)
                throw new UserFriendlyException(L["ThisSpreadRuleAlreadyApproved"]);

            return spreadRule;
        }

        public async Task<List<SpreadRuleDetailWithNavigationPropertiesDto>> GetSpreadRulesWithDetail(bool isGlobal, List<Guid?> spreadRuleIds)
        {
            var spreadRuleDetails = new List<SpreadRuleDetailWithNavigationProperties>();

            if (isGlobal)
            {
                // Get the last approved global spread rule
                var lastGlobalSpreadRule = (await _spreadRuleRepository.GetQueryableAsync())
                    .Where(x => x.Scope == SpreadRuleScope.Global && x.IsApproved)
                    .OrderByDescending(x => x.ActivationDate)
                    .FirstOrDefault();

                if (lastGlobalSpreadRule != null)
                {
                    var globalDetail = (await _spreadRuleDetailRepository
                        .GetListWithNavigationPropertiesAsync(spreadRuleId: lastGlobalSpreadRule.Id)).First();

                    if (globalDetail != null)
                    {
                        spreadRuleDetails.Add(globalDetail);
                    }
                }
            }

            // Add spread rules by IDs (excluding the global one if already added)
            var idsToFetch = spreadRuleIds
                .Where(id => id.HasValue)
                .Select(id => id.Value)
                .ToHashSet();

            // Avoid duplicate if global rule already added
            if (isGlobal && spreadRuleDetails.Any())
            {
                var globalId = spreadRuleDetails.First().SpreadRule.Id;
                idsToFetch.Remove(globalId);
            }

            foreach (var id in idsToFetch)
            {
                var detail = (await _spreadRuleDetailRepository.GetListWithNavigationPropertiesAsync(spreadRuleId: id)).First();
                if (detail != null)
                {
                    spreadRuleDetails.Add(detail);
                }
            }

            return ObjectMapper.Map<List<SpreadRuleDetailWithNavigationProperties>, List<SpreadRuleDetailWithNavigationPropertiesDto>>(spreadRuleDetails);
        }



    }
}