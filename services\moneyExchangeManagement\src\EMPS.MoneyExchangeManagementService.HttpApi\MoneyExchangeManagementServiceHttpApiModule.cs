﻿using Localization.Resources.AbpUi;
using Microsoft.Extensions.DependencyInjection;
using EMPS.MoneyExchangeManagementService.Localization;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService;

[DependsOn(
    typeof(MoneyExchangeManagementServiceApplicationContractsModule),
    typeof(AbpAspNetCoreMvcModule))]
public class MoneyExchangeManagementServiceHttpApiModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<IMvcBuilder>(mvcBuilder =>
        {
            mvcBuilder.AddApplicationPartIfNotExists(typeof(MoneyExchangeManagementServiceHttpApiModule).Assembly);
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Resources
                .Get<MoneyExchangeManagementServiceResource>()
                .AddBaseTypes(typeof(AbpUiResource));
        });
    }
}
