using EMPS.Shared.Enum.ExchangeRules;
using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailExcelDownloadDto
    {
        public string DownloadToken { get; set; }

        public string? FilterText { get; set; }

        public string? CurrencyName { get; set; }
        public Guid? CurrencyID { get; set; }
        public bool? AllowedToBuy { get; set; }
        public double? MinAmountToBuyMin { get; set; }
        public double? MinAmountToBuyMax { get; set; }
        public double? MaxAmountToBuyMin { get; set; }
        public double? MaxAmountToBuyMax { get; set; }
        public double? MaxDailyAmountToBuyMin { get; set; }
        public double? MaxDailyAmountToBuyMax { get; set; }
        public bool? AllowedToSell { get; set; }
        public double? MinAmountToSellMin { get; set; }
        public double? MinAmountToSellMax { get; set; }
        public double? MaxAmountToSellMin { get; set; }
        public double? MaxAmountToSellMax { get; set; }
        public double? MaxDailyAmountToSellMin { get; set; }
        public double? MaxDailyAmountToSellMax { get; set; }
        public bool? AllowedToSellBelowCenterCost { get; set; }
        public bool? AllowedToSellBelowCompanyCost { get; set; }
        public Guid ExchangeRuleMasterID { get; set; }
        public ExchangeRuleDetailsType ExchangeRuleDetailType { get; set; }
        public bool? IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime? ApprovedDateTimeMin { get; set; }
        public DateTime? ApprovedDateTimeMax { get; set; }
        public bool? IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime? ArchivedDateTimeMin { get; set; }
        public DateTime? ArchivedDateTimeMax { get; set; }
        public Guid? UnArchivedByUserId { get; set; }
        public string? UnArchivedByUserName { get; set; }
        public DateTime? UnArchivedByDateMin { get; set; }
        public DateTime? UnArchivedByDateMax { get; set; }

        public ExchangeRuleDetailExcelDownloadDto()
        {

        }
    }
}