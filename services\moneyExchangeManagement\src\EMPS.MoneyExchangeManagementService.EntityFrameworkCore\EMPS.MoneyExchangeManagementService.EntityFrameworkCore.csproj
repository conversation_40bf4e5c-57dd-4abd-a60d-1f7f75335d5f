<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeManagementService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.SqlServer" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore" Version="7.3.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Domain\EMPS.MoneyExchangeManagementService.Domain.csproj" />

    <ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Enum\EMPS.Shared.Enum.csproj" />

  </ItemGroup>

</Project>
