@page "/ExchangeRules"
@attribute [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default)]
@using EMPS.CompanyService.ServicePoints
@using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
@using EMPS.MoneyExchangeManagementService.ExchangeRuless
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject IExchangeRulessAppService ExchangeRulessAppService
@inject IExchangeRuleDetailsAppService ExchangeRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager
@using EMPS.Shared.Enum.ExchangeRules
@inject IJSRuntime JSRuntime
@inject IServicePointsAppService _servicePointService


@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["ExchangeRules"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="ExchangeRuleSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText"
                              Autofocus="true"
                              Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="ExchangeRuleSearchForm" Clicked="SearchAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
            <Accordion>
                <Collapse Visible="@FilterCollapse">
                    <CollapseHeader>
                        <Heading Size="HeadingSize.Is5">
                            <AccordionToggle>@L["Filter"]</AccordionToggle>
                        </Heading>
                    </CollapseHeader>
                    <CollapseBody>
                        <Div>
                            <RadioGroup TValue="ArchiveFilter" Name="ArchiveStatus" CheckedValue="@RadioArchiveCheckedValue"
                                        CheckedValueChanged="@OnRadioArchiveCheckedValueChanged">

                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.All">@L["All"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.Archived">@L["Archived"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.UnArchive">@L["UnArchived"]</Radio>
                            </RadioGroup>
                        </Div>
                    </CollapseBody>
                </Collapse>
            </Accordion>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="ExchangeRulesDto"
                  Data="ExchangeRuleList"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  Responsive="true"
                  PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="ExchangeRulesDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="ExchangeRulesDto" EntityActionsColumn="@EntityActionsColumn">
                            @if (!context.IsApproved)
                            {
                                <EntityAction TItem="ExchangeRulesDto"
                                              Visible="@CanEditExchangeRule"
                                              Clicked="async () => await OpenEditExchangeRuleModalAsync(context)"
                                              Text="@L["Edit"]"></EntityAction>
                                <EntityAction TItem="ExchangeRulesDto"
                                              Visible="@CanDeleteExchangeRule"
                                              Clicked="() => DeleteExchangeRuleAsync(context)"
                                              Text="@L["Delete"]"
                                              ConfirmationMessage="@(() => L["DeleteConfirmationMessage"])"
                                              ></EntityAction>
                            }
                            @if (!context.IsApproved)
                            {
                                <EntityAction TItem="ExchangeRulesDto"
                                              Visible="@CanApproveExchangeRule"
                                              Clicked="() => ApproveExchangeRuleAsync(context)"
                                              Text="@L["Approve"]"
                                              ConfirmationMessage="@(() => L["ApproveConfirmationMessage"])"
                                              ></EntityAction>
                            }
                            @if (context.IsApproved && !context.IsArchived)
                            {
                                <EntityAction TItem="ExchangeRulesDto"
                                              Visible="@CanArchiveExchangeRule"
                                              Clicked="() => ArchiveExchangeRuleAsync(context)"
                                              Text="@L["Archive"]"
                                              ConfirmationMessage="@(() => L["ArchiveConfirmationMessage"])"
                                              ></EntityAction>
                            }
                            @if (context.IsApproved && context.IsArchived)
                            {
                                <EntityAction TItem="ExchangeRulesDto"
                                              Visible="@CanUnArchiveExchangeRule"
                                              Clicked="() => UnArchiveExchangeRuleAsync(context)"
                                              Text="@L["UnArchive"]"
                                              ConfirmationMessage="@(() => L["UnArchiveConfirmationMessage"])"
                                              ></EntityAction>
                            }
                            <EntityAction TItem="ExchangeRulesDto"
                                          Visible="@CanDuplicateExchangeRule"
                                          Clicked="() => DuplicateExchangeRuleAsync(context)"
                                          Text="@L["Duplicate"]"
                                          ConfirmationMessage="@(() => L["DuplicateConfirmationMessage"])"
                                          ></EntityAction>
                                          @if (context.IsApproved && !context.IsArchived && context.ExchangeRuleScope == ExchangeRulesScope.ServicePoint)
                            {
                            <EntityAction TItem="ExchangeRulesDto"
                                          Visible="@CanApplyExchangeRule"
                                          Clicked="async () => await ApplyExchangeRuleAsync(context)"
                                          Text="@L["Apply"]"></EntityAction>
                            }
                            <EntityAction TItem="ExchangeRulesDto"
                                          Visible="@CanViewDetails"
                                          Clicked="async () => await NavigateToDetailsAsync(context.Id.ToString())"
                                          Text="@L["Details"]"></EntityAction>
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="Name"
                                Caption="@L["Name"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="Description"
                                Caption="@L["Description"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ActivationDate"
                                Caption="@L["ActivationDate"]">
                    <DisplayTemplate>
                        @context.ActivationDate.ToString("d")
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ExchangeRuleScope"
                                Caption="@L["ExchangeRuleScope"]">
                    <DisplayTemplate>
                        @L[$"Enum:ExchangeRulesScope.{(int) context.ExchangeRuleScope}"]
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ApprovalLimit"
                                Caption="@L["ApprovalLimit"]">
                </DataGridColumn>
                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="RoundUpFee"
                                Caption="@L["RoundUpFee"]">
                </DataGridColumn>
                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="IsReprintReceiptAllowed"
                                Caption="@L["IsReprintReceiptAllowed"]">
                    <DisplayTemplate>
                        @if (context.IsReprintReceiptAllowed)
                        {
                            <Icon Name="IconName.Check" TextColor="TextColor.Success" />
                        }
                        else
                        {
                            <Icon Name="IconName.Times" TextColor="TextColor.Danger" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="IsApproved"
                                Caption="@L["IsApproved"]">
                    <DisplayTemplate>
                        @if (context.IsApproved)
                        {
                            <Icon Name="IconName.Check" TextColor="TextColor.Success" />
                        }
                        else
                        {
                            <Icon Name="IconName.Times" TextColor="TextColor.Danger" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="IsArchived"
                                Caption="@L["IsArchived"]">
                    <DisplayTemplate>
                        @if (context.IsArchived)
                        {
                            <Icon Name="IconName.Check" TextColor="TextColor.Success" />
                        }
                        else
                        {
                            <Icon Name="IconName.Times" TextColor="TextColor.Danger" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ApprovedByUserName"
                                Caption="@L["ApprovedByName"]">
                    <DisplayTemplate>
                        @(context.ApprovedByUserName ?? "-")
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ApprovedDateTime"
                                Caption="@L["ApprovedDateTime"]">
                    <DisplayTemplate>
                        @if (context.IsApproved && context.ApprovedDateTime != DateTime.MinValue)
                        {
                            @context.ApprovedDateTime.ToString("dd/MM/yyyy HH:mm")
                        }
                        else
                        {
                            <span>-</span>
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ArchivedByUserName"
                                Caption="@L["ArchivedByName"]">
                    <DisplayTemplate>
                        @(context.ArchivedByUserName ?? "-")
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="ArchivedDateTime"
                                Caption="@L["ArchivedDateTime"]">
                    <DisplayTemplate>
                        @if (context.IsArchived && context.ArchivedDateTime != DateTime.MinValue)
                        {
                            @context.ArchivedDateTime?.ToString("dd/MM/yyyy HH:mm")
                        }
                        else
                        {
                            <span>-</span>
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="UnArchivedByUserName"
                                Caption="@L["UnArchivedByUserName"]">
                    <DisplayTemplate>
                        @(context.UnArchivedByUserName ?? "-")
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRulesDto"
                                Field="UnArchivedByDate"
                                Caption="@L["UnArchivedByDate"]">
                    <DisplayTemplate>
                        @if (!context.IsArchived && context.UnArchivedByDate != DateTime.MinValue)
                        {
                            @context.UnArchivedByDate?.ToString("dd/MM/yyyy HH:mm")
                        }
                        else
                        {
                            <span>-</span>
                        }
                    </DisplayTemplate>
                </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreateExchangeRuleModal" Closing="@CreateExchangeRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreateExchangeRuleForm">
            <ModalHeader>
                <ModalTitle>@L["NewExchangeRules"]</ModalTitle>
                <CloseButton Clicked="CloseCreateExchangeRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@NewExchangeRuleValidations"
                            Mode="ValidationMode.Auto"
                            Model="@NewExchangeRule"
                            ValidateOnLoad="false">
                      <Validation>
                     <Field>
                        <FieldLabel>@L["Name"]</FieldLabel>
                            <TextEdit Size="Blazorise.Size.Large" @bind-Text="@NewExchangeRule.Name">
                        </TextEdit>
                     </Field>
                    </Validation>
                    <Field>
                        <FieldLabel>@L["ExchangeRuleScope"]</FieldLabel>
                        <Select TValue="ExchangeRulesScope" @bind-SelectedValue="@NewExchangeRule.ExchangeRuleScope">
                            @foreach (var itemValue in Enum.GetValues(typeof(ExchangeRulesScope)))
                            {
                                <SelectItem TValue="ExchangeRulesScope" Value="@((ExchangeRulesScope) itemValue)">
                                    @L[$"Enum:ExchangeRulesScope.{(int) itemValue}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>
                    <Field>
                        <FieldLabel>@L["Description"]</FieldLabel>
                        <MemoEdit @bind-Text="@NewExchangeRule.Description" />
                    </Field>
                    <Field>
                        <FieldLabel>@L["ActivationDate"]</FieldLabel>
                        <DateEdit TValue="DateTime" @bind-Date="@NewExchangeRule.ActivationDate" />
                    </Field>
                        <Field>
                            <FieldLabel>@L["RoundUpFee"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@NewExchangeRule.RoundUpFee" >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                        <Field>
                            <FieldLabel>@L["ApprovalLimit"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@NewExchangeRule.ApprovalLimit">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    <Field>
                        <FieldLabel>@L["IsReprintReceiptAllowed"]</FieldLabel>
                        <Check TValue="bool" @bind-Checked="@NewExchangeRule.IsReprintReceiptAllowed" />
                    </Field>
                </Validations>
            </ModalBody>
        </Form>
        <ModalFooter>
            <Button Color="Color.Secondary"
                    Clicked="CloseCreateExchangeRuleModalAsync">
                @L["Cancel"]
            </Button>
            <SubmitButton Form="CreateExchangeRuleForm" Clicked="CreateExchangeRuleAsync" />
        </ModalFooter>
    </ModalContent>
</Modal>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditExchangeRuleModal" Closing="@EditExchangeRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditExchangeRuleForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditExchangeRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingExchangeRuleValidations"
                            Mode="ValidationMode.Auto"
                            Model="@EditingExchangeRule"
                            ValidateOnLoad="false">
                    <Validation>
                        <Field>
                            <FieldLabel>@L["Name"]</FieldLabel>
                            <TextEdit Size="Blazorise.Size.Large" @bind-Text="@EditingExchangeRule.Name">
                            </TextEdit>
                        </Field>
                    </Validation>
                    <Field>
                        <FieldLabel>@L["ExchangeRuleScope"]</FieldLabel>
                        <Select TValue="ExchangeRulesScope" @bind-SelectedValue="@EditingExchangeRule.ExchangeRuleScope">
                            @foreach (var itemValue in Enum.GetValues(typeof(ExchangeRulesScope)))
                            {
                                <SelectItem TValue="ExchangeRulesScope" Value="@((ExchangeRulesScope) itemValue)">
                                    @L[$"Enum:ExchangeRulesScope.{(int) itemValue}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>
                    <Field>
                        <FieldLabel>@L["Description"]</FieldLabel>
                        <MemoEdit @bind-Text="@EditingExchangeRule.Description" />
                    </Field>
                    <Field>
                        <FieldLabel>@L["ActivationDate"]</FieldLabel>
                        <DateEdit TValue="DateTime" @bind-Date="@EditingExchangeRule.ActivationDate" />
                    </Field>
                        <Field>
                            <FieldLabel>@L["RoundUpFee"]</FieldLabel>
                        <NumericPicker TValue="double" @bind-Value="@EditingExchangeRule.RoundUpFee">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>

                        <Field>
                            <FieldLabel>@L["ApprovalLimit"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRule.ApprovalLimit" >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    <Field>
                        <FieldLabel>@L["IsReprintReceiptAllowed"]</FieldLabel>
                        <Check TValue="bool" @bind-Checked="@EditingExchangeRule.IsReprintReceiptAllowed" />
                    </Field>
                </Validations>
            </ModalBody>
        </Form>
        <ModalFooter>
            <Button Color="Color.Secondary"
                    Clicked="CloseEditExchangeRuleModalAsync">
                @L["Cancel"]
            </Button>
            <SubmitButton Form="EditExchangeRuleForm" Clicked="UpdateExchangeRuleAsync" />
        </ModalFooter>
    </ModalContent>
</Modal>

@* ************************* APPLY RULE ON SERVICE POINT MODAL ************************* *@
<Modal @ref="ApplyExchangeRuleOnServicePointModal" Closing="@ApplyExchangeRuleOnServicePointModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true" Size="ModalSize.Fullscreen">
        <ModalHeader>
            <ModalTitle>@L["ApplyExchangeRuleOnServicePoint"]</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            <Row Margin="Margin.Is2.FromBottom">
                <Column>
                    <Text>@L["ExchangeRule"] : @RuleToBeAppliedOnServicePoint.Name</Text>
                </Column>
            </Row>

            <MPS.Components.ServicePoint.ServicePointsTransferList EndServicePoints="@EndList"
                                                                   StartServicePoints="@StartList"
                                                                   AppliedServiePointsChanged="HandleAppliedCallback"
                                                                   DisappliedServiePointsChanged="HandleDisappliedCallback" />

        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary"
                    Clicked="CloseApplyExchangeRuleOnServicePointModalAsync">
                @L["Close"]
            </Button>
        </ModalFooter>
    </ModalContent>
</Modal>
