@page "/custom-modal-demo"
@namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
@attribute [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default)]

@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Blazorise
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.Application.Dtos
@using EMPS.MoneyExchangeManagementService.Localization
@using System.Collections.Generic


@inject IStringLocalizer<MoneyExchangeManagementServiceResource> L

<PageHeader Title="Custom Modal Demo">
</PageHeader>

<Card>
    <CardBody>
        <Button Color="Color.Primary" Clicked="OpenComplexModal">
            <Icon Name="IconName.List" Class="me-2"></Icon>Open Complex Modal
        </Button>
    </CardBody>
</Card>

<Modal @ref="ComplexModal" Size="ModalSize.ExtraLarge">
    <ModalContent Centered="true" Size="ModalSize.ExtraLarge">
        <ModalHeader>
            <ModalTitle>
                Complex Modal Demo
            </ModalTitle>
            <CloseButton Clicked="@(() => ComplexModal.Hide())" />
        </ModalHeader>
        <ModalBody>
            <Row>
                <!-- Left section (75%) - Currency Exchange Rates Grid -->
                <Column ColumnSize="ColumnSize.Is9">
                    <Card>
                        <CardHeader>
                            <CardTitle>Currency Exchange Rates</CardTitle>
                        </CardHeader>
                        <CardBody>
                            <DataGrid TItem="CurrencyExchangeRateDto" Data="@CurrencyRates" Responsive Striped Bordered>
                                <DataGridColumns>
                                    <DataGridColumn TItem="CurrencyExchangeRateDto"
                                        Field="@nameof(CurrencyExchangeRateDto.CurrencyPair)" Caption="Currency Pair" />
                                    <DataGridColumn TItem="CurrencyExchangeRateDto"
                                        Field="@nameof(CurrencyExchangeRateDto.CashBid)" Caption="Cash Bid">
                                        <DisplayTemplate>
                                            @context.CashBid.ToString("F4")
                                        </DisplayTemplate>
                                    </DataGridColumn>
                                    <DataGridColumn TItem="CurrencyExchangeRateDto"
                                        Field="@nameof(CurrencyExchangeRateDto.AccountBid)" Caption="Account Bid">
                                        <DisplayTemplate>
                                            @context.AccountBid.ToString("F4")
                                        </DisplayTemplate>
                                    </DataGridColumn>
                                    <DataGridColumn TItem="CurrencyExchangeRateDto"
                                        Field="@nameof(CurrencyExchangeRateDto.CashAsk)" Caption="Cash Ask">
                                        <DisplayTemplate>
                                            @context.CashAsk.ToString("F4")
                                        </DisplayTemplate>
                                    </DataGridColumn>
                                    <DataGridColumn TItem="CurrencyExchangeRateDto"
                                        Field="@nameof(CurrencyExchangeRateDto.AccountAsk)" Caption="Account Ask">
                                        <DisplayTemplate>
                                            @context.AccountAsk.ToString("F4")
                                        </DisplayTemplate>
                                    </DataGridColumn>
                                </DataGridColumns>
                            </DataGrid>
                        </CardBody>
                    </Card>
                </Column>

                <!-- Right section (25%) - Selectable Options -->
                <Column ColumnSize="ColumnSize.Is3">
                    <Card>
                        <CardHeader>
                            <CardTitle>Options</CardTitle>
                            <div class="d-flex gap-2">
                                <Button Color="Color.Primary" Size="Size.Small" Clicked="SelectAllOptions">Select
                                    All</Button>
                                <Button Color="Color.Secondary" Size="Size.Small" Clicked="DeselectAllOptions">Deselect
                                    All</Button>
                            </div>
                        </CardHeader>
                        <CardBody>
                            <Blazorise.Components.ListView TextField="(item)=>item.Label" ValueField="(item)=>item.Id" TItem="SelectableOptionDto"
                                Data="@Options" SelectedItem="@SelectedOption" SelectedItemChanged="@OnOptionSelected"
                                Mode="ListGroupMode.Selectable">
                                <ItemTemplate>
                                    <div class="d-flex align-items-center">
                                        <Check TValue="bool" Checked="@context.Item.IsSelected"
                                            CheckedChanged="@((value) => OptionSelectionChanged(context.Item, value))"
                                            Class="me-2">
                                        </Check>
                                        <span
                                            class="@(context.Item == SelectedOption ? "fw-bold" : "")">@context.Item.Label</span>
                                    </div>
                                </ItemTemplate>
                            </Blazorise.Components.ListView>
                        </CardBody>
                    </Card>
                </Column>
            </Row>
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Primary" Clicked="@(() => ComplexModal.Hide())">Apply</Button>
            <Button Color="Color.Secondary" Clicked="@(() => ComplexModal.Hide())">Cancel</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

@code {
    private Modal ComplexModal { get; set; } = new();
    private List<CurrencyExchangeRateDto> CurrencyRates { get; set; } = new();
    private List<SelectableOptionDto> Options { get; set; } = new();
    private SelectableOptionDto? SelectedOption { get; set; }

    protected override void OnInitialized()
    {
        // Load dummy data for presentation
        CurrencyRates = DummyDataGenerator.GenerateDummyCurrencyRates();
        Options = DummyDataGenerator.GenerateDummyOptions();

        base.OnInitialized();
    }

    private void OpenComplexModal()
    {
        ComplexModal.Show();
    }

    private void OptionSelectionChanged(SelectableOptionDto option, bool value)
    {
        option.IsSelected = value;

        // This is where you would call your backend code
        Console.WriteLine($"Option {option.Label} selection changed to {value}");
    }

    private void SelectAllOptions()
    {
        foreach (var option in Options)
        {
            option.IsSelected = true;
        }
    }

    private void DeselectAllOptions()
    {
        foreach (var option in Options)
        {
            option.IsSelected = false;
        }
    }

    private void OnOptionSelected(SelectableOptionDto option)
    {
        SelectedOption = option;
        Console.WriteLine($"Selected option: {option.Label}");
    }
}
