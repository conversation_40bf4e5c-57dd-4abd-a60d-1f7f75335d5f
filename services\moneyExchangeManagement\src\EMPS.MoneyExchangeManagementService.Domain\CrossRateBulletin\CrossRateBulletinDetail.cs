using System;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin
{
    public class CrossRateBulletinDetail : FullAuditedAggregateRoot<Guid>
    {
        public Guid CrossRateBulletinMasterId { get; set; }

        public string BaseCurrencyCode { get; set; }
        public string QuoteCurrencyCode { get; set; }
        public virtual string PairingFormat { get; set; }

        public double CrossRateValue { get; set; }

        public int DisplayOrder { get; set; }
    }
}