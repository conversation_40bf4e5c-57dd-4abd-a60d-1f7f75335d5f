using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.Shared;
using Microsoft.JSInterop;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService
{
    public partial class PairingRules
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar {get;} = new PageToolbar();
        private IReadOnlyList<PairingRuleDto> PairingRuleList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreatePairingRule { get; set; }
        private bool CanEditPairingRule { get; set; }
        private bool CanDeletePairingRule { get; set; }
        private bool CanApprovePairingRule { get; set; }
        private PairingRuleCreateDto NewPairingRule { get; set; }
        private Validations NewPairingRuleValidations { get; set; } = new();
        private PairingRuleUpdateDto EditingPairingRule { get; set; }
        private Validations EditingPairingRuleValidations { get; set; } = new();
        private Guid EditingPairingRuleId { get; set; }
        private Modal CreatePairingRuleModal { get; set; } = new();
        private Modal EditPairingRuleModal { get; set; } = new();
        private GetPairingRulesInput Filter { get; set; }
        private DataGridEntityActionsColumn<PairingRuleDto> EntityActionsColumn { get; set; } = new();
        protected string SelectedCreateTab = "pairingRule-create-tab";
        protected string SelectedEditTab = "pairingRule-edit-tab";

        private bool FilterCollapse = true;

        [Inject]
        public IJSRuntime JsRuntime { get; set; }

        private enum ArchiveFilter
        {
            All,
            Archived,
            UnArchive
        }
        private ArchiveFilter RadioArchiveCheckedValue = ArchiveFilter.UnArchive;
        private async Task OnRadioArchiveCheckedValueChanged(ArchiveFilter value)
        {
            RadioArchiveCheckedValue = value;
            await Console.Out.WriteLineAsync("RadioArchiveCheckedValue " + RadioArchiveCheckedValue);
            await GetPairingRulesAsync();
            StateHasChanged();

        }
        public PairingRules()
        {
            NewPairingRule = new PairingRuleCreateDto();
            EditingPairingRule = new PairingRuleUpdateDto();
            Filter = new GetPairingRulesInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            PairingRuleList = new List<PairingRuleDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:PairingRules"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["ExportToExcel"], async () =>{ await DownloadAsExcelAsync(); }, IconName.Download);
            
            Toolbar.AddButton(L["NewPairingRule"], async () =>
            {
                await OpenCreatePairingRuleModalAsync();
            }, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.PairingRules.Create);

            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreatePairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Create);
            CanEditPairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Edit);
            CanDeletePairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Delete);
            CanApprovePairingRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRules.Approve);
        }

        private async Task GetPairingRulesAsync()
        {

            bool? ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.All) ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.Archived) ArchivedValue = true;
            if (RadioArchiveCheckedValue == ArchiveFilter.UnArchive) ArchivedValue = false;

            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;
            Filter.IsArchived = ArchivedValue;

            var result = await PairingRulesAppService.GetListAsync(Filter);
            PairingRuleList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetPairingRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private  async Task DownloadAsExcelAsync()
        {
            var token = (await PairingRulesAppService.GetDownloadTokenAsync()).Token;
            var remoteService = await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("MoneyExchangeManagementService") ??
            await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("Default");
            NavigationManager.NavigateTo($"{remoteService?.BaseUrl.EnsureEndsWith('/') ?? string.Empty}api/money-exchange-management-service/pairing-rules/as-excel-file?DownloadToken={token}&FilterText={Filter.FilterText}", forceLoad: true);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<PairingRuleDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetPairingRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreatePairingRuleModalAsync()
        {
            NewPairingRule = new PairingRuleCreateDto{
                EffectiveDate = DateTime.Now,
            };
            await NewPairingRuleValidations.ClearAll();
            await CreatePairingRuleModal.Show();
        }

        private async Task CloseCreatePairingRuleModalAsync()
        {
            NewPairingRule = new PairingRuleCreateDto{
                EffectiveDate = DateTime.Now, 
            };
            await CreatePairingRuleModal.Hide();
        }

        private async Task OpenEditPairingRuleModalAsync(PairingRuleDto input)
        {
            var pairingRule = await PairingRulesAppService.GetAsync(input.Id);
            
            EditingPairingRuleId = pairingRule.Id;
            EditingPairingRule = ObjectMapper.Map<PairingRuleDto, PairingRuleUpdateDto>(pairingRule);
            await EditingPairingRuleValidations.ClearAll();
            await EditPairingRuleModal.Show();
        }

        private async Task DeletePairingRuleAsync(PairingRuleDto input)
        {
            await PairingRulesAppService.DeleteAsync(input.Id);
            await GetPairingRulesAsync();
        }

        private async Task CreatePairingRuleAsync()
        {
            try
            {
                if (await NewPairingRuleValidations.ValidateAll() == false)
                {
                    return;
                }

                await PairingRulesAppService.CreateAsync(NewPairingRule);
                await GetPairingRulesAsync();
                await CloseCreatePairingRuleModalAsync();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditPairingRuleModalAsync()
        {
            await EditPairingRuleModal.Hide();
        }

        private async Task UpdatePairingRuleAsync()
        {
            try
            {
                if (await EditingPairingRuleValidations.ValidateAll() == false)
                {
                    return;
                }

                await PairingRulesAppService.UpdateAsync(EditingPairingRuleId, EditingPairingRule);
                await GetPairingRulesAsync();
                await EditPairingRuleModal.Hide();                
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private void OnSelectedCreateTabChanged(string name)
        {
            SelectedCreateTab = name;
        }

        private void OnSelectedEditTabChanged(string name)
        {
            SelectedEditTab = name;
        }

        private async Task ApprovePairingRuleAsync(Guid RuleId)
        {
            await PairingRulesAppService.SetApproveAsync(RuleId);

            await GetPairingRulesAsync();
        }
        private async Task ArchivePairingRuleAsync(Guid RuleId)
        {
            await PairingRulesAppService.SetArchiveAsync(RuleId);

            await GetPairingRulesAsync();
        }
        private async Task UnArchivePairingRuleAsync(Guid RuleId)
        {
            await PairingRulesAppService.SetUnArchiveAsync(RuleId);

            await GetPairingRulesAsync();
        }
        private async Task DuplicatePairingRuleAsync(Guid RuleId)
        {
            await PairingRulesAppService.DuplicateRuleAsync(RuleId);

            await GetPairingRulesAsync();
        }

        private async Task NavigateToDetailsAsync(PairingRuleDto input)
        {
            string url = $"/PairingRuleDetails/{input.Id}";
            await JsRuntime.InvokeVoidAsync("open", url, "_blank");
            await Task.CompletedTask;
        }

    }
}
