using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public interface IPairingRulesAppService : IApplicationService
    {
        Task<PagedResultDto<PairingRuleDto>> GetListAsync(GetPairingRulesInput input);

        Task<PairingRuleDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<PairingRuleDto> CreateAsync(PairingRuleCreateDto input);

        Task<PairingRuleDto> UpdateAsync(Guid id, PairingRuleUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleExcelDownloadDto input);

        Task<PairingWithDetailes> GetLastEffectiveRule();

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();

        Task SetArchiveAsync(Guid id);

        Task SetUnArchiveAsync(Guid id);

        Task SetApproveAsync(Guid id);

        Task DuplicateRuleAsync(Guid id);

    }
}