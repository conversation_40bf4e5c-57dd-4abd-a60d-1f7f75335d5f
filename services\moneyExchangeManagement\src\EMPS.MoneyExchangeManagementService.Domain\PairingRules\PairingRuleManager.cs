using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public class PairingRuleManager : DomainService
    {
        private readonly IPairingRuleRepository _pairingRuleRepository;

        public PairingRuleManager(IPairingRuleRepository pairingRuleRepository)
        {
            _pairingRuleRepository = pairingRuleRepository;
        }

        public async Task<PairingRule> CreateAsync(
        string name, string description, bool isApproved, string approvedByName, bool isArchived, DateTime? effectiveDate = null, Guid? approvedBy = null, DateTime? approvalDateTime = null)
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var pairingRule = new PairingRule(
             GuidGenerator.Create(),
             name, description, isApproved, approvedByName, isArchived, effectiveDate, approvedBy, approvalDateTime
             );

            return await _pairingRuleRepository.InsertAsync(pairingRule);
        }

        public async Task<PairingRule> UpdateAsync(
            Guid id,
            string name, string description, bool isApproved, string approvedByName, bool isArchived, DateTime? effectiveDate = null, Guid? approvedBy = null, DateTime? approvalDateTime = null, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));

            var pairingRule = await _pairingRuleRepository.GetAsync(id);

            pairingRule.Name = name;
            pairingRule.Description = description;
            pairingRule.IsApproved = isApproved;
            pairingRule.ApprovedByName = approvedByName;
            pairingRule.IsArchived = isArchived;
            pairingRule.EffectiveDate = effectiveDate;
            pairingRule.ApprovedBy = approvedBy;
            pairingRule.ApprovalDateTime = approvalDateTime;

            pairingRule.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _pairingRuleRepository.UpdateAsync(pairingRule);
        }

    }
}