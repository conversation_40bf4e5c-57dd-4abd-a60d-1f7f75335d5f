@page "/PairingRules"



@attribute [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Default)]
@using EMPS.MoneyExchangeManagementService.PairingRules
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject IPairingRulesAppService PairingRulesAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager

@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["PairingRules"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="PairingRuleSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText"
                              Autofocus="true"
                              Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="PairingRuleSearchForm" Clicked="GetPairingRulesAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
            <Accordion>
                <Collapse Visible="@FilterCollapse">
                    <CollapseHeader>
                        <Heading Size="HeadingSize.Is5">
                            <AccordionToggle>@L["Filter"]</AccordionToggle>
                        </Heading>
                    </CollapseHeader>
                    <CollapseBody>
                        <Div>
                            <RadioGroup TValue="ArchiveFilter" Name="ArchiveStatus" CheckedValue="@RadioArchiveCheckedValue"
                                        CheckedValueChanged="@OnRadioArchiveCheckedValueChanged">

                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.All">@L["All"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.Archived">@L["Archived"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.UnArchive">@L["UnArchived"]</Radio>
                            </RadioGroup>
                        </Div>
                    </CollapseBody>
                </Collapse>

            </Accordion>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="PairingRuleDto"
                  Data="PairingRuleList"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  Responsive="true"
                  PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="PairingRuleDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="PairingRuleDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="PairingRuleDto"
                                          Visible="@CanEditPairingRule"
                                          Clicked="async () => await OpenEditPairingRuleModalAsync(context)"
                                          Text="@L["Edit"]"></EntityAction>
                            <EntityAction TItem="PairingRuleDto"
                                          Visible="@(CanDeletePairingRule && !context.IsApproved)"
                                          Clicked="() => DeletePairingRuleAsync(context)"
                                          ConfirmationMessage="@(()=> L["DeleteConfirmationMessage"])"
                                          Text="@L["Delete"]"></EntityAction>

                            @if (!context.IsApproved)
                            {
                                <EntityAction TItem="PairingRuleDto"
                                              Visible="@CanApprovePairingRule"
                                              Clicked="() =>ApprovePairingRuleAsync(context.Id)"
                                              ConfirmationMessage="@(()=> L["ApproveConfirmationMessage"])"
                                              Text="@L["Approve"]"></EntityAction>
                            }
                            @if (!context.IsArchived && context.IsApproved)
                            {
                                <EntityAction TItem="PairingRuleDto"
                                              Visible="@CanEditPairingRule"
                                              Clicked="() =>ArchivePairingRuleAsync(context.Id)"
                                              ConfirmationMessage="@(()=> L["ArchiveConfirmationMessage"])"
                                              Text="@L["Archive"]"></EntityAction>
                            }
                            @if (context.IsArchived && context.IsApproved)
                            {
                                <EntityAction TItem="PairingRuleDto"
                                              Visible="@CanEditPairingRule"
                                              Clicked="() =>UnArchivePairingRuleAsync(context.Id)"
                                              ConfirmationMessage="@(()=> L["UnArchiveConfirmationMessage"])"
                                              Text="@L["UnArchive"]"></EntityAction>
                            }
                            <EntityAction TItem="PairingRuleDto"
                                          Visible="@CanCreatePairingRule"
                                          Clicked="async() =>await DuplicatePairingRuleAsync(context.Id)"
                                          ConfirmationMessage="@(()=> L["DuplicateConfirmationMessage"])"
                                          Text="@L["Duplicate"]"></EntityAction>
                            <EntityAction TItem="PairingRuleDto"
                                          Clicked="()=>NavigateToDetailsAsync(context)"
                                          Text="@L["Details"]"></EntityAction>


                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="Name"
                                Caption="@L["Name"]">
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="EffectiveDate"
                                Caption="@L["EffectiveDate"]">
                    <DisplayTemplate>
                        @(context.EffectiveDate.HasValue ? context.EffectiveDate.Value.ToShortDateString() : string.Empty)
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="ApprovedByName"
                                Caption="@L["ApprovedByName"]">
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="ApprovalDateTime"
                                Caption="@L["ApprovalDateTime"]">
                    <DisplayTemplate>
                        @(context.ApprovalDateTime.HasValue ? context.ApprovalDateTime.Value.ToShortDateString() : string.Empty)
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="IsApproved"
                                Caption="@L["IsApproved"]">
                    <DisplayTemplate>
                        @if (context.IsApproved)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="IsArchived"
                                Caption="@L["IsArchived"]">
                    <DisplayTemplate>
                        @if (context.IsArchived)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="PairingRuleDto"
                                Field="Description"
                                Caption="@L["Description"]">
                </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreatePairingRuleModal" Closing="@CreatePairingRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreatePairingRuleForm">
            <ModalHeader>
                <ModalTitle>@L["NewPairingRule"]</ModalTitle>
                <CloseButton Clicked="CloseCreatePairingRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@NewPairingRuleValidations"
                             Mode="ValidationMode.Auto"
                             Model="@NewPairingRule"
                             
                             ValidateOnLoad="false">


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Name"] *</FieldLabel>
                            <TextEdit @bind-Text="@NewPairingRule.Name">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["EffectiveDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewPairingRule.EffectiveDate">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <TextEdit @bind-Text="@NewPairingRule.Description">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>

                    @*
                    <Field>
                    <Check TValue="bool" @bind-Checked="@NewPairingRule.IsApproved">@L["IsApproved"]</Check>
                    </Field>

                    <Validation>
                    <Field>
                    <FieldLabel>@L["ApprovedByName"]</FieldLabel>
                    <TextEdit @bind-Text="@NewPairingRule.ApprovedByName">
                    <Feedback>
                    <ValidationError />
                    </Feedback>
                    </TextEdit>
                    </Field>
                    </Validation>

                    <Validation>
                    <Field>
                    <FieldLabel>@L["ApprovalDateTime"]</FieldLabel>
                    <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewPairingRule.ApprovalDateTime">
                    <Feedback>
                    <ValidationError />
                    </Feedback>
                    </DateEdit>
                    </Field>
                    </Validation>

                    <Field>
                    <Check TValue="bool" @bind-Checked="@NewPairingRule.IsArchived">@L["IsArchived"]</Check>
                    </Field>
                    *@

                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseCreatePairingRuleModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreatePairingRuleForm" Clicked="CreatePairingRuleAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditPairingRuleModal" Closing="@EditPairingRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditPairingRuleForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditPairingRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingPairingRuleValidations"
                             Mode="ValidationMode.Auto"
                             Model="@EditingPairingRule"
                             ValidateOnLoad="false">


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Name"] *</FieldLabel>
                            <TextEdit @bind-Text="@EditingPairingRule.Name">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["EffectiveDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingPairingRule.EffectiveDate">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingPairingRule.Description">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    @*   <Field>
                    <Check TValue="bool" @bind-Checked="@EditingPairingRule.IsApproved">@L["IsApproved"]</Check>
                    </Field>



                    <Validation>
                    <Field>
                    <FieldLabel>@L["ApprovedByName"]</FieldLabel>
                    <TextEdit @bind-Text="@EditingPairingRule.ApprovedByName">
                    <Feedback>
                    <ValidationError />
                    </Feedback>
                    </TextEdit>
                    </Field>
                    </Validation>


                    <Validation>
                    <Field>
                    <FieldLabel>@L["ApprovalDateTime"]</FieldLabel>
                    <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingPairingRule.ApprovalDateTime">
                    <Feedback>
                    <ValidationError />
                    </Feedback>
                    </DateEdit>
                    </Field>
                    </Validation>


                    <Field>
                    <Check TValue="bool" @bind-Checked="@EditingPairingRule.IsArchived">@L["IsArchived"]</Check>
                    </Field>

                    *@

                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseEditPairingRuleModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreatePairingRuleForm" Clicked="UpdatePairingRuleAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
