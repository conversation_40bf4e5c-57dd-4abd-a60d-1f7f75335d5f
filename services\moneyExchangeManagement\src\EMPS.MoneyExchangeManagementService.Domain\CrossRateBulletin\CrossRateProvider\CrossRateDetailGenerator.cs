using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.PairingRules;
using Org.BouncyCastle.Math.EC.Rfc7748;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// Service to generate CrossRateBulletinDetail records from a CrossLimitProvider
    /// </summary>
    public class CrossRateDetailGenerator : DomainService
    {
        private readonly ICrossLimitProvider _crossLimitProvider;
        private readonly IRepository<CrossRateBulletinDetail, Guid> _detailRepository;
        private readonly IRepository<CrossRateProviderConfiguration, Guid> _configurationRepository;
        private readonly IPairingRuleRepository _pairingRuleRepository;
        private readonly IPairingRuleDetailRepository _pairingRuleDetailRepository;

        public CrossRateDetailGenerator(
            ICrossLimitProvider crossLimitProvider,
            IRepository<CrossRateBulletinDetail, Guid> detailRepository,
            IRepository<CrossRateProviderConfiguration, Guid> configurationRepository,
            IPairingRuleRepository pairingRuleRepository,
            IPairingRuleDetailRepository pairingRuleDetailRepository)
        {
            _crossLimitProvider = crossLimitProvider;
            _detailRepository = detailRepository;
            _configurationRepository = configurationRepository;
            _pairingRuleRepository = pairingRuleRepository;
            _pairingRuleDetailRepository = pairingRuleDetailRepository;
        }

        public async Task<List<CrossRateBulletinDetail>> RegenerateDetailsForMasterAsync(Guid masterId, List<string> currencies)
        {
            var query = await _detailRepository.GetQueryableAsync();
            query = query.Where(x => x.CrossRateBulletinMasterId == masterId);
            var usdSypRecordCrossRateValue =
                query.Where(x => (x.QuoteCurrencyCode == PairingRuleConsts.UsdCode && x.BaseCurrencyCode == PairingRuleConsts.SypCode) || (x.QuoteCurrencyCode == PairingRuleConsts.SypCode && x.BaseCurrencyCode == PairingRuleConsts.UsdCode))
                .FirstOrDefault().CrossRateValue;
            await _detailRepository.DeleteManyAsync(query);
            return await GenerateDetailsForMasterAsync(masterId, currencies, usdSypRecordCrossRateValue);
        }

        /// <summary>
        /// Generates CrossRateBulletinDetail records for a given master bulletin
        /// </summary>
        /// <param name="masterId">The ID of the CrossRateBulletinMaster</param>
        /// <returns>A list of the generated CrossRateBulletinDetail entities</returns>
        public async Task<List<CrossRateBulletinDetail>> GenerateDetailsForMasterAsync(Guid masterId, List<string> currencies, double sypUsdCrossRateValue)
        {
            // Get configuration from database
            var configuration = await _configurationRepository.FirstOrDefaultAsync();

            // Validate configuration exists
            if (configuration == null ||
                string.IsNullOrWhiteSpace(configuration.ProviderBaseUrl) ||
                string.IsNullOrWhiteSpace(configuration.ProviderAccessToken) ||
                string.IsNullOrWhiteSpace(configuration.RequestBaseCurrency))
            {
                throw new UserFriendlyException(
                    "Cross rate provider is not configured. Please contact the administration to configure the exchange rate provider settings.");
            }

            // Get cross rate data from the provider
            var request = new CrossLimitProviderRequest
            {
                AccessToken = configuration.ProviderAccessToken,
                ProviderBaseUrl = configuration.ProviderBaseUrl,
                RequestBaseCurrency = configuration.RequestBaseCurrency,
                RequestedCurrencies = currencies
            };

            var crossLimits = await _crossLimitProvider.GetCrossLimitsAsync(request);
            var details = new List<CrossRateBulletinDetail>();

            var usdSypPairingRule = await GetLastApprovedUsdSypPairingRule();
            details.Add(CreateDetailEntity(masterId, usdSypPairingRule.BaseCode, usdSypPairingRule.QuoteCode, usdSypPairingRule.PairingFormat, sypUsdCrossRateValue, 0));

            // Convert provider responses to CrossRateBulletinDetail entities
            var crossLimitsList = crossLimits.ToList();
            for (int i = 0; i < crossLimitsList.Count; i++)
            {
                var crossLimit = crossLimitsList[i];
                var detail = CreateDetailEntity(masterId, crossLimit.SourceCurrencyCode, crossLimit.QuoteCurrencyCode, $"{crossLimit.SourceCurrencyCode}/{crossLimit.QuoteCurrencyCode}", crossLimit.CrossRateValue, i + 1);
                details.Add(detail);
            }

            await _detailRepository.InsertManyAsync(details);

            return details;
        }

        private CrossRateBulletinDetail CreateDetailEntity(Guid crossRateMasterId, string baseCurrencyCode, string quoteCurrencyCode, string pairingFormat, double crossRateValue, int displayOrder)
        {
            return new CrossRateBulletinDetail
            {
                CrossRateBulletinMasterId = crossRateMasterId,
                BaseCurrencyCode = baseCurrencyCode,
                QuoteCurrencyCode = quoteCurrencyCode,
                CrossRateValue = crossRateValue,
                PairingFormat = pairingFormat,
                DisplayOrder = displayOrder
            };
        }


        public async Task<PairingRuleDetail?> GetLastApprovedUsdSypPairingRule()
        {
            var lastEffectiveRule = (await _pairingRuleRepository.GetListAsync(isApproved: true,
                sorting: "EffectiveDate DESC",
                maxResultCount: 1)).First();

            var query = await _pairingRuleDetailRepository.GetQueryableAsync();
            var usdSypPairingRule = query
            .Where(x => x.PairingRuleId == lastEffectiveRule.Id)
            .Where(x => (x.QuoteCode == PairingRuleConsts.UsdCode && x.BaseCode == PairingRuleConsts.SypCode) || (x.QuoteCode == PairingRuleConsts.SypCode && x.BaseCode == PairingRuleConsts.UsdCode))
            .FirstOrDefault();

            return usdSypPairingRule;


        }
    }
}
