{"App": {"SelfUrl": "https://localhost:45244", "CorsOrigins": "https://localhost:44325,https://localhost:44353"}, "AuthServer": {"Authority": "https://localhost:44322", "RequireHttpsMetadata": "true", "SwaggerClientId": "WebGateway_Swagger"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "RemoteServices": {"Default": {"BaseUrl": "https://localhost:44325", "UseCurrentAccessToken": "false"}}, "AllowedHosts": "*", "ConnectionStrings": {"MoneyExchangeManagementService": "Server=.;Database=EMPS_MoneyExchangeManagementService;User Id=sa;password=********;MultipleActiveResultSets=true;TrustServerCertificate=True", "AdministrationService": "Server=.;Database=EMPS_Administration;User Id=sa;password=********;MultipleActiveResultSets=true;TrustServerCertificate=True", "SaasService": "Server=.;Database=EMPS_Saas;User Id=sa;password=********;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "StringEncryption": {"DefaultPassPhrase": "YyNlXpNdqYQEcU5E"}, "Redis": {"Configuration": "localhost:6379"}, "RabbitMQ": {"Connections": {"Default": {"HostName": "localhost"}}, "EventBus": {"ClientName": "EMPS_MoneyExchangeManagementService", "ExchangeName": "EMPS"}}, "ElasticSearch": {"Url": "http://localhost:9200"}, "IdentityClients": {"Default": {"GrantType": "client_credentials", "ClientId": "MoneyExchangeManagementService", "ClientSecret": "1q2w3e*", "Authority": "https://localhost:44322", "Scope": "IdentityService CompanyService"}}}