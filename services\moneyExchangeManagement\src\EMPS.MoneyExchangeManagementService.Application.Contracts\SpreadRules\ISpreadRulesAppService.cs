using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public interface ISpreadRulesAppService : IApplicationService
    {
        Task<SpreadRuleDto> GetWithDetailsAsync(Guid id, SpreadRuleType type);
        Task<PagedResultDto<SpreadRuleDto>> GetListAsync(GetSpreadRulesInput input);

        Task<SpreadRuleDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<SpreadRuleDto> CreateAsync(SpreadRuleCreateDto input);

        Task<SpreadRuleDto> UpdateAsync(Guid id, SpreadRuleUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleExcelDownloadDto input);

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();
        Task<SpreadRuleDto> ApproveAsync(Guid id, SpreadRuleUpdateDto input);
        Task<SpreadRuleDto> ArchiveAsync(Guid id, SpreadRuleUpdateDto input);
        Task<SpreadRuleDto> UnArchiveAsync(Guid id, SpreadRuleUpdateDto input);
        Task<SpreadRuleDto> CreateDuplicateAsync(SpreadRuleDto input);

        Task<List<SpreadRuleDetailWithNavigationPropertiesDto>> GetSpreadRulesWithDetail(bool isGlobal, List<Guid?> spreadRuleIds);
    }
}