using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailUpdateDto : IHasConcurrencyStamp
    {
        public string? CurrencyName { get; set; }
        public Guid CurrencyID { get; set; }
        public bool AllowedToBuy { get; set; }
        public double MinAmountToBuy { get; set; }
        public double MaxAmountToBuy { get; set; }
        public double MaxDailyAmountToBuy { get; set; }
        public bool AllowedToSell { get; set; }
        public double MinAmountToSell { get; set; }
        public double MaxAmountToSell { get; set; }
        public double MaxDailyAmountToSell { get; set; }
        public bool AllowedToSellBelowCenterCost { get; set; }
        public bool AllowedToSellBelowCompanyCost { get; set; }
        public Guid ExchangeRuleMasterID { get; set; }
        public ExchangeRuleDetailsType ExchangeRuleDetailType { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}