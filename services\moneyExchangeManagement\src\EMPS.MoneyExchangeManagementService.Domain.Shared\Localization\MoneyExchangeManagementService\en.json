{"culture": "en", "texts": {"CrossRateBulletinMaster": "Cross Rate Bulletin Master", "CreateNew": "Create New", "ApprovedByUserName": "Approved By User Name", "Edit": "Edit", "Delete": "Delete", "EditCrossRateBulletinMaster": "Edit Cross Rate Bulletin Master", "CreateCrossRateBulletinMaster": "Create Cross Rate Bulletin Master", "Save": "Save", "Cancel": "Cancel", "Close": "Close", "Menu:CrossRateBulletinMaster": "Cross Rate Bulletin Master", "Approve": "Approve", "Archive": "Archive", "Unarchive": "Unarchive", "CrossRateBulletinDetail": "Cross Rate Bulletin Detail", "BaseCurrency": "Base Currency", "QuoteCurrency": "Quote <PERSON><PERSON>", "CrossRateValue": "Cross Rate Value", "ViewDetails": "View Details", "CustomModalDemo": "Custom Modal Demo", "CrossRateProviderConfiguration": "Cross Rate Provider Configuration", "ProviderBaseUrl": "Provider Base URL", "ProviderAccessToken": "Provider Access Token", "RequestBaseCurrency": "Request Base Currency", "ConfigurationSavedSuccessfully": "Configuration saved successfully", "Permission:CrossRateProviderConfiguration": "Cross Rate Provider Configuration", "Permission:Manage": "Manage", "Error:CrossRateProviderNotConfigured": "Cross rate provider is not configured. Please contact the administration to configure the exchange rate provider settings.", "Error:CrossRateProviderBaseUrlNotConfigured": "Cross rate provider base URL is not configured. Please contact the administration to configure the exchange rate provider settings.", "Error:CrossRateProviderAccessTokenNotConfigured": "Cross rate provider access token is not configured. Please contact the administration to configure the exchange rate provider settings.", "Error:CrossRateProviderBaseCurrencyNotConfigured": "Cross rate provider base currency is not configured. Please contact the administration to configure the exchange rate provider settings.", "Permission:PairingRules": "Pairing Rules", "Permission:MoneyExchangeManagementService": "Money Exchange Management Service", "Permission:Create": "Create", "Permission:Edit": "Edit", "Permission:Delete": "Delete", "Name": "Name", "EffectiveDate": "Effective Date", "Description": "Description", "IsApproved": "Is Approved", "ApprovedBy": "Approved By", "ApprovedByName": "Approved By Name", "ApprovalDateTime": "Approval Date Time", "IsArchived": "Is Archived", "MinEffectiveDate": "Min Effective Date", "MinApprovalDateTime": "Min Approval Date Time", "MaxEffectiveDate": "Max Effective Date", "MaxApprovalDateTime": "Max Approval Date Time", "PairingRules": "Pairing Rules", "NewPairingRule": "New Pairing Rule", "Actions": "Actions", "SuccessfullyDeleted": "Successfully deleted", "DeleteConfirmationMessage": "Are you sure you want to delete this record?", "Search": "Search", "Pick": "Pick", "SeeAdvancedFilters": "Advanced filters", "ItemAlreadyAdded": "This item is already added.", "ExportToExcel": "Export to Excel", "Menu:PairingRules": "Pairing Rules", "Menu:MoneyExchangeManagementService": "Money Exchange Management Service", "PairingRule": "Pairing rule", "Permission:PairingRuleDetails": "Pairing Rule Details", "BaseId": "Base Id", "BaseCode": "Base Code", "QuoteId": "Quote Id", "QuoteCode": "Quote Code", "PairingFormat": "Pairing Format", "DisplayOrder": "Display Order", "MinDisplayOrder": "Min Display Order", "MaxDisplayOrder": "Max Display Order", "PairingRuleDetails": "Pairing Rule Details", "NewPairingRuleDetail": "New Pairing Rule Detail", "Menu:PairingRuleDetails": "Pairing Rule Details", "Permission:BulletinManagementMasters": "Bulletin Management Masters", "BulletinNumber": "Bulletin Number", "BulletinName": "Bulletin Name", "BulletinDate": "Bulletin Date", "Notes": "Notes", "MinBulletinDate": "Min Bulletin Date", "MaxBulletinDate": "Max Bulletin Date", "BulletinManagementMasters": "Bulletin Management Masters", "NewBulletinManagementMaster": "New Bulletin Management Master", "Menu:BulletinManagementMasters": "Bulletin Management Masters", "ServicePointName": "Service Point Name", "ServicePointId": "Service Point Id", "CurrencyPairingRuleName": "Currency Pairing Rule Name", "CurrencyPairingRuleId": "Currency Pairing Rule Id", "CrossRateBulletinName": "Cross Rate Bulletin Name", "CrossRateBulletinId": "Cross Rate Bulletin Id", "SpreadRuleName": "Spread Rule Name", "SpreadRuleId": "Spread Rule Id", "Permission:BulletinManagementDetails": "Bulletin Management Details", "CashBid": "Cash Bid", "CashAsk": "Cash Ask", "AccountBid": "Account Bid", "AccountAsk": "Account Ask", "CurrencyPair": "<PERSON><PERSON><PERSON><PERSON>", "MinCashBid": "<PERSON> Bid", "MinCashAsk": "<PERSON> Cash Ask", "MinAccountBid": "<PERSON> Account Bid", "MaxCashBid": "<PERSON> Bid", "MaxCashAsk": "Max Cash Ask", "MaxAccountBid": "Max Account Bid", "BulletinManagementDetails": "Bulletin Management Details", "NewBulletinManagementDetail": "New Bulletin Management Detail", "Menu:BulletinManagementDetails": "Bulletin Management Details", "MinAccountAsk": "Min Account Ask", "MaxAccountAsk": "Max Account Ask", "BulletinManagementMaster": "Bulletin management master", "PublishByUserName": "Publish By User Name", "PublishByUserId": "Publish By User Id", "PublishDate": "Publish Date", "MinPublishDate": "Min Publish Date", "MaxPublishDate": "Max Publish Date", "BulletinManagement": "Bulletin Management", "Menu:BulletinManagement": "Bulletin Management", "CurrentBulletinDate": "Current Bulletin Date", "BulletinDetails": "Bulletin Details", "ServicePoints": "Service Points", "GlobalBulletin": "Global Bulletin", "SpecificServicePoints": "Specific Service Points", "SelectAll": "Select All", "DeselectAll": "Deselect All", "FilterServicePoints": "Filter Service Points", "NewBulletin": "New Bulletin", "NewBulletinModalContent": "Create a new bulletin for the selected service points", "BulletinCreatedSuccessfully": "Bulletin created successfully", "BulletinPublishedSuccessfully": "Bulletin published successfully", "Publish": "Publish", "ShowDetail": "Show Detail", "Master": "Master", "Details": "Details", "Enum:SpreadRuleType.1": "OnCash", "Enum:SpreadRuleType.2": "OnAccount", "Enum:SpreadRuleScope.1": "Global", "Enum:SpreadRuleScope.2": "Custom", "SpreadRules": "Spread Rules", "Enum:ExchangeRulesScope.1": "global", "Enum:ExchangeRulesScope.2": "Service point", "Permission:ExchangeRuless": "Exchange Ruless", "Permission:Approve": "Approve", "Permission:Archive": "Archive", "Permission:UnArchive": "Un-Archive", "Permission:Duplicate": "Duplicate", "ActivationDate": "Activation Date", "ExchangeRuleScope": "Exchange Rule Scope", "MinActivationDate": "Min Activation Date", "MaxActivationDate": "Max Activation Date", "ExchangeRuless": "Exchange Ruless", "NewExchangeRules": "New Exchange Rules", "Menu:ExchangeRuless": "Exchange Ruless", "Enum:ExchangeRuleDetailsType.1": "In cash", "Enum:ExchangeRuleDetailsType.2": "In account", "Permission:ExchangeRuleDetails": "Exchange Rule Details", "CurrencyName": "Currency Name", "CurrencyID": "Currency I D", "AllowedToBuy": "Allowed To Buy", "MinAmountToBuy": "Min Amount To Buy", "MaxAmountToBuy": "Max Amount To Buy", "MaxDailyAmountToBuy": "Max Daily Amount To Buy", "AllowedToSell": "Allowed To Sell", "MinAmountToSell": "<PERSON> Amount To Sell", "MaxAmountToSell": "<PERSON> Amount To <PERSON>ll", "MaxDailyAmountToSell": "Max Daily Amount To Sell", "AllowedToSellBelowCenterCost": "Allowed To Sell Below Center Cost", "AllowedToSellBelowCompanyCost": "Allowed To Sell Below Company Cost", "ExchangeRuleMasterID": "Exchange Rule Master I D", "ExchangeRuleDetailType": "Exchange Rule Detail Type", "MinMinAmountToBuy": "<PERSON> Min Amount To Buy", "MinMaxAmountToBuy": "<PERSON> Amount To Buy", "MinMaxDailyAmountToBuy": "Min Max Daily Amount To Buy", "MinMinAmountToSell": "<PERSON> Amount To Sell", "MinMaxAmountToSell": "<PERSON> Amount To Sell", "MaxMinAmountToBuy": "<PERSON> Min Amount To Buy", "MaxMaxAmountToBuy": "Max Max Amount To Buy", "MaxMaxDailyAmountToBuy": "Max Max Daily Amount To Buy", "MaxMinAmountToSell": "<PERSON> Amount To Sell", "MaxMaxAmountToSell": "<PERSON> Amount To Sell", "ExchangeRuleDetails": "Exchange Rule Details", "NewExchangeRuleDetail": "New Exchange Rule Detail", "Menu:ExchangeRuleDetails": "Exchange Rule Details", "ApprovedByUserId": "Approved By User Id", "ApprovedDateTime": "Approved Date Time", "ArchivedByUserId": "Archived By User Id", "ArchivedByUserName": "Archived By User Name", "ArchivedDateTime": "Archived Date Time", "UnArchivedByUserId": "Un-Archived By User Id", "UnArchivedByUserName": "Un-Archived By User Name", "UnArchivedByDate": "Un-Archived By Date", "UnArchive": "Un-Archive", "Duplicate": "Duplicate", "ActivationDateMustBeInFuture": "Activation date must be in the future", "RuleMustBeApprovedBeforeArchiving": "Rule must be approved before archiving", "RuleMustBeApprovedBeforeUnarchiving": "Rule must be approved before unarchiving", "ExchangeRuleDuplicatedSuccessfully": "Exchange rule duplicated successfully", "DuplicateRuleName": "Duplicate of {0}", "CantDeleteApprovedRule": "Cannot delete approved rule", "CantEditApprovedExchangeRule": "Cannot edit approved exchange rule", "RuleNameAlreadyExist": "Rule name already exists", "Error trying to CreateAsync": "Error occurred while creating the rule", "Error:ExchangeRuleNotFound": "Exchange rule not found", "Error:UnexpectedErrorOccurred": "An unexpected error occurred", "Error:NoGlobalExchangeRule": "No global exchange rule found", "Error:ExchangeRuleDetailNotFound": "Exchange rule detail not found", "Menu:ExchangeRules": "Exchange Rules", "ExchangeRules": "Exchange Rules", "ApproveConfirmationMessage": "Are you sure you want to approve this exchange rule?", "ArchiveConfirmationMessage": "Are you sure you want to archive this Rule?", "UnArchiveConfirmationMessage": "Are you sure you want to unarchive this Rule?", "DuplicateConfirmationMessage": "Are you sure you want to duplicate this Rule?", "Filter": "Filter", "All": "All", "Active": "Active", "Archived": "Archived", "GeneralInfo": "General Info", "ExchangeRuleDetailTab": "Exchange Rule Details", "SaveExchangeRule": "Save Exchange Rule", "Menu:ApplyExchangeRule": "Apply Exchange Rule", "ApplyExchangeRule": "Apply Exchange Rule", "BackToExchangeRules": "Back to Exchange Rules", "ApplyToSelected": "Apply to Selected", "DisapplyFromSelected": "Remove from Selected", "CurrentExchangeRule": "Current Exchange Rule", "None": "None", "ExchangeRuleAppliedSuccessfully": "Exchange rule applied successfully", "ExchangeRuleDisappliedSuccessfully": "Exchange rule removed successfully", "Apply": "Apply", "ApplyExchangeRuleOnServicePoint": "Apply Exchange Rule on Service Point", "ArchivedByName": "Archived By Name", "BidAndAskNegative": "<PERSON><PERSON> and Ask Spread cannot both be negative.", "MustBeAbsBidLargerThanAsk": "|Bid Spread| must be ? Ask Spread.", "MustBeAbsAskLargerThanBid": "|Ask Spread| must be ? Bid Spread.", "DiscountAndMarkupMustBePositive": "Discount and markup values must be ? 0.", "BulletinNo": " Bulletin No ", "YouCantArchivespreadRuleNotApproved": "You can't archive the spread rule because it has not been approved.", "ActivationDateRequrid": "Activation Date Requrid !", "FetchData": "Fetch Data", "FetchedSuccessfully": "Fetched Successfully", "Refreshing": "Refreshing...", "UpdatedSuccessfully": "Updated Successfully"}}