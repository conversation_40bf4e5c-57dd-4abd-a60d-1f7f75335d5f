using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// Response model for CurrencyLayer API
    /// </summary>
    public class CurrencyLayerApiResponse
    {
        /// <summary>
        /// Indicates if the API request was successful
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// Terms URL from CurrencyLayer
        /// </summary>
        [JsonPropertyName("terms")]
        public string Terms { get; set; } = string.Empty;

        /// <summary>
        /// Privacy URL from CurrencyLayer
        /// </summary>
        [JsonPropertyName("privacy")]
        public string Privacy { get; set; } = string.Empty;

        /// <summary>
        /// Unix timestamp of the exchange rates
        /// </summary>
        [JsonPropertyName("timestamp")]
        public long Timestamp { get; set; }

        /// <summary>
        /// Source currency (base currency)
        /// </summary>
        [JsonPropertyName("source")]
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// Currency quotes - can be either object {} or empty array []
        /// </summary>
        [JsonPropertyName("quotes")]
        [JsonConverter(typeof(QuotesJsonConverter))]
        public List<CurrencyQuote> Quotes { get; set; } = new();

        /// <summary>
        /// Error information when success is false
        /// </summary>
        [JsonPropertyName("error")]
        public CurrencyLayerError? Error { get; set; }
    }

    /// <summary>
    /// Represents a single currency quote
    /// </summary>
    public class CurrencyQuote
    {
        /// <summary>
        /// Currency pair key (e.g., "USDEUR")
        /// </summary>
        public string CurrencyPair { get; set; } = string.Empty;

        /// <summary>
        /// Exchange rate value
        /// </summary>
        public double Rate { get; set; }
    }

    /// <summary>
    /// Error information from CurrencyLayer API
    /// </summary>
    public class CurrencyLayerError
    {
        /// <summary>
        /// Error code
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// Error type
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Error information/message
        /// </summary>
        [JsonPropertyName("info")]
        public string Info { get; set; } = string.Empty;
    }

    /// <summary>
    /// Custom JSON converter for quotes that can be either object {} or empty array []
    /// </summary>
    public class QuotesJsonConverter : JsonConverter<List<CurrencyQuote>>
    {
        public override List<CurrencyQuote> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var quotes = new List<CurrencyQuote>();

            if (reader.TokenType == JsonTokenType.StartArray)
            {
                // Handle empty array case: "quotes": []
                reader.Read(); // Read the end array token
                return quotes; // Return empty list
            }
            else if (reader.TokenType == JsonTokenType.StartObject)
            {
                // Handle object case: "quotes": { "USDEUR": 0.879504, "USDGBP": 0.738798 }
                while (reader.Read())
                {
                    if (reader.TokenType == JsonTokenType.EndObject)
                        break;

                    if (reader.TokenType == JsonTokenType.PropertyName)
                    {
                        var currencyPair = reader.GetString() ?? string.Empty;
                        reader.Read(); // Move to the value
                        var rate = reader.GetDouble();

                        quotes.Add(new CurrencyQuote
                        {
                            CurrencyPair = currencyPair,
                            Rate = rate
                        });
                    }
                }
            }

            return quotes;
        }

        public override void Write(Utf8JsonWriter writer, List<CurrencyQuote> value, JsonSerializerOptions options)
        {
            if (value == null || value.Count == 0)
            {
                writer.WriteStartArray();
                writer.WriteEndArray();
                return;
            }

            writer.WriteStartObject();
            foreach (var quote in value)
            {
                writer.WriteNumber(quote.CurrencyPair, quote.Rate);
            }
            writer.WriteEndObject();
        }
    }
}
