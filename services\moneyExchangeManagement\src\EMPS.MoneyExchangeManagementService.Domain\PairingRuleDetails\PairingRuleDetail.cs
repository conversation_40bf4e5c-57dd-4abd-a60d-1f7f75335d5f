using EMPS.MoneyExchangeManagementService.PairingRules;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class PairingRuleDetail : FullAuditedAggregateRoot<Guid>
    {
        public virtual Guid BaseId { get; set; }

        [NotNull]
        public virtual string BaseCode { get; set; }

        public virtual Guid QuoteId { get; set; }

        [NotNull]
        public virtual string QuoteCode { get; set; }

        [CanBeNull]
        public virtual string? PairingFormat { get; set; }

        public virtual int DisplayOrder { get; set; }
        public Guid PairingRuleId { get; set; }

        public PairingRuleDetail()
        {

        }

        public PairingRuleDetail(Guid id, Guid pairingRuleId, Guid baseId, string baseCode, Guid quoteId, string quoteCode, string pairingFormat, int displayOrder)
        {

            Id = id;
            Check.NotNull(baseCode, nameof(baseCode));
            Check.NotNull(quoteCode, nameof(quoteCode));
            BaseId = baseId;
            BaseCode = baseCode;
            QuoteId = quoteId;
            QuoteCode = quoteCode;
            PairingFormat = pairingFormat;
            DisplayOrder = displayOrder;
            PairingRuleId = pairingRuleId;
        }

    }
}