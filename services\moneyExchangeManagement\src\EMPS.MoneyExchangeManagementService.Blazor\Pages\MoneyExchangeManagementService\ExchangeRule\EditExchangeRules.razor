@using EMPS.MoneyExchangeManagementService.ExchangeRuless
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp
@using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator
@using EMPS.Shared.Enum.ExchangeRules
@inherits MoneyExchangeManagementServiceComponentBase
@inject IExchangeRulessAppService ExchangeRulessAppService
@inject IUiMessageService UiMessageService

<LoadingIndicator @bind-Visible="@LoadingVisiblity">
    <PageHeader Title="@(L["ExchangeRules"] + " : "+ @ExchangeRuleName)" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

    </PageHeader>

    <Card>
        <CardBody>

            <EditForm EditContext="editContext">
                <Validations @ref=EditingExchangeRuleValidations Model="@EditingExchangeRule" Mode="ValidationMode.Manual">
                    <Table>
                        <TableRow>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["Name"] *</FieldLabel>
                                    <TextEdit @bind-Text="@EditingExchangeRule.Name" ReadOnly="@IsApproved">
                                        <Feedback>
                                            <ValidationError />
                                        </Feedback>
                                    </TextEdit>
                                </Field>
                            </TableRowCell>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["Description"]</FieldLabel>
                                    <MemoEdit @bind-Text="@EditingExchangeRule.Description" ReadOnly="@IsApproved">
                                        <Feedback>
                                            <ValidationError />
                                        </Feedback>
                                    </MemoEdit>
                                </Field>
                            </TableRowCell>
                        </TableRow>
                        <TableRow>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["ActivationDate"] *</FieldLabel>
                                    <DateEdit TValue="DateTime" @bind-Date="@EditingExchangeRule.ActivationDate" ReadOnly="@IsApproved">
                                        <Feedback>
                                            <ValidationError />
                                        </Feedback>
                                    </DateEdit>
                                </Field>
                            </TableRowCell>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["ExchangeRuleScope"] *</FieldLabel>
                                    <Select TValue="ExchangeRulesScope" @bind-SelectedValue="@EditingExchangeRule.ExchangeRuleScope"ReadOnly="@IsApproved">
                                        @foreach (var itemValue in Enum.GetValues(typeof(ExchangeRulesScope)))
                                        {
                                            <SelectItem TValue="ExchangeRulesScope" Value="@((ExchangeRulesScope) itemValue)">
                                                @L[$"Enum:ExchangeRulesScope.{(int)itemValue}"]
                                            </SelectItem>
                                        }
                                    </Select>
                                </Field>
                            </TableRowCell>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["IsReprintReceiptAllowed"] *</FieldLabel>
                                    <Check TValue="bool" @bind-Checked="@EditingExchangeRule.IsReprintReceiptAllowed" Disabled="@IsApproved" />
                                    </Field>
                            </TableRowCell>
                        </TableRow>

                        <TableRow>
                            <TableRowCell>

                            <Field>
                                <FieldLabel>@L["RoundUpFee"]</FieldLabel>
                                <NumericPicker TValue="double" @bind-Value="@EditingExchangeRule.RoundUpFee" ReadOnly="@IsApproved">
                                    <Feedback>
                                        <ValidationError />
                                    </Feedback>
                                </NumericPicker>
                            </Field>
                             </TableRowCell>
                            <TableRowCell>
                            <Field>
                                <FieldLabel>@L["ApprovalLimit"]</FieldLabel>
                                <NumericPicker TValue="double" @bind-Value="@EditingExchangeRule.ApprovalLimit" ReadOnly="@IsApproved">
                                    <Feedback>
                                        <ValidationError />
                                    </Feedback>
                                </NumericPicker>
                            </Field>
                            </TableRowCell>

                        </TableRow>
                        <TableRow>
                            <TableRowCell>
                                <Field hidden="@IsApproved">
                                    <SubmitButton Type=ButtonType.Submit Clicked="UpdateExchangeRuleAsync" />
                                </Field>
                            </TableRowCell>
                        </TableRow>
                    </Table>
                </Validations>
            </EditForm>
        </CardBody>
    </Card>
</LoadingIndicator>

@code {

    [Parameter]
    public string ExchangeRuleID { get; set; }
    public string ExchangeRuleName { get; set; }

    [Parameter] public EventCallback<bool> OnDataUpdated { get; set; }

    private bool CanCreateExchangeRule { get; set; }
    private bool CanEditExchangeRule { get; set; }
    private bool CanDeleteExchangeRule { get; set; }
    private Validations EditingExchangeRuleValidations { get; set; } = new();
    private Guid EditingExchangeRuleId { get; set; }

    public bool IsApproved { get; set; }
    private int retryAttempts = 3;
    private ExchangeRulesDto ExchangeRule { get; set; }
    protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
    protected PageToolbar Toolbar { get; } = new PageToolbar();
    protected string SelectedCreateTab = "ExchangeRules-create-tab";
    protected string SelectedEditTab = "ExchangeRules-edit-tab";
    private ExchangeRulesUpdateDto EditingExchangeRule = new ExchangeRulesUpdateDto();
    private EditContext editContext;
    private bool LoadingVisiblity;

    public EditExchangeRules()
    {
        editContext = new EditContext(EditingExchangeRule);
    }

    protected virtual ValueTask SetBreadcrumbItemsAsync()
    {
        BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ExchangeRules"]));
        return ValueTask.CompletedTask;
    }

    protected virtual ValueTask SetToolbarItemsAsync()
    {
        Toolbar.Contributors.Clear();

        if (!IsApproved)
        {
            Toolbar.AddButton(L["Approve"], async () =>
            {
                await ApproveExchangeRuleAsync(Guid.Parse(ExchangeRuleID));
            }, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.ExchangeRuless.Approve);

            Toolbar.AddButton(L["SaveExchangeRule"], async () =>
            {
                await UpdateExchangeRuleAsync();
            }, IconName.Save, requiredPolicyName: MoneyExchangeManagementServicePermissions.ExchangeRuless.Edit);
        }
        return ValueTask.CompletedTask;
    }

    protected override async Task OnInitializedAsync()
    {
        LoadingVisiblity = true;
        var exchangeRule = await ExchangeRulessAppService.GetAsync(Guid.Parse(ExchangeRuleID));
        ExchangeRuleName = exchangeRule.Name;
        IsApproved = exchangeRule.IsApproved;
        EditingExchangeRule = ObjectMapper.Map<ExchangeRulesDto, ExchangeRulesUpdateDto>(exchangeRule);

        editContext = new EditContext(EditingExchangeRule);
        await SetToolbarItemsAsync();
        await SetBreadcrumbItemsAsync();

        LoadingVisiblity = false;
        StateHasChanged();
    }

    private async Task ApproveExchangeRuleAsync(Guid exchangeRuleID)
    {
        LoadingVisiblity = true;

        await ExchangeRulessAppService.ApproveRuleAsync(exchangeRuleID);

        LoadingVisiblity = false;

        StateHasChanged();
        await OnInitializedAsync();
    }

    private async Task UpdateExchangeRuleAsync()
    {
        try
        {
            LoadingVisiblity = true;

            await ExchangeRulessAppService.UpdateAsync(Guid.Parse(ExchangeRuleID), EditingExchangeRule);
            await UiMessageService.Success(L["UpdatedSuccessfully"]);

            LoadingVisiblity = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            LoadingVisiblity = false;
            await HandleErrorAsync(ex);
        }
    }
}
