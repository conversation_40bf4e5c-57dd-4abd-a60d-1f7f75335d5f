using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper.Internal.Mappers;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.Localization;
using Microsoft.AspNetCore.Components;
using Org.BouncyCastle.Math.EC.Rfc7748;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
{
    public partial class CrossRateBulletinDetail
    {

        [Inject]
        public ICrossRateBulletinMasterAppService crossRateBulletinMasterAppService { get; set; }

        public Guid? MasterId { get; set; }
        public CrossRateBulletinMasterDto? masterDto { get; set; }

        [Parameter]
        public bool IsModal { get; set; }

        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new();
        protected List<CrossRateBulletinDetailDto> Details = new();
        protected bool IsRefreshingData { get; set; } = false;

        protected override async Task OnInitializedAsync()
        {
            if (!IsModal)
            {
                await SetBreadcrumbItemsAsync();
            }

            await GetDetailsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CrossRateBulletinMaster"], "/cross-rate-bulletin-master"));
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["CrossRateBulletinDetail"]));
            return ValueTask.CompletedTask;
        }

        protected async Task GetDetailsAsync()
        {
            if (MasterId.HasValue)
            {
                var result = await AppService.GetListByMasterIdAsync(MasterId.Value);
                Details = result.Items.ToList();
                masterDto = await crossRateBulletinMasterAppService.GetAsync(MasterId.Value);
                StateHasChanged();
            }
        }
        public async Task RefreshDetails(Guid masterId)
        {
            MasterId = masterId;

            await GetDetailsAsync();
        }

        private async Task OnUsdSypRowRateValueChanged(CrossRateBulletinDetailDto context, double newCrossRateValue)
        {
            CreateUpdateCrossRateBulletinDetailDto input = ObjectMapper.Map<CrossRateBulletinDetailDto, CreateUpdateCrossRateBulletinDetailDto>(context);
            input.CrossRateValue = newCrossRateValue;
            if (input.CrossRateValue < 0)
            {
                await Notify.Error(L["CannotEnterNegativeValue"]);
                return;
            }
            await AppService.UpdateAsync(context.Id, input);
            await Notify.Success(L["UpdatedSuccessfully"]);
        }

        private async Task RefetchData()
        {
            try
            {
                IsRefreshingData = true;
                StateHasChanged(); // Update UI to show loading state

                var result = await AppService.ReFetchDetailsFromProviderAsync(MasterId.Value);
                Details = result.ToList();
                masterDto = await crossRateBulletinMasterAppService.GetAsync(MasterId.Value);

                await Notify.Success(L["FetchedSuccessfully"]);
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
            finally
            {
                IsRefreshingData = false;
                StateHasChanged(); // Update UI to hide loading state
            }
        }
    }
}