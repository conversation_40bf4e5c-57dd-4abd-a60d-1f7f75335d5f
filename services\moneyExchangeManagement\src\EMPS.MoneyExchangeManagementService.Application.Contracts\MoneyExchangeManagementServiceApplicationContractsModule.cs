﻿using EMPS.CompanyService;
using Volo.Abp.Application;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService;

[DependsOn(
    typeof(MoneyExchangeManagementServiceDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule),
    typeof(CompanyServiceApplicationContractsModule)

    )]
public class MoneyExchangeManagementServiceApplicationContractsModule : AbpModule
{

}
