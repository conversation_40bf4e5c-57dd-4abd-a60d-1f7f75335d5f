using System;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace EMPS.MoneyExchangeManagementService.Controllers.CrossRateBulletin
{
    [RemoteService]
    [Area("MoneyExchangeManagementService")]
    [ControllerName("CrossRateProviderConfiguration")]
    [Route("api/money-exchange-management-service/cross-rate-provider-configuration")]
    public class CrossRateProviderConfigurationController : AbpController, ICrossRateProviderConfigurationAppService
    {
        private readonly ICrossRateProviderConfigurationAppService _appService;

        public CrossRateProviderConfigurationController(ICrossRateProviderConfigurationAppService appService)
        {
            _appService = appService;
        }

        [HttpGet]
        public virtual Task<CrossRateProviderConfigurationDto?> GetAsync()
        {
            return _appService.GetAsync();
        }

        [HttpPost]
        public virtual Task<CrossRateProviderConfigurationDto> CreateOrUpdateAsync(CreateUpdateCrossRateProviderConfigurationDto input)
        {
            return _appService.CreateOrUpdateAsync(input);
        }

        [HttpGet("exists")]
        public virtual Task<bool> ExistsAsync()
        {
            return _appService.ExistsAsync();
        }
    }
}
