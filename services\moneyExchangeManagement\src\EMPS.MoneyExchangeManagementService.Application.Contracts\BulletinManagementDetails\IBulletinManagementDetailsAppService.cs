using EMPS.MoneyExchangeManagementService.Shared;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public interface IBulletinManagementDetailsAppService : IApplicationService
    {
        Task<PagedResultDto<BulletinManagementDetailWithNavigationPropertiesDto>> GetListAsync(GetBulletinManagementDetailsInput input);

        Task<BulletinManagementDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id);

        Task<BulletinManagementDetailDto> GetAsync(Guid id);

        Task<PagedResultDto<LookupDto<Guid>>> GetBulletinManagementMasterLookupAsync(LookupRequestDto input);

        Task DeleteAsync(Guid id);

        Task<BulletinManagementDetailDto> CreateAsync(BulletinManagementDetailCreateDto input);

        Task<BulletinManagementDetailDto> UpdateAsync(Guid id, BulletinManagementDetailUpdateDto input);

    }
}