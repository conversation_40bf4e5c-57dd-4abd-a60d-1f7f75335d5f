using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.PairingRules;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;

namespace EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

[ConnectionStringName(MoneyExchangeManagementServiceDbProperties.ConnectionStringName)]
public class MoneyExchangeManagementServiceDbContext : AbpDbContext<MoneyExchangeManagementServiceDbContext>
{
    public DbSet<BulletinManagementDetail> BulletinManagementDetails { get; set; }
    public DbSet<BulletinManagementMaster> BulletinManagementMasters { get; set; }

    public DbSet<CrossRateBulletinMaster> CrossRateBulletinMasters { get; set; }
    public DbSet<CrossRateBulletinDetail> CrossRateBulletinDetails { get; set; }
    public DbSet<CrossRateProviderConfiguration> CrossRateProviderConfigurations { get; set; }

    public DbSet<PairingRuleDetail> PairingRuleDetails { get; set; }
    public DbSet<PairingRule> PairingRules { get; set; }

    public DbSet<ExchangeRuleDetail> ExchangeRuleDetails { get; set; }
    public DbSet<ExchangeRules> ExchangeRuless { get; set; }

    public DbSet<SpreadRuleDetail> SpreadRuleDetails { get; set; }
    public DbSet<SpreadRule> SpreadRules { get; set; }
    public MoneyExchangeManagementServiceDbContext(DbContextOptions<MoneyExchangeManagementServiceDbContext> options)
        : base(options)
    {

    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ConfigureMoneyExchangeManagementService();
    }
}