using System;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    /// <summary>
    /// DTO for CrossRateProviderConfiguration entity
    /// </summary>
    public class CrossRateProviderConfigurationDto : IEntityDto<Guid>
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// The API endpoint URL for the external provider
        /// </summary>
        public string ProviderBaseUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Authentication token for the provider API
        /// </summary>
        public string ProviderAccessToken { get; set; } = string.Empty;
        
        /// <summary>
        /// The base currency to use in provider requests
        /// </summary>
        public string RequestBaseCurrency { get; set; } = string.Empty;
    }
}
