using System;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailsDataSeedContributor : IDataSeedContributor, ISingletonDependency
    {
        private bool IsSeeded = false;
        private readonly IExchangeRuleDetailRepository _exchangeRuleDetailRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public ExchangeRuleDetailsDataSeedContributor(IExchangeRuleDetailRepository exchangeRuleDetailRepository, IUnitOfWorkManager unitOfWorkManager)
        {
            _exchangeRuleDetailRepository = exchangeRuleDetailRepository;
            _unitOfWorkManager = unitOfWorkManager;

        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (IsSeeded)
            {
                return;
            }

            //await _exchangeRuleDetailRepository.InsertAsync(new ExchangeRuleDetail
            //(
            //    id: Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"),
            //    currencyName: "5c6ff25a996243bdaa6c276d1090139e1632bb7947fd4a0e8f506101612c78214ddf5854dfc54e6a80fa2e674853a450",
            //    currencyID: "19d1fcbc3a42416ca9caa1ec882d832ef6301e1271c54b64b2ee949ade15",
            //    allowedToBuy: true,
            //    minAmountToBuy: 1070721091,
            //    maxAmountToBuy: 51580061,
            //    maxDailyAmountToBuy: 1584829735,
            //    allowedToSell: true,
            //    minAmountToSell: 1208046306,
            //    maxAmountToSell: 1263584799,
            //    maxDailyAmountToSell: "7e77cead3c85496fbe6",
            //    allowedToSellBelowCenterCost: true,
            //    allowedToSellBelowCompanyCost: true,
            //    exchangeRuleMasterID: "09a8b90d2c5f4e279abb76ac1c34d7978c4c0e1e5b4c",
            //    exchangeRuleDetailType: default
            //));

            //await _exchangeRuleDetailRepository.InsertAsync(new ExchangeRuleDetail
            //(
            //    id: Guid.Parse("56ff3310-f120-4b20-8516-4dd5f1b5938d"),
            //    currencyName: "619b37334d7",
            //    currencyID: "b85be643a4054fefaf6c50f57bfde09192e2064b",
            //    allowedToBuy: true,
            //    minAmountToBuy: 1561911081,
            //    maxAmountToBuy: *********,
            //    maxDailyAmountToBuy: 1954495047,
            //    allowedToSell: true,
            //    minAmountToSell: 1260234504,
            //    maxAmountToSell: 1767179843,
            //    maxDailyAmountToSell: "6ed831562f734322a870f11cc240e5d3f0f89743a1d640acaebaa1defd14c3f0b92a45a154664a77997f2ba8288",
            //    allowedToSellBelowCenterCost: true,
            //    allowedToSellBelowCompanyCost: true,
            //    exchangeRuleMasterID: "41afd05f807344e3ac14a73c7465918f8d0a19108bd341cc9b",
            //    exchangeRuleDetailType: default
            //));

            await _unitOfWorkManager.Current.SaveChangesAsync();

            IsSeeded = true;
        }
    }
}