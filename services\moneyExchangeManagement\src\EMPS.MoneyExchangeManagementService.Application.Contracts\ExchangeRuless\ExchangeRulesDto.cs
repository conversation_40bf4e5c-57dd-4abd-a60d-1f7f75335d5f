using EMPS.Shared.Enum.ExchangeRules;
using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulesDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public string Name { get; set; }
        public string? Description { get; set; }
        public DateTime ActivationDate { get; set; }
        public ExchangeRulesScope ExchangeRuleScope { get; set; }
        public bool IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime ApprovedDateTime { get; set; }
        public bool IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime? ArchivedDateTime { get; set; }
        public Guid? UnArchivedByUserId { get; set; }
        public string? UnArchivedByUserName { get; set; }
        public DateTime? UnArchivedByDate { get; set; }
        public virtual bool IsReprintReceiptAllowed { get; set; }
        public virtual double ApprovalLimit { get; set; }

        public virtual double RoundUpFee { get; set; }
        public string ConcurrencyStamp { get; set; }
    }
}