using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.Shared.Enum.ExchangeRules;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("ExchangeRules")]
    [Route("api/money-exchange-management-service/exchange-ruless")]
    public class ExchangeRulesController : AbpController, IExchangeRulessAppService
    {
        private readonly IExchangeRulessAppService _exchangeRulessAppService;

        public ExchangeRulesController(IExchangeRulessAppService exchangeRulessAppService)
        {
            _exchangeRulessAppService = exchangeRulessAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<ExchangeRulesDto>> GetListAsync(GetExchangeRulessInput input)
        {
            return _exchangeRulessAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<ExchangeRulesDto> GetAsync(Guid id)
        {
            return _exchangeRulessAppService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<ExchangeRulesDto> CreateAsync(ExchangeRulesCreateDto input)
        {
            return _exchangeRulessAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<ExchangeRulesDto> UpdateAsync(Guid id, ExchangeRulesUpdateDto input)
        {
            return _exchangeRulessAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _exchangeRulessAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRulesExcelDownloadDto input)
        {
            return _exchangeRulessAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _exchangeRulessAppService.GetDownloadTokenAsync();
        }

        [HttpPut]
        [Route("ApproveAsync/{id}")]
        public Task ApproveRuleAsync(Guid id)
        {
            return _exchangeRulessAppService.ApproveRuleAsync(id);
        }

        [HttpPut]
        [Route("ArchiveAsync/{id}")]
        public Task SetArchiveAsync(Guid id)
        {
            return _exchangeRulessAppService.SetArchiveAsync(id);
        }

        [HttpPut]
        [Route("UnArchiveAsync/{id}")]
        public Task SetUnArchiveAsync(Guid id)
        {
            return _exchangeRulessAppService.SetUnArchiveAsync(id);
        }

        [HttpPost]
        [Route("DuplicateRule/{id}")]
        public Task DuplicateRuleAsync(Guid id)
        {
            return _exchangeRulessAppService.DuplicateRuleAsync(id);
        }

        [HttpGet]
        [Route("GetLastEffectiveApproved")]
        public Task<ExchangeRulesDetailWithNavigationPropertiesDto> GetLastEffectiveApprovedExchangeRulessWithNavigation(Guid? exchangeRuleId, Guid currencyId, ExchangeRulesScope scope)
        {
            return _exchangeRulessAppService.GetLastEffectiveApprovedExchangeRulessWithNavigation(exchangeRuleId, currencyId, scope);
        }

        [HttpGet]
        [Route("GetLastEffectiveApprovedWithAllDetails")]
        public Task<ExchangeRulesWithAllDetailsDto> GetLastEffectiveApprovedExchangeRulessWithAllDetails(Guid? exchangeRuleId, ExchangeRulesScope scope)
        {
            return _exchangeRulessAppService.GetLastEffectiveApprovedExchangeRulessWithAllDetails(exchangeRuleId, scope);
        }
    }
}