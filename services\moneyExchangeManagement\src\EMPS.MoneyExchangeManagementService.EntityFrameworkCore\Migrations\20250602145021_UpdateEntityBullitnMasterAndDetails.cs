﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class UpdateEntityBullitnMasterAndDetails : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BulletinManagementDetails_BulletinManagementMasters_BulletinManagementMasterId",
                table: "BulletinManagementDetails");

            migrationBuilder.AddColumn<bool>(
                name: "IsGlobal",
                table: "BulletinManagementMasters",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "CurrencyBaseCode",
                table: "BulletinManagementDetails",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CurrencyBaseId",
                table: "BulletinManagementDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CurrencyQuoteCode",
                table: "BulletinManagementDetails",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CurrencyQuoteId",
                table: "BulletinManagementDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_BulletinManagementDetails_BulletinManagementMasters_BulletinManagementMasterId",
                table: "BulletinManagementDetails",
                column: "BulletinManagementMasterId",
                principalTable: "BulletinManagementMasters",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BulletinManagementDetails_BulletinManagementMasters_BulletinManagementMasterId",
                table: "BulletinManagementDetails");

            migrationBuilder.DropColumn(
                name: "IsGlobal",
                table: "BulletinManagementMasters");

            migrationBuilder.DropColumn(
                name: "CurrencyBaseCode",
                table: "BulletinManagementDetails");

            migrationBuilder.DropColumn(
                name: "CurrencyBaseId",
                table: "BulletinManagementDetails");

            migrationBuilder.DropColumn(
                name: "CurrencyQuoteCode",
                table: "BulletinManagementDetails");

            migrationBuilder.DropColumn(
                name: "CurrencyQuoteId",
                table: "BulletinManagementDetails");

            migrationBuilder.AddForeignKey(
                name: "FK_BulletinManagementDetails_BulletinManagementMasters_BulletinManagementMasterId",
                table: "BulletinManagementDetails",
                column: "BulletinManagementMasterId",
                principalTable: "BulletinManagementMasters",
                principalColumn: "Id");
        }
    }
}
