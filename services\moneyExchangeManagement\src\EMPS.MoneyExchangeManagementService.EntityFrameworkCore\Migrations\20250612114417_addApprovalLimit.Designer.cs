﻿// <auto-generated />
using System;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    [DbContext(typeof(MoneyExchangeManagementServiceDbContext))]
    [Migration("20250612114417_addApprovalLimit")]
    partial class addApprovalLimit
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementDetails.BulletinManagementDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AccountAsk")
                        .HasColumnType("float")
                        .HasColumnName("AccountAsk");

                    b.Property<double>("AccountBid")
                        .HasColumnType("float")
                        .HasColumnName("AccountBid");

                    b.Property<Guid>("BulletinManagementMasterId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CashAsk")
                        .HasColumnType("float")
                        .HasColumnName("CashAsk");

                    b.Property<double>("CashBid")
                        .HasColumnType("float")
                        .HasColumnName("CashBid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyBaseCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CurrencyBaseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CurrencyPair")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyPair");

                    b.Property<string>("CurrencyQuoteCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CurrencyQuoteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int")
                        .HasColumnName("DisplayOrder");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.HasKey("Id");

                    b.HasIndex("BulletinManagementMasterId");

                    b.ToTable("BulletinManagementDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementMasters.BulletinManagementMaster", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BulletinDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("BulletinDate");

                    b.Property<string>("BulletinName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BulletinName");

                    b.Property<string>("BulletinNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BulletinNumber");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("CrossRateBulletinId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CrossRateBulletinId");

                    b.Property<string>("CrossRateBulletinName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CrossRateBulletinName");

                    b.Property<Guid?>("CurrencyPairingRuleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyPairingRuleId");

                    b.Property<string>("CurrencyPairingRuleName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyPairingRuleName");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<Guid?>("PublishByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("PublishByUserId");

                    b.Property<string>("PublishByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PublishByUserName");

                    b.Property<DateTime?>("PublishDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("PublishDate");

                    b.Property<Guid?>("ServicePointId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ServicePointId");

                    b.Property<string>("ServicePointName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ServicePointName");

                    b.Property<Guid?>("SpreadRuleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("SpreadRuleId");

                    b.Property<string>("SpreadRuleName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SpreadRuleName");

                    b.HasKey("Id");

                    b.ToTable("BulletinManagementMasters", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateBulletinDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BaseCurrencyCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid>("CrossRateBulletinMasterId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CrossRateValue")
                        .HasColumnType("float");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("PairingFormat")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuoteCurrencyCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CrossRateBulletinMasterId");

                    b.ToTable("CrossRateBulletinDetails");
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateBulletinMaster", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ArchivedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CrossRateBulletinMasters");
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProviderConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("ProviderAccessToken")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("ProviderAccessToken");

                    b.Property<string>("ProviderBaseUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("ProviderBaseUrl");

                    b.Property<string>("RequestBaseCurrency")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("RequestBaseCurrency");

                    b.HasKey("Id");

                    b.ToTable("CrossRateProviderConfigurations", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.ExchangeRuleDetails.ExchangeRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AllowedToBuy")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToBuy");

                    b.Property<bool>("AllowedToSell")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSell");

                    b.Property<bool>("AllowedToSellBelowCenterCost")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSellBelowCenterCost");

                    b.Property<bool>("AllowedToSellBelowCompanyCost")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSellBelowCompanyCost");

                    b.Property<double>("ApprovalLimit")
                        .HasColumnType("float");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ApprovedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ArchivedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid>("CurrencyID")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyID");

                    b.Property<string>("CurrencyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyName");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("ExchangeRuleDetailType")
                        .HasColumnType("int")
                        .HasColumnName("ExchangeRuleDetailType");

                    b.Property<Guid>("ExchangeRuleMasterID")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExchangeRuleMasterID");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<double>("MaxAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MaxAmountToBuy");

                    b.Property<double>("MaxAmountToSell")
                        .HasColumnType("float")
                        .HasColumnName("MaxAmountToSell");

                    b.Property<double>("MaxDailyAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MaxDailyAmountToBuy");

                    b.Property<double>("MaxDailyAmountToSell")
                        .HasColumnType("float")
                        .HasColumnName("MaxDailyAmountToSell");

                    b.Property<double>("MinAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MinAmountToBuy");

                    b.Property<double>("MinAmountToSell")
                        .HasColumnType("float")
                        .HasColumnName("MinAmountToSell");

                    b.Property<double>("RoundUpFee")
                        .HasColumnType("float");

                    b.Property<DateTime?>("UnArchivedByDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UnArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UnArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ExchangeRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.ExchangeRuless.ExchangeRules", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ActivationDate");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ApprovedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ArchivedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<int>("ExchangeRuleScope")
                        .HasColumnType("int")
                        .HasColumnName("ExchangeRuleScope");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsReprintReceiptAllowed")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UnArchivedByDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UnArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UnArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ExchangeRuless", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRuleDetails.PairingRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BaseCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BaseCode");

                    b.Property<Guid>("BaseId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("BaseId");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int")
                        .HasColumnName("DisplayOrder");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("PairingFormat")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PairingFormat");

                    b.Property<Guid>("PairingRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("QuoteCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("QuoteCode");

                    b.Property<Guid>("QuoteId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("QuoteId");

                    b.HasKey("Id");

                    b.HasIndex("PairingRuleId");

                    b.ToTable("PairingRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRules.PairingRule", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovalDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDateTime");

                    b.Property<Guid?>("ApprovedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedBy");

                    b.Property<string>("ApprovedByName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByName");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<DateTime?>("EffectiveDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("EffectiveDate");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit")
                        .HasColumnName("IsArchived");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.HasKey("Id");

                    b.ToTable("PairingRules", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRuleDetails.SpreadRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AskMaxDiscount")
                        .HasColumnType("float")
                        .HasColumnName("AskMaxDiscount");

                    b.Property<double>("AskMaxMarkup")
                        .HasColumnType("float")
                        .HasColumnName("AskMaxMarkup");

                    b.Property<double>("AskSpread")
                        .HasColumnType("float")
                        .HasColumnName("AskSpread");

                    b.Property<double>("BidMaxDiscount")
                        .HasColumnType("float")
                        .HasColumnName("BidMaxDiscount");

                    b.Property<double>("BidMaxMarkdown")
                        .HasColumnType("float")
                        .HasColumnName("BidMaxMarkdown");

                    b.Property<double>("BidSpread")
                        .HasColumnType("float")
                        .HasColumnName("BidSpread");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyCode");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("SpreadRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("Type");

                    b.HasKey("Id");

                    b.HasIndex("SpreadRuleId");

                    b.ToTable("SpreadRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRules.SpreadRule", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActivationDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ActivationDate");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedByUserId");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByUserName");

                    b.Property<DateTime?>("ApprovedDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovedDateTime");

                    b.Property<Guid?>("ArchivedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ArchivedByUserId");

                    b.Property<string>("ArchivedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ArchivedByUserName");

                    b.Property<DateTime?>("ArchivedDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ArchivedDateTime");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit")
                        .HasColumnName("IsArchived");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RuleName");

                    b.Property<int>("Scope")
                        .HasColumnType("int")
                        .HasColumnName("Scope");

                    b.Property<DateTime?>("UnArchivedByDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UnArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UnArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SpreadRules", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementDetails.BulletinManagementDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.BulletinManagementMasters.BulletinManagementMaster", null)
                        .WithMany("Details")
                        .HasForeignKey("BulletinManagementMasterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateBulletinDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateBulletinMaster", null)
                        .WithMany("CrossRateBulletinDetails")
                        .HasForeignKey("CrossRateBulletinMasterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRuleDetails.PairingRuleDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.PairingRules.PairingRule", null)
                        .WithMany()
                        .HasForeignKey("PairingRuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRuleDetails.SpreadRuleDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.SpreadRules.SpreadRule", null)
                        .WithMany("SpreadRuleDetails")
                        .HasForeignKey("SpreadRuleId");
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementMasters.BulletinManagementMaster", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateBulletinMaster", b =>
                {
                    b.Navigation("CrossRateBulletinDetails");
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRules.SpreadRule", b =>
                {
                    b.Navigation("SpreadRuleDetails");
                });
#pragma warning restore 612, 618
        }
    }
}
