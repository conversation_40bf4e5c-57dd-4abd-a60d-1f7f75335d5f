﻿@page "/PairingRuleDetails/{PairingRuleId}"
@attribute [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default)]
@using EMPS.MoneyExchangeManagementService.PairingRuleDetails
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject IPairingRuleDetailsAppService PairingRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager

<Tabs SelectedTab=@TabName RenderMode=TabsRenderMode.LazyReload TabPosition=TabPosition.Top FullWidth="true"
      SelectedTabChanged="@((Name) => {TabName =Name; StateHasChanged();})" >
    <Items>
        <Tab Name="PairingRuleTab"><Icon Name="@("fa fa-edit")" /> @L["PairingRuleTab"]</Tab>
        <Tab Name="DetailesTab"><Icon Name="@("fas fa-university")" /> @L["DetailesTab"]</Tab>
    </Items>
    <Content>
        <TabPanel Name="PairingRuleTab">
            @if (PairingRuleId != null)
            {
                @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default).Result == true)
                {
                    <PairingRuleMaster PairingRuleId="@PairingRuleId"></PairingRuleMaster> 
                }
            }
        </TabPanel>
        <TabPanel Name="DetailesTab">
            @if (PairingRuleId != null)
            {
                @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default).Result == true)
                {
                    <PairingRuleDetails PairingRuleId="@PairingRuleId"></PairingRuleDetails>
                }
            }
        </TabPanel>
      
    </Content>
</Tabs>




@code {
    [Parameter]
    public string PairingRuleId { get; set; }

    public string TabName { get; set; }

    string selectedTab = "PairingRuleTab";

    private Task OnSelectedTabChanged(string name)
    {
        selectedTab = name;

        return Task.CompletedTask;
    }
    protected override void OnInitialized()
    {
        base.OnInitialized();
        TabName = selectedTab;
    }
}
