using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.DependencyInjection;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// CurrencyLayer API implementation of ICrossLimitProvider
    /// </summary>
    public class CurrencyLayerProvider : ICrossLimitProvider, ITransientDependency
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CurrencyLayerProvider> _logger;

        public CurrencyLayerProvider(HttpClient httpClient, ILogger<CurrencyLayerProvider> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// Gets cross limits data from CurrencyLayer API
        /// </summary>
        /// <param name="input">Request parameters including API configuration and requested currencies</param>
        /// <returns>Collection of cross limit responses</returns>
        public async Task<ICollection<CrossLimitProviderResponse>> GetCrossLimitsAsync(CrossLimitProviderRequest input)
        {
            try
            {
                // Validate input configuration
                if (string.IsNullOrWhiteSpace(input.ProviderBaseUrl))
                {
                    throw new UserFriendlyException(
                        "Cross rate provider base URL is not configured. Please contact the administration to configure the exchange rate provider settings.");
                }

                if (string.IsNullOrWhiteSpace(input.AccessToken))
                {
                    throw new UserFriendlyException(
                        "Cross rate provider access token is not configured. Please contact the administration to configure the exchange rate provider settings.");
                }

                if (string.IsNullOrWhiteSpace(input.RequestBaseCurrency))
                {
                    throw new UserFriendlyException(
                        "Cross rate provider base currency is not configured. Please contact the administration to configure the exchange rate provider settings.");
                }

                // For testing purposes, use hardcoded currencies if none provided
                var requestedCurrencies = input.RequestedCurrencies;

                _logger.LogInformation("Fetching exchange rates from CurrencyLayer API for currencies: {Currencies}",
                    string.Join(", ", requestedCurrencies));

                // Build the API URL
                var apiUrl = BuildApiUrl(input.ProviderBaseUrl, input.AccessToken, input.RequestBaseCurrency, requestedCurrencies);

                _logger.LogDebug("CurrencyLayer API URL: {ApiUrl}", apiUrl);

                // Make the HTTP request
                var response = await _httpClient.GetAsync(apiUrl);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("CurrencyLayer API request failed with status code: {StatusCode}", response.StatusCode);
                    throw new UserFriendlyException(
                        $"Failed to fetch exchange rates from the provider. HTTP Status: {response.StatusCode}. Please contact the administration.");
                }

                var jsonContent = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("CurrencyLayer API response: {Response}", jsonContent);

                // Parse the JSON response
                var apiResponse = JsonSerializer.Deserialize<CurrencyLayerApiResponse>(jsonContent);

                if (apiResponse == null)
                {
                    _logger.LogError("Failed to deserialize CurrencyLayer API response");
                    throw new UserFriendlyException(
                        "Failed to process exchange rates response from the provider. Please contact the administration.");
                }

                if (!apiResponse.Success)
                {
                    _logger.LogError("CurrencyLayer API returned error: {ErrorCode} - {ErrorInfo}",
                        apiResponse.Error?.Code, apiResponse.Error?.Info);
                    throw new UserFriendlyException(
                        $"Exchange rate provider returned an error: {apiResponse.Error?.Info ?? "Unknown error"}. Please contact the administration.");
                }

                // Convert API response to CrossLimitProviderResponse objects
                return ConvertApiResponseToCrossLimitResponses(apiResponse);
            }
            catch (UserFriendlyException)
            {
                // Re-throw user-friendly exceptions as-is
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching data from CurrencyLayer API");
                throw new UserFriendlyException(
                    "An unexpected error occurred while fetching exchange rates. Please contact the administration.");
            }
        }

        /// <summary>
        /// Builds the CurrencyLayer API URL with parameters
        /// </summary>
        private string BuildApiUrl(string baseUrl, string accessToken, string baseCurrency, List<string> currencies)
        {
            var currenciesParam = string.Join(",", currencies);
            var url = $"{baseUrl}?access_key={accessToken}&source={baseCurrency}&currencies={currenciesParam}&format=1";

            return url;
        }

        /// <summary>
        /// Converts CurrencyLayer API response to CrossLimitProviderResponse objects
        /// </summary>
        private ICollection<CrossLimitProviderResponse> ConvertApiResponseToCrossLimitResponses(CurrencyLayerApiResponse apiResponse)
        {
            var responses = new List<CrossLimitProviderResponse>();

            // Handle case where quotes might be null or empty
            if (apiResponse.Quotes == null || !apiResponse.Quotes.Any())
            {
                _logger.LogWarning("CurrencyLayer API returned empty or null quotes. No exchange rates available.");
                return responses; // Return empty list instead of throwing exception
            }

            foreach (var quote in apiResponse.Quotes)
            {
                // Extract currency codes from the quote key (e.g., "USDEUR" -> source="USD", quote="EUR")
                var quoteCurrencyCode = ExtractQuoteCurrencyFromKey(quote.CurrencyPair, apiResponse.Source);

                if (!string.IsNullOrEmpty(quoteCurrencyCode))
                {
                    responses.Add(new CrossLimitProviderResponse
                    {
                        SourceCurrencyCode = apiResponse.Source,
                        QuoteCurrencyCode = quoteCurrencyCode,
                        CrossRateValue = quote.Rate
                    });
                }
            }

            _logger.LogInformation("Successfully converted {Count} exchange rates from CurrencyLayer API", responses.Count);
            return responses;
        }

        /// <summary>
        /// Extracts the quote currency code from the CurrencyLayer quote key
        /// </summary>
        private string ExtractQuoteCurrencyFromKey(string quoteKey, string sourceCurrency)
        {
            // CurrencyLayer format: "USDEUR" where USD is source and EUR is quote
            if (quoteKey.Length >= 6 && quoteKey.StartsWith(sourceCurrency))
            {
                return quoteKey.Substring(sourceCurrency.Length);
            }

            _logger.LogWarning("Unable to extract quote currency from key: {QuoteKey}", quoteKey);
            return string.Empty;
        }


    }
}
