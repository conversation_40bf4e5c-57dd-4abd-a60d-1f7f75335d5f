using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EMPS.CompanyService.Currencies;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider;
using EMPS.MoneyExchangeManagementService.Localization;
using EMPS.MoneyExchangeManagementService.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.Application.CrossRateBulletin
{
    [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default)]
    public class CrossRateBulletinDetailAppService :
        CrudAppService<
            CrossRateBulletinDetail,
            CrossRateBulletinDetailDto,
            Guid,
            GetCrossRateBulletinDetailListDto,
            CreateUpdateCrossRateBulletinDetailDto
        >,
        ICrossRateBulletinDetailAppService
    {

        public CrossRateDetailGenerator _crossRateDetailGenerator { get; set; }
        public IRepository<CrossRateBulletinMaster, Guid> _crossRateMasterRepository { get; set; }
        private readonly ICurrenciesAppService _currenciesAppService;

        public CrossRateBulletinDetailAppService(IRepository<CrossRateBulletinMaster, Guid> crossRateMasterRepository, IRepository<CrossRateBulletinDetail, Guid> repository, CrossRateDetailGenerator crossRateDetailGenerator, ICurrenciesAppService currenciesAppService)
            : base(repository)
        {
            _currenciesAppService = currenciesAppService;
            _crossRateMasterRepository = crossRateMasterRepository;
            _crossRateDetailGenerator = crossRateDetailGenerator;
            LocalizationResource = typeof(MoneyExchangeManagementServiceResource);
            GetPolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default;
            GetListPolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default;
            CreatePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create;
            UpdatePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create;
            DeletePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Delete;
        }


        public override Task<CrossRateBulletinDetailDto> UpdateAsync(Guid id, CreateUpdateCrossRateBulletinDetailDto input)
        {
            return base.UpdateAsync(id, input);
        }

        protected override async Task<IQueryable<CrossRateBulletinDetail>> CreateFilteredQueryAsync(GetCrossRateBulletinDetailListDto input)
        {
            var query = await base.CreateFilteredQueryAsync(input);
            query = query.WhereIf(input.CrossRateBulletinMasterId.HasValue, e => e.CrossRateBulletinMasterId == input.CrossRateBulletinMasterId);
            return query;
        }

        public async Task<ListResultDto<CrossRateBulletinDetailDto>> GetListByMasterIdAsync(Guid masterId)
        {
            var query = await Repository.GetQueryableAsync();
            query = query.OrderBy(x => x.DisplayOrder);
            var items = await AsyncExecuter.ToListAsync(query.Where(x => x.CrossRateBulletinMasterId == masterId));

            return new ListResultDto<CrossRateBulletinDetailDto>(
                ObjectMapper.Map<List<CrossRateBulletinDetail>, List<CrossRateBulletinDetailDto>>(items)
            );
        }

        public async Task<List<CrossRateBulletinDetailDto>> ReFetchDetailsFromProviderAsync(Guid masterId)
        {
            if (await _crossRateMasterRepository.AnyAsync(x => x.Id == masterId && x.IsApproved))
            {
                throw new UserFriendlyException(L["CannotFetchDetailsForApprovedMaster"]);
            }
            var currencies = await _currenciesAppService.GetActiveAsync();
            return ObjectMapper.Map<List<CrossRateBulletinDetail>, List<CrossRateBulletinDetailDto>>(await _crossRateDetailGenerator.RegenerateDetailsForMasterAsync(masterId, currencies.Select(x => x.Code).ToList()));
        }
    }
}