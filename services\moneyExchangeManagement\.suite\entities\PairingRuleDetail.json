{"Id": "8e41fe70-9beb-4092-9f3a-075566c8d7f6", "Name": "PairingRuleDetail", "OriginalName": "PairingRuleDetail", "NamePlural": "PairingRuleDetails", "DatabaseTableName": "PairingRuleDetails", "Namespace": "PairingRuleDetails", "BaseClass": "FullAuditedAggregateRoot", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "IsMultiTenant": false, "CheckConcurrency": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": true, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": false, "Properties": [{"Id": "c872b895-0c5f-44a9-828b-db1230829c20", "Name": "BaseId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "9620a543-9118-4af0-80b3-4e514ffa9d6f", "Name": "BaseCode", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "35445f3a-9d42-462a-b845-390eecc59b86", "Name": "QuoteId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "fd1f56d6-ee7c-4158-bcce-c9d16f32268d", "Name": "QuoteCode", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "bb468cf1-5fd5-4d74-b61f-b95969795c4b", "Name": "PairingFormat", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "682023b8-8f6f-47d2-b782-3de03ce05866", "Name": "DisplayOrder", "Type": "int", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}], "NavigationProperties": [{"EntityNameWithDuplicationNumber": "PairingRule", "EntitySetNameWithDuplicationNumber": "PairingRules", "ReferencePropertyName": "PairingRule", "UiPickType": "Dropdown", "IsRequired": true, "Name": "PairingRuleId", "DisplayProperty": "Name", "Namespace": "EMPS.MoneyExchangeManagementService.PairingRules", "EntityName": "PairingRule", "EntitySetName": "PairingRules", "DtoNamespace": "EMPS.MoneyExchangeManagementService.PairingRules", "DtoEntityName": "PairingRuleDto", "Type": "Guid"}], "NavigationConnections": [], "PhysicalFileName": "PairingRuleDetail.json"}