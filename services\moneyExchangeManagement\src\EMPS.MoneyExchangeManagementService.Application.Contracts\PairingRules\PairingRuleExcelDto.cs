using System;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public class PairingRuleExcelDto
    {
        public string Name { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public string? Description { get; set; }
        public bool IsApproved { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? ApprovalDateTime { get; set; }
        public bool IsArchived { get; set; }
    }
}