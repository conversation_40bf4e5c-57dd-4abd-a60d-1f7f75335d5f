﻿<Project Sdk="Microsoft.NET.Sdk">

	<Import Project="..\..\..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<Nullable>enable</Nullable>
		<RootNamespace>EMPS.MoneyExchangeManagementService</RootNamespace>
	</PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MiniExcel" Version="1.26.2" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Volo.Abp.BlobStoring" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.BlobStoring.FileSystem" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Ddd.Application" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="7.3.2" />
    <ProjectReference Include="..\..\..\identity\src\EMPS.IdentityService.HttpApi.Client\EMPS.IdentityService.HttpApi.Client.csproj" />
    <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Application.Contracts\EMPS.MoneyExchangeManagementService.Application.Contracts.csproj" />
    <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Domain\EMPS.MoneyExchangeManagementService.Domain.csproj" />
    <ProjectReference Include="..\..\..\company\src\EMPS.CompanyService.HttpApi.Client\EMPS.CompanyService.HttpApi.Client.csproj" />

  </ItemGroup>

</Project>
