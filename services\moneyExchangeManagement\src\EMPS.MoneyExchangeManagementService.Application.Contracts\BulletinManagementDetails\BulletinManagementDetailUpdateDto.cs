using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class BulletinManagementDetailUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string CurrencyPair { get; set; }
        public double CashBid { get; set; }
        public double CashAsk { get; set; }
        public double AccountBid { get; set; }
        public double AccountAsk { get; set; }
        public int DisplayOrder { get; set; }
        public Guid BulletinManagementMasterId { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}