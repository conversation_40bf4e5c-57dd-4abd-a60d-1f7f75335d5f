﻿using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin
{
    public class EfCoreCrossRateBulletinMasterRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, CrossRateBulletinMaster, Guid>, ICrossRateBulletinMasterRepository
    {
        public EfCoreCrossRateBulletinMasterRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<CrossRateBulletinMaster?> GetLastCrossApprovedAsync()
        {
            var query = (await GetQueryableAsync()).Where(x=>x.IsApproved);
            if (!query.Any())
                return null;
            return query.OrderByDescending(c=>c.ApprovedAt).Include(x=>x.CrossRateBulletinDetails).First();

        }
    }
}
