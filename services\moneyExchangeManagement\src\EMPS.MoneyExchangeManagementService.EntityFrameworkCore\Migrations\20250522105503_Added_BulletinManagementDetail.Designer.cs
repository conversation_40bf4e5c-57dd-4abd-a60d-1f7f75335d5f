﻿// <auto-generated />
using System;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    [DbContext(typeof(MoneyExchangeManagementServiceDbContext))]
    [Migration("20250522105503_Added_BulletinManagementDetail")]
    partial class AddedBulletinManagementDetail
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementDetails.BulletinManagementDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AccountAsk")
                        .HasColumnType("float")
                        .HasColumnName("AccountAsk");

                    b.Property<double>("AccountBid")
                        .HasColumnType("float")
                        .HasColumnName("AccountBid");

                    b.Property<Guid>("BulletinManagementMasterId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("CashAsk")
                        .HasColumnType("float")
                        .HasColumnName("CashAsk");

                    b.Property<double>("CashBid")
                        .HasColumnType("float")
                        .HasColumnName("CashBid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyPair")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyPair");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int")
                        .HasColumnName("DisplayOrder");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.HasKey("Id");

                    b.HasIndex("BulletinManagementMasterId");

                    b.ToTable("BulletinManagementDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementMasters.BulletinManagementMaster", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("BulletinDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("BulletinDate");

                    b.Property<string>("BulletinName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BulletinName");

                    b.Property<string>("BulletinNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BulletinNumber");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("CrossRateBulletinId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CrossRateBulletinId");

                    b.Property<string>("CrossRateBulletinName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CrossRateBulletinName");

                    b.Property<Guid?>("CurrencyPairingRuleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyPairingRuleId");

                    b.Property<string>("CurrencyPairingRuleName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyPairingRuleName");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<Guid?>("ServicePointId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ServicePointId");

                    b.Property<string>("ServicePointName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ServicePointName");

                    b.Property<Guid?>("SpreadRuleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("SpreadRuleId");

                    b.Property<string>("SpreadRuleName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SpreadRuleName");

                    b.HasKey("Id");

                    b.ToTable("BulletinManagementMasters", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.BulletinManagementDetails.BulletinManagementDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.BulletinManagementMasters.BulletinManagementMaster", null)
                        .WithMany()
                        .HasForeignKey("BulletinManagementMasterId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
