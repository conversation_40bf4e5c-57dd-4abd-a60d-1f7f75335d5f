using Volo.Abp.Reflection;

namespace EMPS.MoneyExchangeManagementService.Permissions;

public class MoneyExchangeManagementServicePermissions
{
    public const string GroupName = "MoneyExchangeManagementService";

    public static class CrossRateBulletinMaster
    {
        public const string Default = GroupName + ".CrossRateBulletinMaster";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
        public const string Approve = Default + ".Approve";
        public const string Archive = Default + ".Archive";
        public const string Unarchive = Default + ".Unarchive";
    }

    public static class CrossRateProviderConfiguration
    {
        public const string Default = GroupName + ".CrossRateProviderConfiguration";
        public const string Manage = Default + ".Manage";
    }

    public static string[] GetAll()
    {
        return ReflectionHelper.GetPublicConstantsRecursively(typeof(MoneyExchangeManagementServicePermissions));
    }

    public static class BulletinManagementMasters
    {
        public const string Default = GroupName + ".BulletinManagementMasters";
        public const string Publish = Default + ".Publish";
        public const string Preview = Default + ".Preview";


    }

    public static class BulletinManagementDetails
    {
        public const string Default = GroupName + ".BulletinManagementDetails";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
    }


    public static class PairingRules
    {
        public const string Default = GroupName + ".PairingRules";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
        public const string Approve = Default + ".Approve";
    }

    public static class PairingRuleDetails
    {
        public const string Default = GroupName + ".PairingRuleDetails";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
    }


    public static class SpreadRules
    {
        public const string Default = GroupName + ".SpreadRules";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
        public const string Archive = Default + ".Archive";
        public const string UnArchive = Default + ".UnArchive";
        public const string Approve = Default + ".Approve";
        public const string Duplicate = Default + ".Duplicate";
    }

    public static class SpreadRuleDetails
    {
        public const string Default = GroupName + ".SpreadRuleDetails";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
    }

    public static class ExchangeRuless
    {
        public const string Default = GroupName + ".ExchangeRuless";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
        public const string Archive = Default + ".Archive";
        public const string UnArchive = Default + ".UnArchive";
        public const string Approve = Default + ".Approve";
        public const string Duplicate = Default + ".Duplicate";
        public const string Apply = Default + ".Apply";
    }

    public static class ExchangeRuleDetails
    {
        public const string Default = GroupName + ".ExchangeRuleDetails";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
    }
}