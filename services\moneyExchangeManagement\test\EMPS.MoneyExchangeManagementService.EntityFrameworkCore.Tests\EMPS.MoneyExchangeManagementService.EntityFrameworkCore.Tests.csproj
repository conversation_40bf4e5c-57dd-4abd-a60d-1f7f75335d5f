<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeManagementService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.2.0" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="7.0.0" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="7.3.2" />
    <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.TestBase\EMPS.MoneyExchangeManagementService.TestBase.csproj" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\EMPS.MoneyExchangeManagementService.EntityFrameworkCore\EMPS.MoneyExchangeManagementService.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>
