using System;
using System.Linq;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.Localization;
using EMPS.MoneyExchangeManagementService.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.Application.CrossRateBulletin
{
    /// <summary>
    /// Application service for managing CrossRateProviderConfiguration
    /// </summary>
    [Authorize(MoneyExchangeManagementServicePermissions.CrossRateProviderConfiguration.Default)]
    public class CrossRateProviderConfigurationAppService : ApplicationService, ICrossRateProviderConfigurationAppService
    {
        private readonly IRepository<CrossRateProviderConfiguration, Guid> _repository;

        public CrossRateProviderConfigurationAppService(IRepository<CrossRateProviderConfiguration, Guid> repository)
        {
            _repository = repository;
            LocalizationResource = typeof(MoneyExchangeManagementServiceResource);
        }

        /// <summary>
        /// Gets the current provider configuration (singleton)
        /// </summary>
        public async Task<CrossRateProviderConfigurationDto?> GetAsync()
        {
            var configuration = await _repository.FirstOrDefaultAsync();
            return configuration != null ? ObjectMapper.Map<CrossRateProviderConfiguration, CrossRateProviderConfigurationDto>(configuration) : null;
        }

        /// <summary>
        /// Creates or updates the provider configuration (singleton)
        /// </summary>
        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateProviderConfiguration.Manage)]
        public async Task<CrossRateProviderConfigurationDto> CreateOrUpdateAsync(CreateUpdateCrossRateProviderConfigurationDto input)
        {
            var existingConfiguration = await _repository.FirstOrDefaultAsync();

            CrossRateProviderConfiguration configuration;

            if (existingConfiguration == null)
            {
                // Create new configuration
                configuration = new CrossRateProviderConfiguration
                {
                    ProviderBaseUrl = input.ProviderBaseUrl,
                    ProviderAccessToken = input.ProviderAccessToken,
                    RequestBaseCurrency = input.RequestBaseCurrency.ToUpperInvariant()
                };

                configuration = await _repository.InsertAsync(configuration, autoSave: true);
            }
            else
            {
                // Update existing configuration
                existingConfiguration.ProviderBaseUrl = input.ProviderBaseUrl;
                existingConfiguration.ProviderAccessToken = input.ProviderAccessToken;
                existingConfiguration.RequestBaseCurrency = input.RequestBaseCurrency.ToUpperInvariant();

                configuration = await _repository.UpdateAsync(existingConfiguration, autoSave: true);
            }

            return ObjectMapper.Map<CrossRateProviderConfiguration, CrossRateProviderConfigurationDto>(configuration);
        }

        /// <summary>
        /// Checks if the provider configuration exists
        /// </summary>
        public async Task<bool> ExistsAsync()
        {
            return await _repository.AnyAsync();
        }
    }
}
