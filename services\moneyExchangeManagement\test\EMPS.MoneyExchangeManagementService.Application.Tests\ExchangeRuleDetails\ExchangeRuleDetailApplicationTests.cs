using System;
using System.Linq;
using Shouldly;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Xunit;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailsAppServiceTests : MoneyExchangeManagementServiceApplicationTestBase
    {
        private readonly IExchangeRuleDetailsAppService _exchangeRuleDetailsAppService;
        private readonly IRepository<ExchangeRuleDetail, Guid> _exchangeRuleDetailRepository;

        public ExchangeRuleDetailsAppServiceTests()
        {
            _exchangeRuleDetailsAppService = GetRequiredService<IExchangeRuleDetailsAppService>();
            _exchangeRuleDetailRepository = GetRequiredService<IRepository<ExchangeRuleDetail, Guid>>();
        }

        [Fact]
        public async Task GetListAsync()
        {
            // Act
            var result = await _exchangeRuleDetailsAppService.GetListAsync(new GetExchangeRuleDetailsInput());

            // Assert
            result.TotalCount.ShouldBe(2);
            result.Items.Count.ShouldBe(2);
            result.Items.Any(x => x.Id == Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3")).ShouldBe(true);
            result.Items.Any(x => x.Id == Guid.Parse("56ff3310-f120-4b20-8516-4dd5f1b5938d")).ShouldBe(true);
        }

        [Fact]
        public async Task GetAsync()
        {
            // Act
            var result = await _exchangeRuleDetailsAppService.GetAsync(Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"));

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"));
        }

        [Fact]
        public async Task CreateAsync()
        {
            // Arrange
            //var input = new ExchangeRuleDetailCreateDto
            //{
            //    CurrencyName = "141161f92dfa",
            //    CurrencyID = "02df6ee9e2d64be0b9052d264e72fdab48e191685e7e45c78168",
            //    AllowedToBuy = true,
            //    MinAmountToBuy = *********,
            //    MaxAmountToBuy = 1918290901,
            //    MaxDailyAmountToBuy = 1138296887,
            //    AllowedToSell = true,
            //    MinAmountToSell = *********,
            //    MaxAmountToSell = 1772732556,
            //    MaxDailyAmountToSell = "2c7cfb72559d4744b01af740aef875f69ba67d9eb81249409f8a538b8197be5c7576c698af934a76a477584b",
            //    AllowedToSellBelowCenterCost = true,
            //    AllowedToSellBelowCompanyCost = true,
            //    ExchangeRuleMasterID = "18352a4e353d449b9b7b465dfb443ca3cb3e8a1ac80b442f84d",
            //    ExchangeRuleDetailType = default
            //};

            //// Act
            //var serviceResult = await _exchangeRuleDetailsAppService.CreateAsync(input);

            //// Assert
            //var result = await _exchangeRuleDetailRepository.FindAsync(c => c.Id == serviceResult.Id);

            //result.ShouldNotBe(null);
            //result.CurrencyName.ShouldBe("141161f92dfa");
            //result.CurrencyID.ShouldBe("02df6ee9e2d64be0b9052d264e72fdab48e191685e7e45c78168");
            //result.AllowedToBuy.ShouldBe(true);
            //result.MinAmountToBuy.ShouldBe(*********);
            //result.MaxAmountToBuy.ShouldBe(1918290901);
            //result.MaxDailyAmountToBuy.ShouldBe(1138296887);
            //result.AllowedToSell.ShouldBe(true);
            //result.MinAmountToSell.ShouldBe(*********);
            //result.MaxAmountToSell.ShouldBe(1772732556);
            //result.MaxDailyAmountToSell.ShouldBe("2c7cfb72559d4744b01af740aef875f69ba67d9eb81249409f8a538b8197be5c7576c698af934a76a477584b");
            //result.AllowedToSellBelowCenterCost.ShouldBe(true);
            //result.AllowedToSellBelowCompanyCost.ShouldBe(true);
            //result.ExchangeRuleMasterID.ShouldBe("18352a4e353d449b9b7b465dfb443ca3cb3e8a1ac80b442f84d");
            //result.ExchangeRuleDetailType.ShouldBe(default);
        }

        [Fact]
        public async Task UpdateAsync()
        {
            // Arrange
            //var input = new ExchangeRuleDetailUpdateDto()
            //{
            //    CurrencyName = "f24caccb51a542178fb2aa59b30861d5991234d6f37f",
            //    CurrencyID = "7ad2870c1ea649178e661a03b497727ca7d53ab27305483899a5e09",
            //    AllowedToBuy = true,
            //    MinAmountToBuy = *********,
            //    MaxAmountToBuy = *********,
            //    MaxDailyAmountToBuy = 1395463851,
            //    AllowedToSell = true,
            //    MinAmountToSell = *********,
            //    MaxAmountToSell = *********,
            //    MaxDailyAmountToSell = "3388718c5f28448680b656221c1974cdaf6ed4a3",
            //    AllowedToSellBelowCenterCost = true,
            //    AllowedToSellBelowCompanyCost = true,
            //    ExchangeRuleMasterID = "c88f5bc238ef4c7584a43b2301b6e",
            //    ExchangeRuleDetailType = default
            //};

            //// Act
            //var serviceResult = await _exchangeRuleDetailsAppService.UpdateAsync(Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"), input);

            //// Assert
            //var result = await _exchangeRuleDetailRepository.FindAsync(c => c.Id == serviceResult.Id);

            //result.ShouldNotBe(null);
            //result.CurrencyName.ShouldBe("f24caccb51a542178fb2aa59b30861d5991234d6f37f");
            //result.CurrencyID.ShouldBe("7ad2870c1ea649178e661a03b497727ca7d53ab27305483899a5e09");
            //result.AllowedToBuy.ShouldBe(true);
            //result.MinAmountToBuy.ShouldBe(*********);
            //result.MaxAmountToBuy.ShouldBe(*********);
            //result.MaxDailyAmountToBuy.ShouldBe(1395463851);
            //result.AllowedToSell.ShouldBe(true);
            //result.MinAmountToSell.ShouldBe(*********);
            //result.MaxAmountToSell.ShouldBe(*********);
            //result.MaxDailyAmountToSell.ShouldBe("3388718c5f28448680b656221c1974cdaf6ed4a3");
            //result.AllowedToSellBelowCenterCost.ShouldBe(true);
            //result.AllowedToSellBelowCompanyCost.ShouldBe(true);
            //result.ExchangeRuleMasterID.ShouldBe("c88f5bc238ef4c7584a43b2301b6e");
            //result.ExchangeRuleDetailType.ShouldBe(default);
        }

        [Fact]
        public async Task DeleteAsync()
        {
            // Act
            await _exchangeRuleDetailsAppService.DeleteAsync(Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"));

            // Assert
            var result = await _exchangeRuleDetailRepository.FindAsync(c => c.Id == Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"));

            result.ShouldBeNull();
        }
    }
}