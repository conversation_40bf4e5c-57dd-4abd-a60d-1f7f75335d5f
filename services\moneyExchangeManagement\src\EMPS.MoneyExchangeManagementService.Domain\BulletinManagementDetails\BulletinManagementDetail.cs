using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class BulletinManagementDetail : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string CurrencyPair { get; set; }

        public virtual double CashBid { get; set; }

        public virtual double CashAsk { get; set; }

        public virtual double AccountBid { get; set; }

        public virtual double AccountAsk { get; set; }

        public virtual int DisplayOrder { get; set; }
        public Guid BulletinManagementMasterId { get; set; }
        public virtual string? CurrencyBaseCode { get; set; }

        public virtual Guid? CurrencyBaseId { get; set; }
        public virtual string? CurrencyQuoteCode { get; set; }

        public virtual Guid? CurrencyQuoteId { get; set; }

        public BulletinManagementDetail()
        {

        }

        public BulletinManagementDetail(Guid id, Guid bulletinManagementMasterId, string currencyPair, double cashBid, double cashAsk, double accountBid, double accountAsk, int displayOrder)
        {

            Id = id;
            Check.NotNull(currencyPair, nameof(currencyPair));
            CurrencyPair = currencyPair;
            CashBid = cashBid;
            CashAsk = cashAsk;
            AccountBid = accountBid;
            AccountAsk = accountAsk;
            DisplayOrder = displayOrder;
            BulletinManagementMasterId = bulletinManagementMasterId;
        }


    }
}