using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public class PairingRule : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string Name { get; set; }

        public virtual DateTime? EffectiveDate { get; set; }

        [CanBeNull]
        public virtual string? Description { get; set; }

        public virtual bool IsApproved { get; set; }

        public virtual Guid? ApprovedBy { get; set; }

        [CanBeNull]
        public virtual string? ApprovedByName { get; set; }

        public virtual DateTime? ApprovalDateTime { get; set; }

        public virtual bool IsArchived { get; set; }

        public PairingRule()
        {

        }

        public PairingRule(Guid id, string name, string description, bool isApproved, string approvedByName, bool isArchived, DateTime? effectiveDate = null, Guid? approvedBy = null, DateTime? approvalDateTime = null)
        {

            Id = id;
            Check.NotNull(name, nameof(name));
            Name = name;
            Description = description;
            IsApproved = isApproved;
            ApprovedByName = approvedByName;
            IsArchived = isArchived;
            EffectiveDate = effectiveDate;
            ApprovedBy = approvedBy;
            ApprovalDateTime = approvalDateTime;
        }

    }
}