﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class AltercrossRateProps : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BaseCurrencyId",
                table: "CrossRateBulletinDetails");

            migrationBuilder.DropColumn(
                name: "QuoteCurrencyId",
                table: "CrossRateBulletinDetails");

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "CrossRateBulletinDetails",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "PairingFormat",
                table: "CrossRateBulletinDetails",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "CrossRateBulletinDetails");

            migrationBuilder.DropColumn(
                name: "PairingFormat",
                table: "CrossRateBulletinDetails");

            migrationBuilder.AddColumn<Guid>(
                name: "BaseCurrencyId",
                table: "CrossRateBulletinDetails",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "QuoteCurrencyId",
                table: "CrossRateBulletinDetails",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
        }
    }
}
