﻿@attribute [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default)]

@using EMPS.MoneyExchangeManagementService.Permissions
@using EMPS.FinancialManagementService.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator
@using Volo.Abp;
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using EMPS.Shared.Enum.ExchangeRules
@using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
@inherits MoneyExchangeManagementServiceComponentBase
@inject IExchangeRuleDetailsAppService ExchangeRuleDetailsAppService

<LoadingIndicator @bind-Visible="@LoadingVisiblity">
    <Tabs SelectedTab=@TabName RenderMode=TabsRenderMode.LazyReload TabPosition=TabPosition.Top FullWidth="true"
          SelectedTabChanged="@((Name) => {TabName =Name; StateHasChanged();})">
        <Items>
            <Tab Name="InCashTab"><Icon Name="@("fa fa-edit")" /> @L["InCashTab"]</Tab>
            <Tab Name="InAccountTab"><Icon Name="@("fas fa-details")" /> @L["InAccountTab"]</Tab>
        </Items>
        <Content>
            <TabPanel Name="InCashTab">
                @if (ExchangeRuleID != null)
                {
                    @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default).Result == true)
                    {
                        <ExchangeRuleDetails ExchangeRuleID="@ExchangeRuleID" exchangeRuleDetailsType="ExchangeRuleDetailsType.InCash"></ExchangeRuleDetails>
                    }
                }
            </TabPanel>
            <TabPanel Name="InAccountTab">
                @if (ExchangeRuleID != null)
                {
                    @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default).Result == true)
                    {
                        <ExchangeRuleDetails ExchangeRuleID="@ExchangeRuleID" exchangeRuleDetailsType="ExchangeRuleDetailsType.InAccount"></ExchangeRuleDetails>
                    }
                }
            </TabPanel>
        </Content>
    </Tabs>
</LoadingIndicator>
@code {
    [Parameter]
    public string ExchangeRuleID { get; set; }

    public string TabName { get; set; }

    string selectedTab = "InCashTab";
    private bool LoadingVisiblity;

    private Task OnSelectedTabChanged(string name)
    {
        LoadingVisiblity = true;
        selectedTab = name;

        LoadingVisiblity = false;
        return Task.CompletedTask;
    }

    protected override async Task OnInitializedAsync()
    {
        LoadingVisiblity = true;

        base.OnInitialized();
        TabName = selectedTab;
        LoadingVisiblity = false;
    }
}
