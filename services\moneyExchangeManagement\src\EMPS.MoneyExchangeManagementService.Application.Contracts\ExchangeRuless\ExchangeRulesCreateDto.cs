using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulesCreateDto
    {
        [Required]
        public string Name { get; set; }
        public string? Description { get; set; }
        public DateTime ActivationDate { get; set; }
        public ExchangeRulesScope ExchangeRuleScope { get; set; } = ((ExchangeRulesScope[])Enum.GetValues(typeof(ExchangeRulesScope)))[0];
        public virtual bool IsReprintReceiptAllowed { get; set; }

        public virtual double ApprovalLimit { get; set; }

        public virtual double RoundUpFee { get; set; }

    }
}