using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{

    [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Default)]
    public class BulletinManagementDetailsAppService : MoneyExchangeManagementServiceAppService, IBulletinManagementDetailsAppService
    {

        private readonly IBulletinManagementDetailRepository _bulletinManagementDetailRepository;
        private readonly BulletinManagementDetailManager _bulletinManagementDetailManager;
        private readonly IRepository<BulletinManagementMaster, Guid> _bulletinManagementMasterRepository;

        public BulletinManagementDetailsAppService(IBulletinManagementDetailRepository bulletinManagementDetailRepository, BulletinManagementDetailManager bulletinManagementDetailManager, IRepository<BulletinManagementMaster, Guid> bulletinManagementMasterRepository)
        {

            _bulletinManagementDetailRepository = bulletinManagementDetailRepository;
            _bulletinManagementDetailManager = bulletinManagementDetailManager; _bulletinManagementMasterRepository = bulletinManagementMasterRepository;
        }

        public virtual async Task<PagedResultDto<BulletinManagementDetailWithNavigationPropertiesDto>> GetListAsync(GetBulletinManagementDetailsInput input)
        {
            var totalCount = await _bulletinManagementDetailRepository.GetCountAsync(input.FilterText, input.CurrencyPair, input.CashBidMin, input.CashBidMax, input.CashAskMin, input.CashAskMax, input.AccountBidMin, input.AccountBidMax, input.AccountAskMin, input.AccountAskMax, input.DisplayOrderMin, input.DisplayOrderMax, input.BulletinManagementMasterId);
            var items = await _bulletinManagementDetailRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.CurrencyPair, input.CashBidMin, input.CashBidMax, input.CashAskMin, input.CashAskMax, input.AccountBidMin, input.AccountBidMax, input.AccountAskMin, input.AccountAskMax, input.DisplayOrderMin, input.DisplayOrderMax, input.BulletinManagementMasterId, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<BulletinManagementDetailWithNavigationPropertiesDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<BulletinManagementDetailWithNavigationProperties>, List<BulletinManagementDetailWithNavigationPropertiesDto>>(items)
            };
        }

        public virtual async Task<BulletinManagementDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return ObjectMapper.Map<BulletinManagementDetailWithNavigationProperties, BulletinManagementDetailWithNavigationPropertiesDto>
                (await _bulletinManagementDetailRepository.GetWithNavigationPropertiesAsync(id));
        }

        public virtual async Task<BulletinManagementDetailDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<BulletinManagementDetail, BulletinManagementDetailDto>(await _bulletinManagementDetailRepository.GetAsync(id));
        }

        public virtual async Task<PagedResultDto<LookupDto<Guid>>> GetBulletinManagementMasterLookupAsync(LookupRequestDto input)
        {
            var query = (await _bulletinManagementMasterRepository.GetQueryableAsync())
                .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                    x => x.BulletinName != null &&
                         x.BulletinName.Contains(input.Filter));

            var lookupData = await query.PageBy(input.SkipCount, input.MaxResultCount).ToDynamicListAsync<BulletinManagementMaster>();
            var totalCount = query.Count();
            return new PagedResultDto<LookupDto<Guid>>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<BulletinManagementMaster>, List<LookupDto<Guid>>>(lookupData)
            };
        }

        [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _bulletinManagementDetailRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Create)]
        public virtual async Task<BulletinManagementDetailDto> CreateAsync(BulletinManagementDetailCreateDto input)
        {
            if (input.BulletinManagementMasterId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["BulletinManagementMaster"]]);
            }

            var bulletinManagementDetail = await _bulletinManagementDetailManager.CreateAsync(
            input.BulletinManagementMasterId, input.CurrencyPair, input.CashBid, input.CashAsk, input.AccountBid, input.AccountAsk, input.DisplayOrder
            );

            return ObjectMapper.Map<BulletinManagementDetail, BulletinManagementDetailDto>(bulletinManagementDetail);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Edit)]
        public virtual async Task<BulletinManagementDetailDto> UpdateAsync(Guid id, BulletinManagementDetailUpdateDto input)
        {
            if (input.BulletinManagementMasterId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["BulletinManagementMaster"]]);
            }

            var bulletinManagementDetail = await _bulletinManagementDetailManager.UpdateAsync(
            id,
            input.BulletinManagementMasterId, input.CurrencyPair, input.CashBid, input.CashAsk, input.AccountBid, input.AccountAsk, input.DisplayOrder, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<BulletinManagementDetail, BulletinManagementDetailDto>(bulletinManagementDetail);
        }
    }
}