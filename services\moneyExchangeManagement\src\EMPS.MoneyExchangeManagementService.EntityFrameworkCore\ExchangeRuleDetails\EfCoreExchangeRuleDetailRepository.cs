using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class EfCoreExchangeRuleDetailRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, ExchangeRuleDetail, Guid>, IExchangeRuleDetailRepository
    {
        public EfCoreExchangeRuleDetailRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<ExchangeRuleDetail>> GetListAsync(
            Guid exchangeRuleMasterID,
            string filterText,
            ExchangeRuleDetailsType? exchangeRuleDetailType = null,
            string currencyName = null,
            Guid? currencyID = null,
            bool? allowedToBuy = null,
            double? minAmountToBuyMin = null,
            double? minAmountToBuyMax = null,
            double? maxAmountToBuyMin = null,
            double? maxAmountToBuyMax = null,
            double? maxDailyAmountToBuyMin = null,
            double? maxDailyAmountToBuyMax = null,
            bool? allowedToSell = null,
            double? minAmountToSellMin = null,
            double? minAmountToSellMax = null,
            double? maxAmountToSellMin = null,
            double? maxAmountToSellMax = null,
            double? maxDailyAmountToSellMin = null,
            double? maxDailyAmountToSellMax = null,
            bool? allowedToSellBelowCenterCost = null,
            bool? allowedToSellBelowCompanyCost = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), exchangeRuleMasterID ,filterText, exchangeRuleDetailType, currencyName, currencyID, allowedToBuy, minAmountToBuyMin, minAmountToBuyMax, maxAmountToBuyMin, maxAmountToBuyMax, maxDailyAmountToBuyMin, maxDailyAmountToBuyMax, allowedToSell, minAmountToSellMin, minAmountToSellMax, maxAmountToSellMin, maxAmountToSellMax, maxDailyAmountToSellMin, maxDailyAmountToSellMax, allowedToSellBelowCenterCost, allowedToSellBelowCompanyCost, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax, unArchivedByUserId, unArchivedByUserName, unArchivedByDateMin, unArchivedByDateMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? ExchangeRuleDetailConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            Guid exchangeRuleMasterID,
            string filterText,
            ExchangeRuleDetailsType? exchangeRuleDetailType = null,
            string currencyName = null,
            Guid? currencyID = null,
            bool? allowedToBuy = null,
            double? minAmountToBuyMin = null,
            double? minAmountToBuyMax = null,
            double? maxAmountToBuyMin = null,
            double? maxAmountToBuyMax = null,
            double? maxDailyAmountToBuyMin = null,
            double? maxDailyAmountToBuyMax = null,
            bool? allowedToSell = null,
            double? minAmountToSellMin = null,
            double? minAmountToSellMax = null,
            double? maxAmountToSellMin = null,
            double? maxAmountToSellMax = null,
            double? maxDailyAmountToSellMin = null,
            double? maxDailyAmountToSellMax = null,
            bool? allowedToSellBelowCenterCost = null,
            bool? allowedToSellBelowCompanyCost = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), exchangeRuleMasterID, filterText, exchangeRuleDetailType, currencyName, currencyID, allowedToBuy, minAmountToBuyMin, minAmountToBuyMax, maxAmountToBuyMin, maxAmountToBuyMax, maxDailyAmountToBuyMin, maxDailyAmountToBuyMax, allowedToSell, minAmountToSellMin, minAmountToSellMax, maxAmountToSellMin, maxAmountToSellMax, maxDailyAmountToSellMin, maxDailyAmountToSellMax, allowedToSellBelowCenterCost, allowedToSellBelowCompanyCost, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax, unArchivedByUserId, unArchivedByUserName, unArchivedByDateMin, unArchivedByDateMax);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<ExchangeRuleDetail> ApplyFilter(
            IQueryable<ExchangeRuleDetail> query,
            Guid exchangeRuleMasterID,
            string filterText,
            ExchangeRuleDetailsType? exchangeRuleDetailType=null,
            string currencyName = null,
            Guid? currencyID = null,
            bool? allowedToBuy = null,
            double? minAmountToBuyMin = null,
            double? minAmountToBuyMax = null,
            double? maxAmountToBuyMin = null,
            double? maxAmountToBuyMax = null,
            double? maxDailyAmountToBuyMin = null,
            double? maxDailyAmountToBuyMax = null,
            bool? allowedToSell = null,
            double? minAmountToSellMin = null,
            double? minAmountToSellMax = null,
            double? maxAmountToSellMin = null,
            double? maxAmountToSellMax = null,
            double? maxDailyAmountToSellMin = null,
            double? maxDailyAmountToSellMax = null,
            bool? allowedToSellBelowCenterCost = null,
            bool? allowedToSellBelowCompanyCost = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.CurrencyName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyName), e => e.CurrencyName.Contains(currencyName))
                    .WhereIf(allowedToBuy.HasValue, e => e.AllowedToBuy == allowedToBuy)
                    .WhereIf(currencyID.HasValue && currencyID != Guid.Empty, e => e.CurrencyID == currencyID)
                    .WhereIf(minAmountToBuyMin.HasValue, e => e.MinAmountToBuy >= minAmountToBuyMin.Value)
                    .WhereIf(minAmountToBuyMax.HasValue, e => e.MinAmountToBuy <= minAmountToBuyMax.Value)
                    .WhereIf(maxAmountToBuyMin.HasValue, e => e.MaxAmountToBuy >= maxAmountToBuyMin.Value)
                    .WhereIf(maxAmountToBuyMax.HasValue, e => e.MaxAmountToBuy <= maxAmountToBuyMax.Value)
                    .WhereIf(maxDailyAmountToBuyMin.HasValue, e => e.MaxDailyAmountToBuy >= maxDailyAmountToBuyMin.Value)
                    .WhereIf(maxDailyAmountToBuyMax.HasValue, e => e.MaxDailyAmountToBuy <= maxDailyAmountToBuyMax.Value)
                    .WhereIf(allowedToSell.HasValue, e => e.AllowedToSell == allowedToSell)
                    .WhereIf(minAmountToSellMin.HasValue, e => e.MinAmountToSell >= minAmountToSellMin.Value)
                    .WhereIf(minAmountToSellMax.HasValue, e => e.MinAmountToSell <= minAmountToSellMax.Value)
                    .WhereIf(maxAmountToSellMin.HasValue, e => e.MaxAmountToSell >= maxAmountToSellMin.Value)
                    .WhereIf(maxAmountToSellMax.HasValue, e => e.MaxAmountToSell <= maxAmountToSellMax.Value)
                    .WhereIf(maxDailyAmountToSellMin.HasValue, e => e.MaxDailyAmountToSell >= maxDailyAmountToSellMin.Value)
                    .WhereIf(maxDailyAmountToSellMax.HasValue, e => e.MaxDailyAmountToSell <= maxDailyAmountToSellMax.Value)
                    .WhereIf(allowedToSellBelowCenterCost.HasValue, e => e.AllowedToSellBelowCenterCost == allowedToSellBelowCenterCost)
                    .WhereIf(allowedToSellBelowCompanyCost.HasValue, e => e.AllowedToSellBelowCompanyCost == allowedToSellBelowCompanyCost)
                    .Where(e => e.ExchangeRuleMasterID == exchangeRuleMasterID)
                    .WhereIf(exchangeRuleDetailType.HasValue, e => e.ExchangeRuleDetailType == exchangeRuleDetailType)
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(approvedByUserId.HasValue, e => e.ApprovedByUserId == approvedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserName), e => e.ApprovedByUserName.Contains(approvedByUserName))
                    .WhereIf(approvedDateTimeMin.HasValue, e => e.ApprovedDateTime >= approvedDateTimeMin.Value)
                    .WhereIf(approvedDateTimeMax.HasValue, e => e.ApprovedDateTime <= approvedDateTimeMax.Value)
                    .WhereIf(isArchived.HasValue, e => e.IsArchived == isArchived)
                    .WhereIf(archivedByUserId.HasValue, e => e.ArchivedByUserId == archivedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(archivedByUserName), e => e.ArchivedByUserName.Contains(archivedByUserName))
                    .WhereIf(archivedDateTimeMin.HasValue, e => e.ArchivedDateTime >= archivedDateTimeMin.Value)
                    .WhereIf(archivedDateTimeMax.HasValue, e => e.ArchivedDateTime <= archivedDateTimeMax.Value)
                    .WhereIf(unArchivedByUserId.HasValue, e => e.UnArchivedByUserId == unArchivedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(unArchivedByUserName), e => e.UnArchivedByUserName.Contains(unArchivedByUserName))
                    .WhereIf(unArchivedByDateMin.HasValue, e => e.UnArchivedByDate >= unArchivedByDateMin.Value)
                    .WhereIf(unArchivedByDateMax.HasValue, e => e.UnArchivedByDate <= unArchivedByDateMax.Value);
        }
    }
}