﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class AddedExchangeRuleDetail : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ExchangeRuleDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CurrencyName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CurrencyID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AllowedToBuy = table.Column<bool>(type: "bit", nullable: false),
                    MinAmountToBuy = table.Column<double>(type: "float", nullable: false),
                    MaxAmountToBuy = table.Column<double>(type: "float", nullable: false),
                    MaxDailyAmountToBuy = table.Column<double>(type: "float", nullable: false),
                    AllowedToSell = table.Column<bool>(type: "bit", nullable: false),
                    MinAmountToSell = table.Column<double>(type: "float", nullable: false),
                    MaxAmountToSell = table.Column<double>(type: "float", nullable: false),
                    MaxDailyAmountToSell = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AllowedToSellBelowCenterCost = table.Column<bool>(type: "bit", nullable: false),
                    AllowedToSellBelowCompanyCost = table.Column<bool>(type: "bit", nullable: false),
                    ExchangeRuleMasterID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExchangeRuleDetailType = table.Column<int>(type: "int", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExchangeRuleDetails", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExchangeRuleDetails");
        }
    }
}
