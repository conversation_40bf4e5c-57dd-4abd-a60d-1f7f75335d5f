using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace EMPS.MoneyExchangeManagementService.Controllers.CrossRateBulletin
{
    [RemoteService]
    [Area("MoneyExchangeManagementService")]
    [ControllerName("CrossRateBulletinDetail")]
    [Route("api/money-exchange-management-service/cross-rate-bulletin-detail")]
    public class CrossRateBulletinDetailController : AbpController, ICrossRateBulletinDetailAppService
    {
        private readonly ICrossRateBulletinDetailAppService _appService;

        public CrossRateBulletinDetailController(ICrossRateBulletinDetailAppService appService)
        {
            _appService = appService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<CrossRateBulletinDetailDto>> GetListAsync(GetCrossRateBulletinDetailListDto input)
        {
            return _appService.GetListAsync(input);
        }

        [HttpGet("{id}")]
        public virtual Task<CrossRateBulletinDetailDto> GetAsync(Guid id)
        {
            return _appService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<CrossRateBulletinDetailDto> CreateAsync(CreateUpdateCrossRateBulletinDetailDto input)
        {
            return _appService.CreateAsync(input);
        }

        [HttpPut("{id}")]
        public virtual Task<CrossRateBulletinDetailDto> UpdateAsync(Guid id, CreateUpdateCrossRateBulletinDetailDto input)
        {
            return _appService.UpdateAsync(id, input);
        }

        [HttpDelete("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _appService.DeleteAsync(id);
        }

        [HttpGet("by-master/{masterId}")]
        public Task<ListResultDto<CrossRateBulletinDetailDto>> GetListByMasterIdAsync(Guid masterId)
        {
            return _appService.GetListByMasterIdAsync(masterId);
        }
        [HttpPost("ReFetchDetailsFromProviderAsync/{masterId}")]
        public Task<List<CrossRateBulletinDetailDto>> ReFetchDetailsFromProviderAsync(Guid masterId)
        {
            return _appService.ReFetchDetailsFromProviderAsync(masterId);
        }
    }
}