using EMPS.Shared.Enum;
using System;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class SpreadRuleExcelDto
    {
        public string RuleName { get; set; }
        public DateTime? ActivationDate { get; set; }
        public SpreadRuleScope Scope { get; set; }
        public string? Description { get; set; }
        public bool IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime ApprovedDateTime { get; set; }
        public bool IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime ArchivedDateTime { get; set; }
    }
}