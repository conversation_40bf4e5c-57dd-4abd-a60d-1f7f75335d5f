using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("ExchangeRuleDetail")]
    [Route("api/money-exchange-management-service/exchange-rule-details")]
    public class ExchangeRuleDetailController : AbpController, IExchangeRuleDetailsAppService
    {
        private readonly IExchangeRuleDetailsAppService _exchangeRuleDetailsAppService;

        public ExchangeRuleDetailController(IExchangeRuleDetailsAppService exchangeRuleDetailsAppService)
        {
            _exchangeRuleDetailsAppService = exchangeRuleDetailsAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<ExchangeRuleDetailDto>> GetListAsync(GetExchangeRuleDetailsInput input)
        {
            return _exchangeRuleDetailsAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<ExchangeRuleDetailDto> GetAsync(Guid id)
        {
            return _exchangeRuleDetailsAppService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<ExchangeRuleDetailDto> CreateAsync(ExchangeRuleDetailCreateDto input)
        {
            return _exchangeRuleDetailsAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<ExchangeRuleDetailDto> UpdateAsync(Guid id, ExchangeRuleDetailUpdateDto input)
        {
            return _exchangeRuleDetailsAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _exchangeRuleDetailsAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRuleDetailExcelDownloadDto input)
        {
            return _exchangeRuleDetailsAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _exchangeRuleDetailsAppService.GetDownloadTokenAsync();
        }

        [HttpPut]
        [Route("ApproveAsync/{exchangeRuleMasterID}")]
        public Task ApproveRuleAsync(Guid exchangeRuleMasterID)
        {
            return _exchangeRuleDetailsAppService.ApproveRuleAsync(exchangeRuleMasterID);
        }

        [HttpPut]
        [Route("ArchiveAsync/{exchangeRuleMasterID}")]
        public Task SetArchiveAsync(Guid exchangeRuleMasterID)
        {
            return _exchangeRuleDetailsAppService.SetArchiveAsync(exchangeRuleMasterID);
        }

        [HttpPut]
        [Route("UnArchiveAsync/{exchangeRuleMasterID}")]
        public Task SetUnArchiveAsync(Guid exchangeRuleMasterID)
        {
            return _exchangeRuleDetailsAppService.SetUnArchiveAsync(exchangeRuleMasterID);
        }
        [HttpDelete]
        [Route("DeleteManyAsync/{exchangeRuleMasterID}")]
        public Task DeleteManyAsync(Guid exchangeRuleMasterID)
        {
            throw new NotImplementedException();
        }
    }
}