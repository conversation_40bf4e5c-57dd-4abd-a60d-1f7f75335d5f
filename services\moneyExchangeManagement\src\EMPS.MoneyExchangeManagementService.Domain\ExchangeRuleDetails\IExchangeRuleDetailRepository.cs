using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public interface IExchangeRuleDetailRepository : IRepository<ExchangeRuleDetail, Guid>
    {
        Task<List<ExchangeRuleDetail>> GetListAsync(
            Guid exchangeRuleMasterID,
            string filterText = null,
            ExchangeRuleDetailsType? exchangeRuleDetailType = null,
            string currencyName = null,
            Guid? currencyID = null,
            bool? allowedToBuy = null,
            double? minAmountToBuyMin = null,
            double? minAmountToBuyMax = null,
            double? maxAmountToBuyMin = null,
            double? maxAmountToBuyMax = null,
            double? maxDailyAmountToBuyMin = null,
            double? maxDailyAmountToBuyMax = null,
            bool? allowedToSell = null,
            double? minAmountToSellMin = null,
            double? minAmountToSellMax = null,
            double? maxAmountToSellMin = null,
            double? maxAmountToSellMax = null,
            double? maxDailyAmountToSellMin = null,
            double? maxDailyAmountToSellMax = null,
            bool? allowedToSellBelowCenterCost = null,
            bool? allowedToSellBelowCompanyCost = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            Guid exchangeRuleMasterID,
            string filterText =null,
            ExchangeRuleDetailsType? exchangeRuleDetailType = null,
            string currencyName = null,
            Guid? currencyID = null,
            bool? allowedToBuy = null,
            double? minAmountToBuyMin = null,
            double? minAmountToBuyMax = null,
            double? maxAmountToBuyMin = null,
            double? maxAmountToBuyMax = null,
            double? maxDailyAmountToBuyMin = null,
            double? maxDailyAmountToBuyMax = null,
            bool? allowedToSell = null,
            double? minAmountToSellMin = null,
            double? minAmountToSellMax = null,
            double? maxAmountToSellMin = null,
            double? maxAmountToSellMax = null,
            double? maxDailyAmountToSellMin = null,
            double? maxDailyAmountToSellMax = null,
            bool? allowedToSellBelowCenterCost = null,
            bool? allowedToSellBelowCompanyCost = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            CancellationToken cancellationToken = default);
    }
}