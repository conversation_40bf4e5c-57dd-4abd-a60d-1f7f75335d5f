﻿using Microsoft.Extensions.DependencyInjection;
using EMPS.MoneyExchangeManagementService.Blazor.Menus;
using Volo.Abp.AspNetCore.Components.Web.Theming;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.UI.Navigation;
using EMPS.CompanyService;

namespace EMPS.MoneyExchangeManagementService.Blazor;

[DependsOn(
    typeof(MoneyExchangeManagementServiceApplicationContractsModule),
    typeof(AbpAspNetCoreComponentsWebThemingModule),
    typeof(AbpAutoMapperModule))]

public class MoneyExchangeManagementServiceBlazorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<MoneyExchangeManagementServiceBlazorModule>();

        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddProfile<MoneyExchangeManagementServiceBlazorAutoMapperProfile>(validate: true);
        });

        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new MoneyExchangeManagementServiceMenuContributor());
        });

        Configure<AbpRouterOptions>(options =>
        {
            options.AdditionalAssemblies.Add(typeof(MoneyExchangeManagementServiceBlazorModule).Assembly);
        });
    }
}
