
<Project Sdk="Microsoft.NET.Sdk.Razor">
	<!--Suite code generation requirement:
	Microsoft.NET.Sdk.BlazorWebAssembly / do not remove this line-->

	<Import Project="..\..\..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Volo.Abp.AspNetCore.Components.Web.Theming" Version="7.3.2" />
		<PackageReference Include="Volo.Abp.RemoteServices" Version="7.3.2" />

		<ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Application.Contracts\EMPS.MoneyExchangeManagementService.Application.Contracts.csproj" />
    <!--<ProjectReference Include="..\..\..\company\src\EMPS.CompanyService.Application.Contracts\EMPS.CompanyService.Application.Contracts.csproj" />-->
    <ProjectReference Include="..\..\..\fee\src\EMPS.FeeService.Blazor\EMPS.FeeService.Blazor.csproj" />
    <PackageReference Include="Blazorise.LoadingIndicator" Version="1.4.1" />



    <PackageReference Include="Blazorise.Components" Version="1.4.0" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.RemoteServices" Version="7.3.2" />
    <PackageReference Include="Blazorise.LoadingIndicator" Version="1.4.1" />
    <PackageReference Include="Blazorise.DataGrid" Version="1.4" />
    <PackageReference Include="Blazorise.Snackbar" Version="1.4.1" />
    <ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Enum\EMPS.Shared.Enum.csproj" />
<ProjectReference Include="..\..\..\..\shared\EMPS.ExchangeLineEditor\EMPS.ExchangeLineEditor.csproj" />
<ProjectReference Include="..\..\..\customer\src\EMPS.CustomerService.Blazor\EMPS.CustomerService.Blazor.csproj" />
		<ProjectReference Include="..\..\..\..\shared\EPMS.Shared.NumberToWord\EPMS.Shared.NumberToWord.csproj" />
<ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Agent.HttpApi.Client\EMPS.Shared.Agent.HttpApi.Client.csproj" />
  </ItemGroup>
</Project>

