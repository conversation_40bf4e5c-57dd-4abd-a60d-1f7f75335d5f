@attribute [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default)]
@using EMPS.MoneyExchangeManagementService.PairingRuleDetails
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject IPairingRuleDetailsAppService PairingRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager

@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["PairingRuleDetails"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="PairingRuleDetailSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText" Autofocus="true" Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="PairingRuleDetailSearchForm" Clicked="GetPairingRuleDetailsAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="PairingRuleDetailWithNavigationPropertiesDto" Data="PairingRuleDetailList"
            ReadData="OnDataGridReadAsync" TotalItems="TotalCount" ShowPager="true" Responsive="true"
            PageSize="PageSize">
            <DataGridColumns>

                <DataGridColumn TItem="PairingRuleDetailWithNavigationPropertiesDto" Caption="@L["DisplayOrder"]">
                    <DisplayTemplate Context="context">
                        <div class="flex flex-col items-center space-y-1">
                            <Button Disabled="@(pairingRuleDto?.IsApproved??false)"
                                class="text-blue-500 hover:text-blue-700 w-8 h-8 flex items-center justify-center border border-blue-300 rounded"
                                Clicked="() => UpAPI(context)">
                                <i class="fas fa-arrow-up"></i>
                            </Button>
                            <Button Disabled="@(pairingRuleDto?.IsApproved??false)"
                                class="text-blue-500 hover:text-blue-700 w-8 h-8 flex items-center justify-center border border-blue-300 rounded"
                                Clicked="() => DownAPI(context)">
                                <i class="fas fa-arrow-down"></i>
                            </Button>
                        </div>
                    </DisplayTemplate>
                </DataGridColumn>



                <DataGridColumn TItem="PairingRuleDetailWithNavigationPropertiesDto" Caption="@L["SWITCH"]">
                    <DisplayTemplate Context="context">
                        <div class="flex flex-col items-center space-y-1">
                            <Button Disabled="@(pairingRuleDto?.IsApproved??false)"
                                class="text-gray-500 border border-gray-400 rounded px-2 py-1 hover:text-gray-700 hover:border-gray-600"
                                Clicked="() => SwitchAPI(context)">
                                @L["SWITCH"]
                            </Button>

                        </div>
                    </DisplayTemplate>
                </DataGridColumn>




                <DataGridColumn TItem="PairingRuleDetailWithNavigationPropertiesDto"
                    Field="PairingRuleDetail.PairingFormat" Caption="@L["PairingFormat"]">
                </DataGridColumn>



                <DataGridColumn TItem="PairingRuleDetailWithNavigationPropertiesDto" Field="PairingRule.Name"
                    Caption="@L["PairingRule"]">
                </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreatePairingRuleDetailModal" Closing="@CreatePairingRuleDetailModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreatePairingRuleDetailForm">
            <ModalHeader>
                <ModalTitle>@L["NewPairingRuleDetail"]</ModalTitle>
                <CloseButton Clicked="CloseCreatePairingRuleDetailModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@NewPairingRuleDetailValidations" Mode="ValidationMode.Auto"
                    Model="@NewPairingRuleDetail" ValidateOnLoad="false">




                    <Validation>
                        <Field>
                            <FieldLabel>@L["BaseCode"] *</FieldLabel>
                            <TextEdit @bind-Text="@NewPairingRuleDetail.BaseCode">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["QuoteCode"] *</FieldLabel>
                            <TextEdit @bind-Text="@NewPairingRuleDetail.QuoteCode">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["PairingFormat"]</FieldLabel>
                            <TextEdit @bind-Text="@NewPairingRuleDetail.PairingFormat">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>



                    <Field>
                        <FieldLabel>@L["PairingRule"]</FieldLabel>
                        <Select TValue="Guid" @bind-SelectedValue="@NewPairingRuleDetail.PairingRuleId">
                            @foreach (var pairingRule in PairingRulesCollection)
                            {
                                <SelectItem TValue="Guid" Value="@pairingRule.Id">
                                    @pairingRule.DisplayName
                                </SelectItem>
                            }
                        </Select>
                    </Field>




                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary" Clicked="CloseCreatePairingRuleDetailModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreatePairingRuleDetailForm" Clicked="CreatePairingRuleDetailAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditPairingRuleDetailModal" Closing="@EditPairingRuleDetailModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditPairingRuleDetailForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditPairingRuleDetailModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingPairingRuleDetailValidations" Mode="ValidationMode.Auto"
                    Model="@EditingPairingRuleDetail" ValidateOnLoad="false">




                    <Validation>
                        <Field>
                            <FieldLabel>@L["BaseCode"] *</FieldLabel>
                            <TextEdit @bind-Text="@EditingPairingRuleDetail.BaseCode">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["QuoteCode"] *</FieldLabel>
                            <TextEdit @bind-Text="@EditingPairingRuleDetail.QuoteCode">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["PairingFormat"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingPairingRuleDetail.PairingFormat">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>





                    <Field>
                        <FieldLabel>@L["PairingRule"]</FieldLabel>
                        <Select TValue="Guid" @bind-SelectedValue="@EditingPairingRuleDetail.PairingRuleId">
                            @foreach (var pairingRule in PairingRulesCollection)
                            {
                                <SelectItem TValue="Guid" Value="@pairingRule.Id">
                                    @pairingRule.DisplayName
                                </SelectItem>
                            }
                        </Select>
                    </Field>




                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary" Clicked="CloseEditPairingRuleDetailModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreatePairingRuleDetailForm" Clicked="UpdatePairingRuleDetailAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
