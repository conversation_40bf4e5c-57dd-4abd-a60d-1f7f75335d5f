using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public interface IExchangeRulesRepository : IRepository<ExchangeRules, Guid>
    {
        Task<List<ExchangeRules>> GetListAsync(
            string filterText = null,
            string name = null,
            string description = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            ExchangeRulesScope? exchangeRuleScope = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string name = null,
            string description = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            ExchangeRulesScope? exchangeRuleScope = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            CancellationToken cancellationToken = default);
    }
}