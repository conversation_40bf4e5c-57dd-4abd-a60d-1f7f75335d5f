using EMPS.Shared.Enum.ExchangeRules;
using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class GetExchangeRulessInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? Name { get; set; }
        public string? Description { get; set; }
        public DateTime? ActivationDateMin { get; set; }
        public DateTime? ActivationDateMax { get; set; }
        public ExchangeRulesScope? ExchangeRuleScope { get; set; }
        public bool? IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime? ApprovedDateTimeMin { get; set; }
        public DateTime? ApprovedDateTimeMax { get; set; }
        public bool? IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime? ArchivedDateTimeMin { get; set; }
        public DateTime? ArchivedDateTimeMax { get; set; }
        public Guid? UnArchivedByUserId { get; set; }
        public string? UnArchivedByUserName { get; set; }
        public DateTime? UnArchivedByDateMin { get; set; }
        public DateTime? UnArchivedByDateMax { get; set; }
        public virtual bool IsReprintReceiptAllowed { get; set; }
        public virtual double ApprovalLimit { get; set; }

        public virtual double RoundUpFee { get; set; }
        public GetExchangeRulessInput()
        {

        }
    }
}