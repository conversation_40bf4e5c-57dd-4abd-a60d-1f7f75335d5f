using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.CompanyService.Currencies;
using Volo.Abp.Uow;
using EMPS.Shared.Enum.ExchangeRules;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{

    [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default)]
    public class ExchangeRulessAppService : ApplicationService, IExchangeRulessAppService
    {
        private readonly IDistributedCache<ExchangeRulesExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly IExchangeRulesRepository _exchangeRulesRepository;
        private readonly ExchangeRulesManager _exchangeRulesManager;
        private readonly IExchangeRuleDetailsAppService _exchangeRuleDetailsAppService;
        private readonly IExchangeRuleDetailRepository _exchangeRuleDetailRepository;
        private readonly ExchangeRuleDetailManager _exchangeRuleDetailManager;
        private readonly ICurrenciesAppService _currenciesAppService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public ExchangeRulessAppService(IExchangeRulesRepository exchangeRulesRepository, ExchangeRulesManager exchangeRulesManager, IDistributedCache<ExchangeRulesExcelDownloadTokenCacheItem, string> excelDownloadTokenCache, IExchangeRuleDetailsAppService exchangeRuleDetailsAppService, IExchangeRuleDetailRepository exchangeRuleDetailRepository, ExchangeRuleDetailManager exchangeRuleDetailManager, ICurrenciesAppService currenciesAppService, IUnitOfWorkManager unitOfWorkManager)
        {
            _excelDownloadTokenCache = excelDownloadTokenCache;
            _exchangeRulesRepository = exchangeRulesRepository;
            _exchangeRulesManager = exchangeRulesManager;
            _exchangeRuleDetailsAppService = exchangeRuleDetailsAppService;
            _exchangeRuleDetailRepository = exchangeRuleDetailRepository;
            _exchangeRuleDetailManager = exchangeRuleDetailManager;
            _currenciesAppService = currenciesAppService;
            _unitOfWorkManager = unitOfWorkManager;
        }

        public virtual async Task<PagedResultDto<ExchangeRulesDto>> GetListAsync(GetExchangeRulessInput input)
        {
            var totalCount = await _exchangeRulesRepository.GetCountAsync(input.FilterText, input.Name, input.Description, input.ActivationDateMin, input.ActivationDateMax, input.ExchangeRuleScope, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax);
            var items = await _exchangeRulesRepository.GetListAsync(input.FilterText, input.Name, input.Description, input.ActivationDateMin, input.ActivationDateMax, input.ExchangeRuleScope, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<ExchangeRulesDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<ExchangeRules>, List<ExchangeRulesDto>>(items)
            };
        }

        public virtual async Task<ExchangeRulesDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(await _exchangeRulesRepository.GetAsync(id));
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            var rule = await _exchangeRulesRepository.GetAsync(id);

            if (rule.IsApproved)
            {
                throw new UserFriendlyException(L["CantDeleteApprovedRule"]);
            }

            await _exchangeRuleDetailsAppService.DeleteManyAsync(id);
            await _exchangeRulesRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Create)]
        public virtual async Task<ExchangeRulesDto> CreateAsync(ExchangeRulesCreateDto input)
        {
            await CheckRuleName(input.Name);
            await CheckApplyDate(input.ActivationDate);

            ExchangeRules exchangeRules;

            // Get list of active currencies
            var currencies = await _currenciesAppService.GetListAsync(new GetCurrenciesInput()
            {
                Status = true,
            });

            using (var unitOfWork = _unitOfWorkManager.Begin(true, true))
            {
                unitOfWork.Failed += async (obj, args) =>
                {
                    // If any part of unit of work failed to execute, rollback will be issued
                    await args.UnitOfWork.RollbackAsync();
                    throw new UserFriendlyException(L["Error trying to CreateAsync"]);
                };

                exchangeRules = await _exchangeRulesManager.CreateAsync(
                    input.Name, input.Description, input.ActivationDate, input.ExchangeRuleScope,input.IsReprintReceiptAllowed
                    ,input.RoundUpFee,input.ApprovalLimit
                );

                // Add rules for new currencies - create both InCash and InAccount types
                foreach (var currency in currencies.Items)
                {
                    if (currency.CurrencyType != CurrencyType.Local)
                    {


                        // Create InCash detail
                        await _exchangeRuleDetailManager.CreateAsync(
                            currencyName: currency.Code,
                            currencyID: currency.Id,
                            allowedToBuy: true,
                            minAmountToBuy: 0,
                            maxAmountToBuy: 0,
                            maxDailyAmountToBuy: 0,
                            allowedToSell: true,
                            minAmountToSell: 0,
                            maxAmountToSell: 0,
                            maxDailyAmountToSell: 0,
                            allowedToSellBelowCenterCost: false,
                            allowedToSellBelowCompanyCost: false,
                            exchangeRuleMasterID: exchangeRules.Id,
                            exchangeRuleDetailType: ExchangeRuleDetailsType.InCash
                        );

                        // Create InAccount detail
                        await _exchangeRuleDetailManager.CreateAsync(
                            currencyName: currency.Code,
                            currencyID: currency.Id,
                            allowedToBuy: true,
                            minAmountToBuy: 0,
                            maxAmountToBuy: 0,
                            maxDailyAmountToBuy: 0,
                            allowedToSell: true,
                            minAmountToSell: 0,
                            maxAmountToSell: 0,
                            maxDailyAmountToSell: 0,
                            allowedToSellBelowCenterCost: false,
                            allowedToSellBelowCompanyCost: false,
                            exchangeRuleMasterID: exchangeRules.Id,
                            exchangeRuleDetailType: ExchangeRuleDetailsType.InAccount
                        );
                    }
                }

                await unitOfWork.SaveChangesAsync();
                await unitOfWork.CompleteAsync();
            }

            return ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(exchangeRules);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Edit)]
        public virtual async Task<ExchangeRulesDto> UpdateAsync(Guid id, ExchangeRulesUpdateDto input)
        {
            var oldRule = await _exchangeRulesRepository.GetAsync(id);

            if (oldRule.IsApproved == true)
            {
                throw new UserFriendlyException(L["CantEditApprovedExchangeRule"]);
            }

            if (oldRule.Name != input.Name)
            {
                await CheckRuleName(input.Name);
            }

            await CheckApplyDate(input.ActivationDate);

            var exchangeRules = await _exchangeRulesManager.UpdateAsync(
                id,
                input.Name, input.Description, input.ActivationDate, input.ExchangeRuleScope,input.IsReprintReceiptAllowed
                , input.RoundUpFee, input.ApprovalLimit,input.ConcurrencyStamp
            );

            return ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(exchangeRules);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRulesExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var items = await _exchangeRulesRepository.GetListAsync(input.FilterText, input.Name, input.Description, input.ActivationDateMin, input.ActivationDateMax, input.ExchangeRuleScope, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax);

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(ObjectMapper.Map<List<ExchangeRules>, List<ExchangeRulesExcelDto>>(items));
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "ExchangeRuless.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new ExchangeRulesExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Approve)]
        public virtual async Task ApproveRuleAsync(Guid id)
        {
            var exchangeRules = await _exchangeRulesRepository.GetAsync(id);

            // Check that activation date is in the future
            if (exchangeRules.ActivationDate <= DateTime.Now)
            {
                throw new UserFriendlyException(L["ActivationDateMustBeInFuture"]);
            }

            exchangeRules.Approve((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            await _exchangeRuleDetailsAppService.ApproveRuleAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Archive)]
        public virtual async Task SetArchiveAsync(Guid id)
        {
            var exchangeRules = await _exchangeRulesRepository.GetAsync(id);

            // Check that rule is approved before archiving
            if (!exchangeRules.IsApproved)
            {
                throw new UserFriendlyException(L["RuleMustBeApprovedBeforeArchiving"]);
            }

            exchangeRules.Archive((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            await _exchangeRuleDetailsAppService.SetArchiveAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.UnArchive)]
        public virtual async Task SetUnArchiveAsync(Guid id)
        {
            var exchangeRules = await _exchangeRulesRepository.GetAsync(id);

            // Check that rule is approved before unarchiving
            if (!exchangeRules.IsApproved)
            {
                throw new UserFriendlyException(L["RuleMustBeApprovedBeforeUnarchiving"]);
            }

            exchangeRules.UnArchive((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            await _exchangeRuleDetailsAppService.SetUnArchiveAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Duplicate)]
        public virtual async Task DuplicateRuleAsync(Guid id)
        {
            var rule = await _exchangeRulesRepository.GetAsync(id);

            string newRuleName = rule.Name + "_Copy";

            await CheckRuleName(newRuleName);

            var ruleDetails = await _exchangeRuleDetailRepository.GetListAsync(id);

            // Get list of active currencies
            var currencies = await _currenciesAppService.GetListAsync(new GetCurrenciesInput()
            {
                Status = true,
            });
            var activeCurrencyIds = currencies.Items.Select(c => c.Id).ToList();

            // Filter rule details to include only those matching active currencies
            var filteredRuleDetails = ruleDetails.Where(rd => activeCurrencyIds.Contains(rd.CurrencyID)).ToList();

            var exchangeRules = await _exchangeRulesManager.CreateAsync(
                newRuleName, rule.Description, rule.ActivationDate, rule.ExchangeRuleScope,rule.IsReprintReceiptAllowed, rule.RoundUpFee, rule.ApprovalLimit
            );

            foreach (var detail in filteredRuleDetails)
            {
                await _exchangeRuleDetailManager.CreateAsync(
                    detail.CurrencyName,
                    detail.CurrencyID,
                    detail.AllowedToBuy,
                    detail.MinAmountToBuy,
                    detail.MaxAmountToBuy,
                    detail.MaxDailyAmountToBuy,
                    detail.AllowedToSell,
                    detail.MinAmountToSell,
                    detail.MaxAmountToSell,
                    detail.MaxDailyAmountToSell,
                    detail.AllowedToSellBelowCenterCost,
                    detail.AllowedToSellBelowCompanyCost,
                    exchangeRules.Id,
                    detail.ExchangeRuleDetailType
                );
            }

            // Handle missing currencies - need to check for both InCash and InAccount types
            var existingCurrencyTypeCombo = ruleDetails.Select(rd => new { rd.CurrencyID, rd.ExchangeRuleDetailType }).ToList();

            foreach (var currency in currencies.Items)
            {
                if (currency.CurrencyType != CurrencyType.Local)
                {


                    // Check if InCash type exists for this currency
                    if (!existingCurrencyTypeCombo.Any(x => x.CurrencyID == currency.Id && x.ExchangeRuleDetailType == ExchangeRuleDetailsType.InCash))
                    {
                        await _exchangeRuleDetailManager.CreateAsync(
                            currencyName: currency.Code,
                            currencyID: currency.Id,
                  
                            allowedToBuy: true,
                            minAmountToBuy: 0,
                            maxAmountToBuy: 0,
                            maxDailyAmountToBuy: 0,
                            allowedToSell: true,
                            minAmountToSell: 0,
                            maxAmountToSell: 0,
                            maxDailyAmountToSell: 0,
                            allowedToSellBelowCenterCost: false,
                            allowedToSellBelowCompanyCost: false,
                            exchangeRuleMasterID: exchangeRules.Id,
                            exchangeRuleDetailType: ExchangeRuleDetailsType.InCash
                        );
                    }

                    // Check if InAccount type exists for this currency
                    if (!existingCurrencyTypeCombo.Any(x => x.CurrencyID == currency.Id && x.ExchangeRuleDetailType == ExchangeRuleDetailsType.InAccount))
                    {
                        await _exchangeRuleDetailManager.CreateAsync(
                            currencyName: currency.Code,
                            currencyID: currency.Id,
                        
                            allowedToBuy: true,
                            minAmountToBuy: 0,
                            maxAmountToBuy: 0,
                            maxDailyAmountToBuy: 0,
                            allowedToSell: true,
                            minAmountToSell: 0,
                            maxAmountToSell: 0,
                            maxDailyAmountToSell: 0,
                            allowedToSellBelowCenterCost: false,
                            allowedToSellBelowCompanyCost: false,
                            exchangeRuleMasterID: exchangeRules.Id,
                            exchangeRuleDetailType: ExchangeRuleDetailsType.InAccount
                        );
                    }
                }
            }
        }

        public async Task<ExchangeRulesDetailWithNavigationPropertiesDto> GetLastEffectiveApprovedExchangeRulessWithNavigation(Guid? exchangeRuleId, Guid currencyId, ExchangeRulesScope scope)
        {
            try
            {
                // If exchangeRuleId is null, return the global rule
                if (!exchangeRuleId.HasValue)
                {
                    return await GetLastEffectiveApprovedGlobalExchangeRule(currencyId);
                }

                // Retrieve exchange rule
                var exchangeRule = await _exchangeRulesRepository.GetAsync(exchangeRuleId.Value);

                // Check if rule is not yet effective or not approved
                if (exchangeRule.ActivationDate > DateTime.Now || !exchangeRule.IsApproved)
                {
                    return await GetLastEffectiveApprovedGlobalExchangeRule(currencyId);
                }

                // Map and return the detailed DTO
                return new ExchangeRulesDetailWithNavigationPropertiesDto
                {
                    ExchangeRulesDto = ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(exchangeRule),
                    ExchangeRuleDetailDto = await GetExchangeRuleDetailDto(exchangeRule.Id, currencyId)
                };
            }
            catch (EntityNotFoundException ex)
            {
                // Provide a user-friendly message
                throw new UserFriendlyException(L["Error:ExchangeRuleNotFound"]);
            }
            catch (Exception ex)
            {
                // Re-throw or handle accordingly
                throw new UserFriendlyException(L["Error:UnexpectedErrorOccurred"], null, ex.Message);
            }
        }

        private async Task<ExchangeRulesDetailWithNavigationPropertiesDto> GetLastEffectiveApprovedGlobalExchangeRule(Guid currencyId)
        {
            // Get the most recent approved global exchange rule
            var globalRules = await _exchangeRulesRepository.GetListAsync(
                exchangeRuleScope: ExchangeRulesScope.global,
                isApproved: true,
                sorting: "ActivationDate desc"
            );

            var effectiveRule = globalRules.FirstOrDefault(r => r.ActivationDate <= DateTime.Now);

            if (effectiveRule == null)
            {
                throw new EntityNotFoundException(L["Error:NoGlobalExchangeRule"]);
            }

            return new ExchangeRulesDetailWithNavigationPropertiesDto
            {
                ExchangeRulesDto = ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(effectiveRule),
                ExchangeRuleDetailDto = await GetExchangeRuleDetailDto(effectiveRule.Id, currencyId)
            };
        }

        public async Task<ExchangeRulesWithAllDetailsDto> GetLastEffectiveApprovedExchangeRulessWithAllDetails(Guid? exchangeRuleId, ExchangeRulesScope scope)
        {
            try
            {
                // If exchangeRuleId is null, return the global rule
                if (!exchangeRuleId.HasValue)
                {
                    return await GetLastEffectiveApprovedGlobalExchangeRuleWithAllDetails();
                }

                // Retrieve exchange rule
                var exchangeRule = await _exchangeRulesRepository.GetAsync(exchangeRuleId.Value);

                // Check if rule is not yet effective or not approved
                if (exchangeRule.ActivationDate > DateTime.Now || !exchangeRule.IsApproved)
                {
                    return await GetLastEffectiveApprovedGlobalExchangeRuleWithAllDetails();
                }

                // Map and return the detailed DTO with all details
                return new ExchangeRulesWithAllDetailsDto
                {
                    ExchangeRulesDto = ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(exchangeRule),
                    ExchangeRuleDetailDtos = await GetAllExchangeRuleDetailDtos(exchangeRule.Id)
                };
            }
            catch (EntityNotFoundException ex)
            {
                // Provide a user-friendly message
                throw new UserFriendlyException(L["Error:ExchangeRuleNotFound"]);
            }
            catch (Exception ex)
            {
                // Re-throw or handle accordingly
                throw new UserFriendlyException(L["Error:UnexpectedErrorOccurred"], null, ex.Message);
            }
        }

        private async Task<ExchangeRulesWithAllDetailsDto> GetLastEffectiveApprovedGlobalExchangeRuleWithAllDetails()
        {
            // Get the most recent approved global exchange rule
            var globalRules = await _exchangeRulesRepository.GetListAsync(
                exchangeRuleScope: ExchangeRulesScope.global,
                isApproved: true,
                sorting: "ActivationDate desc"
            );

            var effectiveRule = globalRules.FirstOrDefault(r => r.ActivationDate <= DateTime.Now);

            if (effectiveRule == null)
            {
                throw new EntityNotFoundException(L["Error:NoGlobalExchangeRule"]);
            }

            return new ExchangeRulesWithAllDetailsDto
            {
                ExchangeRulesDto = ObjectMapper.Map<ExchangeRules, ExchangeRulesDto>(effectiveRule),
                ExchangeRuleDetailDtos = await GetAllExchangeRuleDetailDtos(effectiveRule.Id)
            };
        }

        private async Task<List<ExchangeRuleDetailDto>> GetAllExchangeRuleDetailDtos(Guid exchangeRuleId)
        {
            var exchangeRuleDetails = await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleId);

            if (exchangeRuleDetails == null || !exchangeRuleDetails.Any())
            {
                throw new EntityNotFoundException(L["Error:ExchangeRuleDetailsNotFound"]);
            }

            return ObjectMapper.Map<List<ExchangeRuleDetail>, List<ExchangeRuleDetailDto>>(exchangeRuleDetails);
        }

        private async Task<ExchangeRuleDetailDto> GetExchangeRuleDetailDto(Guid exchangeRuleId, Guid currencyId)
        {
            var exchangeRuleDetail = (await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleId))
                .FirstOrDefault(x => x.CurrencyID == currencyId);

            if (exchangeRuleDetail == null)
            {
                throw new EntityNotFoundException(L["Error:ExchangeRuleDetailNotFound"]);
            }

            return ObjectMapper.Map<ExchangeRuleDetail, ExchangeRuleDetailDto>(exchangeRuleDetail);
        }

        private async Task CheckApplyDate(DateTime activationDate)
        {
            if (activationDate < DateTime.Now)
            {
                throw new UserFriendlyException(L["ActivationDateMustBeInFuture"]);
            }
        }

        private async Task CheckRuleName(string name)
        {
            if (await _exchangeRulesRepository.AnyAsync(x => x.Name == name))
            {
                throw new UserFriendlyException(L["RuleNameAlreadyExist"]);
            }
        }
    }
}