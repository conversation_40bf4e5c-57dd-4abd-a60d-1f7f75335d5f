@page "/SpreadRules"
@attribute [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Default)]
@using EMPS.CompanyService.ServicePoints
@using EMPS.MoneyExchangeManagementService.SpreadRules
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using MPS.Components.ServicePoint
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject ISpreadRulesAppService SpreadRulesAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager
@using EMPS.Shared.Enum
@inject IJSRuntime JSRuntime
@inject IServicePointsAppService ServicePointService

@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["SpreadRules"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@

<Card>
    <CardBody>
        <Form id="SpreadRuleSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText"
                              Autofocus="true"
                              Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="SpreadRuleSearchForm" Clicked="GetSpreadRulesAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
            <Accordion>
                <Collapse Visible="@FilterCollapse">
                    <CollapseHeader>
                        <Heading Size="HeadingSize.Is5">
                            <AccordionToggle>@L["Filter"]</AccordionToggle>
                        </Heading>
                    </CollapseHeader>
                    <CollapseBody>
                        <Div>
                            <RadioGroup TValue="ArchiveFilter" Name="ArchiveStatus" CheckedValue="@RadioArchiveCheckedValue"
                                        CheckedValueChanged="@OnRadioArchiveCheckedValueChanged">

                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.All">@L["All"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.Archived">@L["Archived"]</Radio>
                                <Radio TValue="ArchiveFilter" Group="ArchiveStatus" Value="ArchiveFilter.UnArchive">@L["UnArchived"]</Radio>
                            </RadioGroup>
                        </Div>
                    </CollapseBody>
                </Collapse>

            </Accordion>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="SpreadRuleDto"
                  Data="SpreadRuleList"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  Responsive="true"
                  PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="SpreadRuleDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="SpreadRuleDto" EntityActionsColumn="@EntityActionsColumn">

                            @if (!context.IsApproved)
                            {
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanApproveSpreadRule"
                                              Clicked="async () => await ApproveEntityAsync(context)"
                                              ConfirmationMessage="@(()=> L["ApproveConfirmationMessage"])"
                                              Text="@L["Approve"]"></EntityAction>
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanEditSpreadRule"
                                              Clicked="() => OpenEditSpreadRuleModalAsync(context)"
                                              Text="@L["Edit"]"></EntityAction>
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanDeleteSpreadRule"
                                              Clicked="() => DeleteSpreadRuleAsync(context)"
                                              ConfirmationMessage="@(()=> L["DeleteConfirmationMessage"])"
                                              Text="@L["Delete"]"></EntityAction>
                            }
                            @if (context.IsApproved && !context.IsArchived)
                            {
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanArchiveSpreadRule"
                                              Clicked="async () => await ArchiveEntityAsync(context)"
                                              ConfirmationMessage="@(()=> L["ArchiveConfirmationMessage"])"
                                              Text="@L["Archive"]"></EntityAction>

                            }
                            @if (context.IsApproved && context.IsArchived)
                            {
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanUnArchiveSpreadRule"
                                              Clicked="async () => await UnArchiveEntityAsync(context)"
                                              ConfirmationMessage="@(()=> L["UnArchiveConfirmationMessage"])"
                                              Text="@L["UnArchive"]"></EntityAction>

                            }
                            @if (context.IsApproved && context.Scope != SpreadRuleScope.Global && !context.IsArchived)
                            {
                                <EntityAction TItem="SpreadRuleDto"
                                              Visible="@CanEditSpreadRule"
                                              Clicked="() =>ApplySpreadRuleAsync(context)"
                                              Text="@L["ApplyRule"]"></EntityAction>
                            }
                            <EntityAction TItem="SpreadRuleDto"
                                          Visible="@CanDuplicateSpreadRule"
                                          Clicked="async () => await DuplicateEntityAsync(context)"
                                          ConfirmationMessage="@(()=> L["DuplicateConfirmationMessage"])"
                                          Text="@L["Duplicate"]"></EntityAction>

                            <EntityAction TItem="SpreadRuleDto"
                                          Visible="@CanEditSpreadRule"
                                          Clicked="@(() => OpenSpreadRuleTabs(context.Id, context.RuleName))"
                                          Text="@L["ManageDetails"]"></EntityAction>
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
               
              <DataGridColumn TItem="SpreadRuleDto"
                      Field="RuleName"
                      Caption="@L["RuleName"]">
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="ActivationDate"
                      Caption="@L["ActivationDate"]">
                  <DisplayTemplate>
                        @(context.ActivationDate.HasValue ? context.ActivationDate.Value : string.Empty)
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="Scope"
                      Caption="@L["Scope"]">
                    <DisplayTemplate>
                        @L[$"Enum:SpreadRuleScope.{context.Scope.ToString("d")}"]
                    </DisplayTemplate>
              </DataGridColumn>


              <DataGridColumn TItem="SpreadRuleDto"
                      Field="Description"
                      Caption="@L["Description"]">
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="IsApproved"
                      Caption="@L["IsApproved"]">
                    <DisplayTemplate>
                        @if (context.IsApproved)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="ApprovedByUserName"
                      Caption="@L["ApprovedByUserName"]">
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="ApprovedDateTime"
                      Caption="@L["ApprovedDateTime"]">
                  <DisplayTemplate>
                        @context.ApprovedDateTime?.ToShortDateString()
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="IsArchived"
                      Caption="@L["IsArchived"]">
                    <DisplayTemplate>
                        @if (context.IsArchived)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="ArchivedByUserName"
                      Caption="@L["ArchivedByUserName"]">
              </DataGridColumn>

              <DataGridColumn TItem="SpreadRuleDto"
                      Field="ArchivedDateTime"
                      Caption="@L["ArchivedDateTime"]">
                  <DisplayTemplate>
                        @context.ArchivedDateTime?.ToShortDateString()
                  </DisplayTemplate>
              </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreateSpreadRuleModal" Closing="@CreateSpreadRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreateSpreadRuleForm">
            <ModalHeader>
                <ModalTitle>@L["NewSpreadRule"]</ModalTitle>
                <CloseButton Clicked="CloseCreateSpreadRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@NewSpreadRuleValidations"
                            Mode="ValidationMode.Auto"
                            Model="@NewSpreadRule"
                            ValidateOnLoad="false">
                     
                    
                    <Validation>
                        <Field>
                            <FieldLabel>@L["RuleName"] *</FieldLabel>
                            <TextEdit @bind-Text="@NewSpreadRule.RuleName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ActivationDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewSpreadRule.ActivationDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <FieldLabel>@L["Scope"]</FieldLabel>
                        <Select TValue="SpreadRuleScope" @bind-SelectedValue="@NewSpreadRule.Scope" >
                            @foreach (var itemValue in Enum.GetValues(typeof(SpreadRuleScope)))
                            {
                                <SelectItem TValue="SpreadRuleScope" Value="@((SpreadRuleScope) itemValue)">
                                    @L[$"Enum:SpreadRuleScope.{((SpreadRuleScope) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <MemoEdit @bind-Text="@NewSpreadRule.Description"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </MemoEdit>
                        </Field>
                    </Validation>

@* 
                    <Field>
                        <Check TValue="bool" @bind-Checked="@NewSpreadRule.IsApproved" >@L["IsApproved"]</Check>
                    </Field>
 *@
            
                    
                    
                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseCreateSpreadRuleModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreateSpreadRuleForm" Clicked="CreateSpreadRuleAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditSpreadRuleModal" Closing="@EditSpreadRuleModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditSpreadRuleForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditSpreadRuleModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingSpreadRuleValidations"
                            Mode="ValidationMode.Auto"
                            Model="@EditingSpreadRule"
                            ValidateOnLoad="false">
                     
                    
                    <Validation>
                        <Field>
                            <FieldLabel>@L["RuleName"] *</FieldLabel>
                            <TextEdit @bind-Text="@EditingSpreadRule.RuleName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ActivationDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingSpreadRule.ActivationDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <FieldLabel>@L["Scope"]</FieldLabel>
                        <Select TValue="SpreadRuleScope" @bind-SelectedValue="@EditingSpreadRule.Scope" >
                            @foreach (var itemValue in Enum.GetValues(typeof(SpreadRuleScope)))
                            {
                                <SelectItem TValue="SpreadRuleScope" Value="@((SpreadRuleScope) itemValue)">
                                    @L[$"Enum:SpreadRuleScope.{((SpreadRuleScope) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingSpreadRule.Description"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>



                    
                    
                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseEditSpreadRuleModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreateSpreadRuleForm" Clicked="UpdateSpreadRuleAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
<Modal @ref="ApplySpreadRuleOnServicePointModal" Closing="@ApplySpreadRuleOnServicePointModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true" Size="ModalSize.Fullscreen">
        <ModalHeader>
            <ModalTitle>@L["ApplySpreadRuleOnServicePoint"]</ModalTitle>

                <CloseButton />
            </ModalHeader>
            <ModalBody>


                <Row Margin="Margin.Is2.FromBottom">
                    <Column>
                        <Text>@L["SpreadRule"] : @RuleToBeAppliedOnServicePoint.RuleName</Text>

                    </Column>
                </Row>

                <ServicePointsTransferList EndServicePoints="@EndList"
                                                                       StartServicePoints="@StartList"
                                                                       AppliedServiePointsChanged="HandleAppliedCallback"
                                                                       DisappliedServiePointsChanged="HandleDisappliedCallback" />

            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseApplySpreadRuleOnServicePointModalAsync">
                    @L["Close"]
            </Button>
        </ModalFooter>
    </ModalContent>
</Modal>