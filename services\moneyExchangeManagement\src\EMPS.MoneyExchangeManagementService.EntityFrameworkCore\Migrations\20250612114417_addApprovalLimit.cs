﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class addApprovalLimit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsReprintReceiptAllowed",
                table: "ExchangeRuless",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<double>(
                name: "ApprovalLimit",
                table: "ExchangeRuleDetails",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "RoundUpFee",
                table: "ExchangeRuleDetails",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsReprintReceiptAllowed",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ApprovalLimit",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "RoundUpFee",
                table: "ExchangeRuleDetails");
        }
    }
}
