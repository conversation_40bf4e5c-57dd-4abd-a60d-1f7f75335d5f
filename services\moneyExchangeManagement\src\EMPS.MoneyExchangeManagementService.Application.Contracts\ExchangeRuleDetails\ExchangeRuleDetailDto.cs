using EMPS.Shared.Enum.ExchangeRules;
using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public string? CurrencyName { get; set; }
        public Guid CurrencyID { get; set; }
        public bool AllowedToBuy { get; set; }
        public double MinAmountToBuy { get; set; }
        public double MaxAmountToBuy { get; set; }
        public double MaxDailyAmountToBuy { get; set; }
        public bool AllowedToSell { get; set; }
        public double MinAmountToSell { get; set; }
        public double MaxAmountToSell { get; set; }
        public double MaxDailyAmountToSell { get; set; }
        public bool AllowedToSellBelowCenterCost { get; set; }
        public bool AllowedToSellBelowCompanyCost { get; set; }
        public Guid ExchangeRuleMasterID { get; set; }
        public ExchangeRuleDetailsType ExchangeRuleDetailType { get; set; }
        public bool IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime ApprovedDateTime { get; set; }
        public bool IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime ArchivedDateTime { get; set; }
        public Guid? UnArchivedByUserId { get; set; }
        public string? UnArchivedByUserName { get; set; }
        public DateTime? UnArchivedByDate { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}