using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public class GetBulletinManagementMastersInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? BulletinNumber { get; set; }
        public string? BulletinName { get; set; }
        public DateTime? BulletinDateMin { get; set; }
        public DateTime? BulletinDateMax { get; set; }
        public string? Notes { get; set; }
        public string? ServicePointName { get; set; }
        public Guid? ServicePointId { get; set; }
        public string? CurrencyPairingRuleName { get; set; }
        public Guid? CurrencyPairingRuleId { get; set; }
        public string? CrossRateBulletinName { get; set; }
        public Guid? CrossRateBulletinId { get; set; }
        public string? SpreadRuleName { get; set; }
        public Guid? SpreadRuleId { get; set; }
        public string? PublishByUserName { get; set; }
        public Guid? PublishByUserId { get; set; }
        public DateTime? PublishDateMin { get; set; }
        public DateTime? PublishDateMax { get; set; }

        public GetBulletinManagementMastersInput()
        {

        }
    }
}