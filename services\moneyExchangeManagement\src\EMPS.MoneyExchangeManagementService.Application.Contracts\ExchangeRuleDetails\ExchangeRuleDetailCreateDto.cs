using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailCreateDto
    {
        public string? CurrencyName { get; set; }
        public Guid CurrencyID { get; set; }
        public bool AllowedToBuy { get; set; } = true;
        public double MinAmountToBuy { get; set; } = 0;
        public double MaxAmountToBuy { get; set; } = 0;
        public double MaxDailyAmountToBuy { get; set; } = 0;
        public bool AllowedToSell { get; set; } = true;
        public double MinAmountToSell { get; set; } = 0;
        public double MaxAmountToSell { get; set; } = 0;
        public double MaxDailyAmountToSell { get; set; } = 0;
        public bool AllowedToSellBelowCenterCost { get; set; } = true;
        public bool AllowedToSellBelowCompanyCost { get; set; } = true;
        public Guid ExchangeRuleMasterID { get; set; }

        public ExchangeRuleDetailsType ExchangeRuleDetailType { get; set; } = ((ExchangeRuleDetailsType[])Enum.GetValues(typeof(ExchangeRuleDetailsType)))[0];
    }
}