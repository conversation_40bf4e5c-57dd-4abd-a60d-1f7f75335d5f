using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public class BulletinManagementMasterUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string BulletinNumber { get; set; }
        [Required]
        public string BulletinName { get; set; }
        public DateTime BulletinDate { get; set; }
        public string? Notes { get; set; }
        public string? ServicePointName { get; set; }
        public Guid? ServicePointId { get; set; }
        public string? CurrencyPairingRuleName { get; set; }
        public Guid? CurrencyPairingRuleId { get; set; }
        public string? CrossRateBulletinName { get; set; }
        public Guid? CrossRateBulletinId { get; set; }
        public string? SpreadRuleName { get; set; }
        public Guid? SpreadRuleId { get; set; }
        public string? PublishByUserName { get; set; }
        public Guid? PublishByUserId { get; set; }
        public DateTime? PublishDate { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}