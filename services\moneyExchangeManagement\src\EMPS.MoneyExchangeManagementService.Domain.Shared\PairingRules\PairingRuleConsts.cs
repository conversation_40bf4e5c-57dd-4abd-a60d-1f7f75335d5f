namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public static class PairingRuleConsts
    {
        private const string DefaultSorting = "{0}Name asc";

        public static string GetDefaultSorting(bool withEntityName)
        {
            return string.Format(DefaultSorting, withEntityName ? "PairingRule." : string.Empty);
        }

        public static string UsdCode = "USD";
        public static string SypCode = "SYP";

    }
}