﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class updateCrossForgainKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_CrossRateBulletinDetails_CrossRateBulletinMasterId",
                table: "CrossRateBulletinDetails",
                column: "CrossRateBulletinMasterId");

            migrationBuilder.AddForeignKey(
                name: "FK_CrossRateBulletinDetails_CrossRateBulletinMasters_CrossRateBulletinMasterId",
                table: "CrossRateBulletinDetails",
                column: "CrossRateBulletinMasterId",
                principalTable: "CrossRateBulletinMasters",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CrossRateBulletinDetails_CrossRateBulletinMasters_CrossRateBulletinMasterId",
                table: "CrossRateBulletinDetails");

            migrationBuilder.DropIndex(
                name: "IX_CrossRateBulletinDetails_CrossRateBulletinMasterId",
                table: "CrossRateBulletinDetails");
        }
    }
}
