using EMPS.MoneyExchangeManagementService.Permissions;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Generic;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Localization;
using EMPS.MoneyExchangeManagementService.Permissions;
using Volo.Abp.UI.Navigation;

namespace EMPS.MoneyExchangeManagementService.Blazor.Menus;

public class MoneyExchangeManagementServiceMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }

        var moduleMenu = AddModuleMenuItem(context);

        AddCrossRateBulletinMasterMenu(context, moduleMenu);
        AddMenuItemSpreadRule(context, moduleMenu);


        AddMenuItemBulletinManagement(context, moduleMenu);


        AddMenuItemPairingRules(context, moduleMenu);

        AddMenuItemExchangeRuless(context, moduleMenu);
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {

        return Task.CompletedTask;
    }

    private static void AddCrossRateBulletinMasterMenu(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        var l = context.GetLocalizer<MoneyExchangeManagementServiceResource>();
        parentMenu.AddItem(
            new ApplicationMenuItem(
                MoneyExchangeManagementServiceMenus.CrossRateBulletinMaster,
                l["CrossRateBulletinMaster"],
                "/cross-rate-bulletin-master",
                requiredPermissionName: MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default
            )
        );
    }



    private static ApplicationMenuItem AddModuleMenuItem(MenuConfigurationContext context)
    {
        var moduleMenu = new ApplicationMenuItem(
            MoneyExchangeManagementServiceMenus.Prefix,
            context.GetLocalizer<MoneyExchangeManagementServiceResource>()["Menu:MoneyExchangeManagementService"],
            icon: "fa fa-folder"
        );

        context.Menu.Items.AddIfNotContains(moduleMenu);
        return moduleMenu;
    }
    private static void AddMenuItemPairingRules(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeManagementServiceMenus.PairingRules,
                context.GetLocalizer<MoneyExchangeManagementServiceResource>()["Menu:PairingRules"],
                "/PairingRules",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeManagementServicePermissions.PairingRules.Default
            )
        );
    }


    private static void AddMenuItemBulletinManagement(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeManagementServiceMenus.BulletinManagement,
                context.GetLocalizer<MoneyExchangeManagementServiceResource>()["Menu:BulletinManagement"],
                "/MoneyExchangeManagementService/BulletinManagement",
                icon: "fa fa-clipboard",
                requiredPermissionName: MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Default
            )
        );
    }



    private void AddMenuItemSpreadRule(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
          new ApplicationMenuItem(
              Menus.MoneyExchangeManagementServiceMenus.SpreadRules,
              context.GetLocalizer<MoneyExchangeManagementServiceResource>()["Menu:SpreadRules"],
              "/SpreadRules",
              icon: "fa fa-file-alt",
              requiredPermissionName: MoneyExchangeManagementServicePermissions.SpreadRules.Default
          )
      );
    }



    private static void AddMenuItemExchangeRuless(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeManagementServiceMenus.ExchangeRuless,
                context.GetLocalizer<MoneyExchangeManagementServiceResource>()["Menu:ExchangeRuless"],
                "/ExchangeRules",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeManagementServicePermissions.ExchangeRuless.Default
            )
        );
    }


}