using System;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    public class CrossRateBulletinMasterDto : IEntityDto<Guid>
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsApproved { get; set; }
        public string? ApprovedByUserName { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public bool IsArchived { get; set; }
        public string? ArchivedByUserName { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public DateTime? ArchivedAt { get; set; }
    }
}