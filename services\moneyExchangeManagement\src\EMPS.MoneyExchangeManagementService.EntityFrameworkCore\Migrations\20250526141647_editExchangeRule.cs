﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class editExchangeRule : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ApprovedByUserId",
                table: "ExchangeRuless",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserName",
                table: "ExchangeRuless",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedDateTime",
                table: "ExchangeRuless",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "ArchivedByUserId",
                table: "ExchangeRuless",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ArchivedByUserName",
                table: "ExchangeRuless",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ArchivedDateTime",
                table: "ExchangeRuless",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsApproved",
                table: "ExchangeRuless",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsArchived",
                table: "ExchangeRuless",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UnArchivedByDate",
                table: "ExchangeRuless",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UnArchivedByUserId",
                table: "ExchangeRuless",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UnArchivedByUserName",
                table: "ExchangeRuless",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AlterColumn<double>(
                name: "MaxDailyAmountToSell",
                table: "ExchangeRuleDetails",
                type: "float",
                nullable: false,
                defaultValue: 0.0,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ExchangeRuleMasterID",
                table: "ExchangeRuleDetails",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "CurrencyID",
                table: "ExchangeRuleDetails",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ApprovedByUserId",
                table: "ExchangeRuleDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserName",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedDateTime",
                table: "ExchangeRuleDetails",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "ArchivedByUserId",
                table: "ExchangeRuleDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ArchivedByUserName",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ArchivedDateTime",
                table: "ExchangeRuleDetails",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsApproved",
                table: "ExchangeRuleDetails",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsArchived",
                table: "ExchangeRuleDetails",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UnArchivedByDate",
                table: "ExchangeRuleDetails",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UnArchivedByUserId",
                table: "ExchangeRuleDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UnArchivedByUserName",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserName",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ApprovedDateTime",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ArchivedByUserId",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ArchivedByUserName",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ArchivedDateTime",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "IsApproved",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "IsArchived",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "UnArchivedByDate",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "UnArchivedByUserId",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "UnArchivedByUserName",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserName",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "ApprovedDateTime",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "ArchivedByUserId",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "ArchivedByUserName",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "ArchivedDateTime",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "IsApproved",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "IsArchived",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "UnArchivedByDate",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "UnArchivedByUserId",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "UnArchivedByUserName",
                table: "ExchangeRuleDetails");

            migrationBuilder.AlterColumn<string>(
                name: "MaxDailyAmountToSell",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<string>(
                name: "ExchangeRuleMasterID",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AlterColumn<string>(
                name: "CurrencyID",
                table: "ExchangeRuleDetails",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");
        }
    }
}
