using EMPS.Shared.Enum;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class EfCoreSpreadRuleDetailRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, SpreadRuleDetail, Guid>, ISpreadRuleDetailRepository
    {
        public EfCoreSpreadRuleDetailRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<SpreadRuleDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            return (await GetDbSetAsync()).Where(b => b.Id == id)
                .Select(spreadRuleDetail => new SpreadRuleDetailWithNavigationProperties
                {
                    SpreadRuleDetail = spreadRuleDetail,
                    SpreadRule = dbContext.Set<SpreadRule>().FirstOrDefault(c => c.Id == spreadRuleDetail.SpreadRuleId)
                }).FirstOrDefault();
        }

        public async Task<List<SpreadRuleDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            Guid? spreadRuleId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, currencyId, currencyCode, type, bidSpreadMin, bidSpreadMax, bidMaxDiscountMin, bidMaxDiscountMax, bidMaxMarkdownMin, bidMaxMarkdownMax, askSpreadMin, askSpreadMax, askMaxDiscountMin, askMaxDiscountMax, askMaxMarkupMin, askMaxMarkupMax, spreadRuleId);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? SpreadRuleDetailConsts.GetDefaultSorting(true) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        protected virtual async Task<IQueryable<SpreadRuleDetailWithNavigationProperties>> GetQueryForNavigationPropertiesAsync()
        {
            return from spreadRuleDetail in (await GetDbSetAsync())
                   join spreadRule in (await GetDbContextAsync()).Set<SpreadRule>() on spreadRuleDetail.SpreadRuleId equals spreadRule.Id into spreadRules
                   from spreadRule in spreadRules.DefaultIfEmpty()
                   select new SpreadRuleDetailWithNavigationProperties
                   {
                       SpreadRuleDetail = spreadRuleDetail,
                       SpreadRule = spreadRule
                   };
        }

        protected virtual IQueryable<SpreadRuleDetailWithNavigationProperties> ApplyFilter(
            IQueryable<SpreadRuleDetailWithNavigationProperties> query,
            string filterText,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            Guid? spreadRuleId = null)
        {
            return query
                .WhereIf(!string.IsNullOrWhiteSpace(filterText), e =>  e.SpreadRuleDetail.CurrencyCode.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyCode), e => e.SpreadRuleDetail.CurrencyCode.Contains(currencyCode))
                    .WhereIf(type.HasValue, e => e.SpreadRuleDetail.Type == type)
                    .WhereIf(bidSpreadMin.HasValue, e => e.SpreadRuleDetail.BidSpread >= bidSpreadMin.Value)
                    .WhereIf(bidSpreadMax.HasValue, e => e.SpreadRuleDetail.BidSpread <= bidSpreadMax.Value)
                    .WhereIf(bidMaxDiscountMin.HasValue, e => e.SpreadRuleDetail.BidMaxDiscount >= bidMaxDiscountMin.Value)
                    .WhereIf(bidMaxDiscountMax.HasValue, e => e.SpreadRuleDetail.BidMaxDiscount <= bidMaxDiscountMax.Value)
                    .WhereIf(bidMaxMarkdownMin.HasValue, e => e.SpreadRuleDetail.BidMaxMarkdown >= bidMaxMarkdownMin.Value)
                    .WhereIf(bidMaxMarkdownMax.HasValue, e => e.SpreadRuleDetail.BidMaxMarkdown <= bidMaxMarkdownMax.Value)
                    .WhereIf(askSpreadMin.HasValue, e => e.SpreadRuleDetail.AskSpread >= askSpreadMin.Value)
                    .WhereIf(askSpreadMax.HasValue, e => e.SpreadRuleDetail.AskSpread <= askSpreadMax.Value)
                    .WhereIf(askMaxDiscountMin.HasValue, e => e.SpreadRuleDetail.AskMaxDiscount >= askMaxDiscountMin.Value)
                    .WhereIf(askMaxDiscountMax.HasValue, e => e.SpreadRuleDetail.AskMaxDiscount <= askMaxDiscountMax.Value)
                    .WhereIf(askMaxMarkupMin.HasValue, e => e.SpreadRuleDetail.AskMaxMarkup >= askMaxMarkupMin.Value)
                    .WhereIf(askMaxMarkupMax.HasValue, e => e.SpreadRuleDetail.AskMaxMarkup <= askMaxMarkupMax.Value)
                    .WhereIf(spreadRuleId != null && spreadRuleId != Guid.Empty, e => e.SpreadRule != null && e.SpreadRule.Id == spreadRuleId);
        }

        public async Task<List<SpreadRuleDetail>> GetListAsync(
            string filterText = null,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, currencyId, currencyCode, type, bidSpreadMin, bidSpreadMax, bidMaxDiscountMin, bidMaxDiscountMax, bidMaxMarkdownMin, bidMaxMarkdownMax, askSpreadMin, askSpreadMax, askMaxDiscountMin, askMaxDiscountMax, askMaxMarkupMin, askMaxMarkupMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? SpreadRuleDetailConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            Guid? spreadRuleId = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, currencyId, currencyCode, type, bidSpreadMin, bidSpreadMax, bidMaxDiscountMin, bidMaxDiscountMax, bidMaxMarkdownMin, bidMaxMarkdownMax, askSpreadMin, askSpreadMax, askMaxDiscountMin, askMaxDiscountMax, askMaxMarkupMin, askMaxMarkupMax, spreadRuleId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<SpreadRuleDetail> ApplyFilter(
            IQueryable<SpreadRuleDetail> query,
            string filterText,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e =>  e.CurrencyCode.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyCode), e => e.CurrencyCode.Contains(currencyCode))
                    .WhereIf(type.HasValue, e => e.Type == type)
                    .WhereIf(bidSpreadMin.HasValue, e => e.BidSpread >= bidSpreadMin.Value)
                    .WhereIf(bidSpreadMax.HasValue, e => e.BidSpread <= bidSpreadMax.Value)
                    .WhereIf(bidMaxDiscountMin.HasValue, e => e.BidMaxDiscount >= bidMaxDiscountMin.Value)
                    .WhereIf(bidMaxDiscountMax.HasValue, e => e.BidMaxDiscount <= bidMaxDiscountMax.Value)
                    .WhereIf(bidMaxMarkdownMin.HasValue, e => e.BidMaxMarkdown >= bidMaxMarkdownMin.Value)
                    .WhereIf(bidMaxMarkdownMax.HasValue, e => e.BidMaxMarkdown <= bidMaxMarkdownMax.Value)
                    .WhereIf(askSpreadMin.HasValue, e => e.AskSpread >= askSpreadMin.Value)
                    .WhereIf(askSpreadMax.HasValue, e => e.AskSpread <= askSpreadMax.Value)
                    .WhereIf(askMaxDiscountMin.HasValue, e => e.AskMaxDiscount >= askMaxDiscountMin.Value)
                    .WhereIf(askMaxDiscountMax.HasValue, e => e.AskMaxDiscount <= askMaxDiscountMax.Value)
                    .WhereIf(askMaxMarkupMin.HasValue, e => e.AskMaxMarkup >= askMaxMarkupMin.Value)
                    .WhereIf(askMaxMarkupMax.HasValue, e => e.AskMaxMarkup <= askMaxMarkupMax.Value);
        }
    }
}