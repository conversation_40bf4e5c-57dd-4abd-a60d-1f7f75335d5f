using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.Shared;
using Microsoft.JSInterop;
using Volo.Abp;
using EMPS.CustomerService.Permissions;
using EMPS.CompanyService.ServicePoints;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.SpreadRulePages
{
    public partial class SpreadRules
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();
        private IReadOnlyList<SpreadRuleDto> SpreadRuleList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreateSpreadRule { get; set; }
        private bool CanEditSpreadRule { get; set; }
        private bool CanDeleteSpreadRule { get; set; }
        private bool CanArchiveSpreadRule { get; set; }
        private bool CanUnArchiveSpreadRule { get; set; }
        private bool CanApproveSpreadRule { get; set; }
        private bool CanDuplicateSpreadRule { get; set; }
        private SpreadRuleCreateDto NewSpreadRule { get; set; }
        private Validations NewSpreadRuleValidations { get; set; } = new();
        private SpreadRuleUpdateDto EditingSpreadRule { get; set; }
        private Validations EditingSpreadRuleValidations { get; set; } = new();
        private Guid EditingSpreadRuleId { get; set; }
        private Modal CreateSpreadRuleModal { get; set; } = new();
        private Modal EditSpreadRuleModal { get; set; } = new();
        private GetSpreadRulesInput Filter { get; set; }
        private DataGridEntityActionsColumn<SpreadRuleDto> EntityActionsColumn { get; set; } = new();
        protected string SelectedCreateTab = "spreadRule-create-tab";
        protected string SelectedEditTab = "spreadRule-edit-tab";
        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        private enum ArchiveFilter
        {
            All,
            Archived,
            UnArchive
        }
        private ArchiveFilter RadioArchiveCheckedValue = ArchiveFilter.UnArchive;

        bool FilterCollapse = true;







        private Modal ApplySpreadRuleOnServicePointModal { get; set; } = new();

        /// //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        public SpreadRules()
        {
            NewSpreadRule = new SpreadRuleCreateDto();
            EditingSpreadRule = new SpreadRuleUpdateDto();
            Filter = new GetSpreadRulesInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            SpreadRuleList = new List<SpreadRuleDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:SpreadRules"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["ExportToExcel"], async () => { await DownloadAsExcelAsync(); }, IconName.Download);

            Toolbar.AddButton(L["NewSpreadRule"], async () =>
            {
                await OpenCreateSpreadRuleModalAsync();
            }, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.SpreadRules.Create);

            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateSpreadRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Create);
            CanEditSpreadRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Edit);
            CanDeleteSpreadRule = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Delete);
            CanArchiveSpreadRule = await AuthorizationService
              .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Archive);
            CanUnArchiveSpreadRule = await AuthorizationService
                  .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.UnArchive);
            CanApproveSpreadRule = await AuthorizationService
                     .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Approve);
            CanDuplicateSpreadRule = await AuthorizationService
                     .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Duplicate);
        }

        private async Task GetSpreadRulesAsync()
        {
            bool? ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.All) ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.Archived) ArchivedValue = true;
            if (RadioArchiveCheckedValue == ArchiveFilter.UnArchive) ArchivedValue = false;
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;
            Filter.IsArchived = ArchivedValue;

            var result = await SpreadRulesAppService.GetListAsync(Filter);
            SpreadRuleList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetSpreadRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task DownloadAsExcelAsync()
        {
            var token = (await SpreadRulesAppService.GetDownloadTokenAsync()).Token;
            var remoteService = await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("MoneyExchangeManagementService") ??
            await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("Default");
            NavigationManager.NavigateTo($"{remoteService?.BaseUrl.EnsureEndsWith('/') ?? string.Empty}api/liquidity-service/spread-rules/as-excel-file?DownloadToken={token}&FilterText={Filter.FilterText}", forceLoad: true);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<SpreadRuleDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetSpreadRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreateSpreadRuleModalAsync()
        {
            NewSpreadRule = new SpreadRuleCreateDto
            {
                ActivationDate = DateTime.Now,
            };
            await NewSpreadRuleValidations.ClearAll();
            await CreateSpreadRuleModal.Show();
        }

        private async Task CloseCreateSpreadRuleModalAsync()
        {
            NewSpreadRule = new SpreadRuleCreateDto
            {
                ActivationDate = DateTime.Now,
            };
            await CreateSpreadRuleModal.Hide();
        }

        private async Task OpenEditSpreadRuleModalAsync(SpreadRuleDto input)
        {
            var spreadRule = await SpreadRulesAppService.GetAsync(input.Id);

            EditingSpreadRuleId = spreadRule.Id;
            EditingSpreadRule = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(spreadRule);
            await EditingSpreadRuleValidations.ClearAll();
            await EditSpreadRuleModal.Show();
        }

        private async Task DeleteSpreadRuleAsync(SpreadRuleDto input)
        {
                await SpreadRulesAppService.DeleteAsync(input.Id);
                await GetSpreadRulesAsync();
        }

        private async Task CreateSpreadRuleAsync()
        {
            try
            {
                if (await NewSpreadRuleValidations.ValidateAll() == false)
                {
                    return;
                }

                await SpreadRulesAppService.CreateAsync(NewSpreadRule);
                await GetSpreadRulesAsync();
                await CloseCreateSpreadRuleModalAsync();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditSpreadRuleModalAsync()
        {
            await EditSpreadRuleModal.Hide();
        }

        private async Task UpdateSpreadRuleAsync()
        {
            try
            {
                if (await EditingSpreadRuleValidations.ValidateAll() == false)
                {
                    return;
                }

                await SpreadRulesAppService.UpdateAsync(EditingSpreadRuleId, EditingSpreadRule);
                await GetSpreadRulesAsync();
                await EditSpreadRuleModal.Hide();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private void OnSelectedCreateTabChanged(string name)
        {
            SelectedCreateTab = name;
        }

        private void OnSelectedEditTabChanged(string name)
        {
            SelectedEditTab = name;
        }
        ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


        private async Task OnRadioArchiveCheckedValueChanged(ArchiveFilter value)
        {
            RadioArchiveCheckedValue = value;
            await GetSpreadRulesAsync();
            StateHasChanged();

        }
        private async Task ApproveEntityAsync(SpreadRuleDto input)
        {
            try
            {
                var SpreadRule = await SpreadRulesAppService.GetAsync(input.Id);
                if (SpreadRule == null)
                    throw new UserFriendlyException(@L["ThisSpreadRuleNotFound"]);
                if (SpreadRule.IsApproved == true)
                    throw new UserFriendlyException(@L["ThisSpreadRuleAlreadyApproved"]);
                EditingSpreadRuleId = input.Id;
                EditingSpreadRule = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRule);
                EditingSpreadRule.ApprovedDateTime = DateTime.Now;
                EditingSpreadRule.ApprovedByUserId = CurrentUser.Id;
                EditingSpreadRule.IsApproved = true;
                EditingSpreadRule.ApprovedByUserName = CurrentUser.Name;
                await SpreadRulesAppService.ApproveAsync(EditingSpreadRuleId, EditingSpreadRule);
                await GetSpreadRulesAsync();
                await UiMessageService.Success(L["SpreadRuleApprovedSuccessfully"]);

                await Task.CompletedTask;
            }
            catch (Exception)
            {
                throw;
            }
        }


        private async Task DuplicateEntityAsync(SpreadRuleDto input)
        {
            try
            {
                var feeRule = await SpreadRulesAppService.GetAsync(input.Id);
                await SpreadRulesAppService.CreateDuplicateAsync(feeRule);
                await GetSpreadRulesAsync();
                await Task.CompletedTask;
            }
            catch (Exception)
            {

                throw;
            }
        }

        private async Task OpenSpreadRuleTabs(Guid id, string name)
        {
            string url = $"/SpreadRuleTabs/{id}/{name}";
            await JSRuntime.InvokeVoidAsync("window.open", url, "_blank");



        }


        private async Task ArchiveEntityAsync(SpreadRuleDto input)
        {

            var SpreadRule = await SpreadRulesAppService.GetAsync(input.Id);
            if (SpreadRule == null)
                throw new UserFriendlyException("ThisSpreadRuleNotFound");
            if (!SpreadRule.IsApproved)
                throw new UserFriendlyException("ThisSpreadRuleNotApproved");
            EditingSpreadRuleId = input.Id;
            EditingSpreadRule = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRule);
            EditingSpreadRule.IsArchived = true;

            EditingSpreadRule.UnArchivedByUserId = null;
            EditingSpreadRule.UnArchivedByUserName = null;
            EditingSpreadRule.UnArchivedByDate = null;


            EditingSpreadRule.ArchivedByUserId = CurrentUser.Id;
            EditingSpreadRule.ArchivedByUserName = CurrentUser.UserName;
            EditingSpreadRule.ArchivedDateTime = DateTime.Now;
            await SpreadRulesAppService.ArchiveAsync(EditingSpreadRuleId, EditingSpreadRule);
            await GetSpreadRulesAsync();
            StateHasChanged();
            await Task.CompletedTask;

        }
        private async Task UnArchiveEntityAsync(SpreadRuleDto input)
        {

            var SpreadRule = await SpreadRulesAppService.GetAsync(input.Id);
            if (SpreadRule == null)
                throw new UserFriendlyException("ThisSpreadRuleNotFound");
            if (!SpreadRule.IsApproved)
                throw new UserFriendlyException("ThisSpreadRuleNotApproved");
            EditingSpreadRuleId = input.Id;
            EditingSpreadRule = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRule);
            EditingSpreadRule.IsArchived = false;

            EditingSpreadRule.ArchivedByUserId = null;
            EditingSpreadRule.ArchivedByUserName = null;
            EditingSpreadRule.ArchivedDateTime = null;

            EditingSpreadRule.UnArchivedByUserId = CurrentUser.Id;
            EditingSpreadRule.UnArchivedByUserName = CurrentUser.UserName;
            EditingSpreadRule.UnArchivedByDate = DateTime.Now;

            await SpreadRulesAppService.UnArchiveAsync(EditingSpreadRuleId, EditingSpreadRule);
            await GetSpreadRulesAsync();
            StateHasChanged();
            await Task.CompletedTask;

        }









        private async Task ApplySpreadRuleAsync(SpreadRuleDto context)
        {
            RuleToBeAppliedOnServicePoint = context;


            var servicePoints = await GetAllServicPointsAsync();

            foreach (var servicePoint in servicePoints)
            {
                Console.WriteLine(servicePoint.ServicePoint.Id);
            }

            StartList = servicePoints.Where(x => x.ServicePoint.SpreadRuleId != context.Id).ToList();

            EndList = servicePoints.Where(x => x.ServicePoint.SpreadRuleId == context.Id).ToList();

            await ApplySpreadRuleOnServicePointModal.Show();
        }

        private async Task CloseApplySpreadRuleOnServicePointModalAsync()
        {
            RuleToBeAppliedOnServicePoint = new SpreadRuleDto();

            await ApplySpreadRuleOnServicePointModal.Hide();

            await GetSpreadRulesAsync();

        }


        private List<ServicePointWithNavigationPropertiesDto> StartList = new List<ServicePointWithNavigationPropertiesDto>();
        private List<ServicePointWithNavigationPropertiesDto> EndList = new List<ServicePointWithNavigationPropertiesDto>();
        private List<ServicePointWithNavigationPropertiesDto> FullList = new List<ServicePointWithNavigationPropertiesDto>();

        private SpreadRuleDto RuleToBeAppliedOnServicePoint { get; set; } = new SpreadRuleDto();

        private async Task<List<ServicePointWithNavigationPropertiesDto>> GetAllServicPointsAsync()
        {
            return await ServicePointService.GetAllWithNavigationAsync();
        }


        private async Task<bool> HandleAppliedCallback(List<ServicePointDto> list)
        {
            var ids = list.Select(x => x.Id).ToList();
            return await ServicePointService.ApplySpreadRuleAsync(RuleToBeAppliedOnServicePoint.Id, ids);
        }

        private async Task<bool> HandleDisappliedCallback(List<ServicePointDto> list)
        {
            var ids = list.Select(x => x.Id).ToList();
            return await ServicePointService.DisapplySpreadRuleAsync(ids);
        }

    }
}
