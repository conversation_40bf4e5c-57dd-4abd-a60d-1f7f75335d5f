using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.Shared.Enum.ExchangeRules;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public interface IExchangeRulessAppService : IApplicationService
    {
        Task<PagedResultDto<ExchangeRulesDto>> GetListAsync(GetExchangeRulessInput input);

        Task<ExchangeRulesDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<ExchangeRulesDto> CreateAsync(ExchangeRulesCreateDto input);

        Task<ExchangeRulesDto> UpdateAsync(Guid id, ExchangeRulesUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRulesExcelDownloadDto input);

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();

        Task ApproveRuleAsync(Guid id);

        Task SetArchiveAsync(Guid id);

        Task SetUnArchiveAsync(Guid id);

        Task DuplicateRuleAsync(Guid id);

        Task<ExchangeRulesDetailWithNavigationPropertiesDto> GetLastEffectiveApprovedExchangeRulessWithNavigation(Guid? exchangeRuleId, Guid currencyId, ExchangeRulesScope scope);

        Task<ExchangeRulesWithAllDetailsDto> GetLastEffectiveApprovedExchangeRulessWithAllDetails(Guid? exchangeRuleId, ExchangeRulesScope scope);
    }
}