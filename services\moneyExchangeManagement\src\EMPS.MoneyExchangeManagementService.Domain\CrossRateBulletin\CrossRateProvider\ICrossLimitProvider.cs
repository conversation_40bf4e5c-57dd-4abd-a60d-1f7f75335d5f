using System.Collections.Generic;
using System.Threading.Tasks;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// Interface for providers that supply cross rate limit data
    /// </summary>
    public interface ICrossLimitProvider
    {
        /// <summary>
        /// Gets cross limits data from the provider
        /// </summary>
        /// <param name="input">Request parameters for the provider</param>
        /// <returns>Collection of cross limit responses</returns>
        Task<ICollection<CrossLimitProviderResponse>> GetCrossLimitsAsync(CrossLimitProviderRequest input);
    }
}
