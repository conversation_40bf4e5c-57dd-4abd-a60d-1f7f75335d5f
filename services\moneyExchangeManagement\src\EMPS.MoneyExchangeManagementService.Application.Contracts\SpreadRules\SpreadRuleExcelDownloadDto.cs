using EMPS.Shared.Enum;
using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class SpreadRuleExcelDownloadDto
    {
        public string DownloadToken { get; set; }

        public string? FilterText { get; set; }

        public string? RuleName { get; set; }
        public DateTime? ActivationDateMin { get; set; }
        public DateTime? ActivationDateMax { get; set; }
        public SpreadRuleScope? Scope { get; set; }
        public string? Description { get; set; }
        public bool? IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime? ApprovedDateTimeMin { get; set; }
        public DateTime? ApprovedDateTimeMax { get; set; }
        public bool? IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime? ArchivedDateTimeMin { get; set; }
        public DateTime? ArchivedDateTimeMax { get; set; }

        public SpreadRuleExcelDownloadDto()
        {

        }
    }
}