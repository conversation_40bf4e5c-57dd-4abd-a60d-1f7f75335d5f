<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Authorization" Version="7.3.2" />
 
    <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Domain.Shared\EMPS.MoneyExchangeManagementService.Domain.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Enum\EMPS.Shared.Enum.csproj" />

  </ItemGroup>
  <ItemGroup>
    <Folder Include="SpreadRuleDetails\" />
    <Folder Include="SpreadRules\" />
  </ItemGroup>

</Project>
