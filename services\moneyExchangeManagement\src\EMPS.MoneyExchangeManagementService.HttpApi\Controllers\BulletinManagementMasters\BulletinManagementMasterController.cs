using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using System.Collections.Generic;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using EMPS.CompanyService.ServicePoints;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("BulletinManagementMaster")]
    [Route("api/money-exchange-management-service/bulletin-management-masters")]
    public class BulletinManagementMasterController : AbpController, IBulletinManagementMastersAppService
    {
        private readonly IBulletinManagementMastersAppService _bulletinManagementMastersAppService;

        public BulletinManagementMasterController(IBulletinManagementMastersAppService bulletinManagementMastersAppService)
        {
            _bulletinManagementMastersAppService = bulletinManagementMastersAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<BulletinManagementMasterDto>> GetListAsync(GetBulletinManagementMastersInput input)
        {
            return _bulletinManagementMastersAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<BulletinManagementMasterDto> GetAsync(Guid id)
        {
            return _bulletinManagementMastersAppService.GetAsync(id);
        }


        [HttpGet]
        [Route("PreviewBulletin")]
        public Task<List<BulletinManagementDetailDto>> PreviewBulletin(RequestPreviewBulletinDto request)
        {
            return _bulletinManagementMastersAppService.PreviewBulletin(request);
        }
        [HttpPost]
        [Route("Publish")]

        public Task Publish(bool IsGlobal, List<Guid> ServicePointIds)
        {
            return _bulletinManagementMastersAppService.Publish(IsGlobal, ServicePointIds);
        }
        [HttpGet]
        [Route("GetAllServicePointHaveSpreadRuleId")]
        public Task<List<ServicePointDto>> GetAllServicePointHaveSpreadRuleId()
        {
            return _bulletinManagementMastersAppService.GetAllServicePointHaveSpreadRuleId();
        }
        [HttpGet]
        [Route("GetListOfDetailsByMasterId")]
        public Task<List<BulletinManagementDetailDto>> GetListOfDetailsByMasterId(Guid masterId)
        {
            return _bulletinManagementMastersAppService.GetListOfDetailsByMasterId(masterId);
        }
        [HttpGet]
        [Route("GetBulletinManagementWithNavigation")]
        public Task<BulletinManagementMasterDto?> GetBulletinManagementWithNavigation(Guid? servicePointId = null)
        {
            return _bulletinManagementMastersAppService.GetBulletinManagementWithNavigation(servicePointId);
        }
    }
}