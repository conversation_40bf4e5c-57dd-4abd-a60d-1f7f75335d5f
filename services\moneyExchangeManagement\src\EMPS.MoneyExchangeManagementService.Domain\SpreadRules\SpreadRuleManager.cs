using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class SpreadRuleManager : DomainService
    {
        private readonly ISpreadRuleRepository _spreadRuleRepository;

        public SpreadRuleManager(ISpreadRuleRepository spreadRuleRepository)
        {
            _spreadRuleRepository = spreadRuleRepository;
        }

     

        public async Task<SpreadRule> CreateAsync(string ruleName, string? description, SpreadRuleScope scope,
            DateTime? activationDate, bool isApproved, Guid? approvedByUserId, string? approvedByUserName,
            DateTime? approvedDateTime, bool isArchived, Guid? archivedByUserId, string? archivedByUserName,
            Guid? unArchivedByUserId, string? unArchivedByUserName, DateTime? archivedDateTime, DateTime? unArchivedByDate)
        {
            Check.NotNullOrWhiteSpace(ruleName, nameof(ruleName));
            Check.NotNull(scope, nameof(scope));

            var spreadRule = new SpreadRule(
             GuidGenerator.Create(),
             ruleName, description, scope, activationDate,
             isApproved, approvedByUserId, approvedByUserName, approvedDateTime, isArchived, 
             archivedByUserId, archivedByUserName, unArchivedByUserId, unArchivedByUserName,
             archivedDateTime , unArchivedByDate
             );

            return await _spreadRuleRepository.InsertAsync(spreadRule);
        }

        public async Task<SpreadRule> UpdateAsync(
            Guid id, string ruleName, string? description, SpreadRuleScope scope, DateTime? activationDate,
            bool isApproved, Guid? approvedByUserId, string? approvedByUserName, DateTime? approvedDateTime, bool isArchived,
            Guid? archivedByUserId, string? archivedByUserName, Guid? unArchivedByUserId, string? unArchivedByUserName,
            DateTime? archivedDateTime, DateTime? unArchivedByDate,
            [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(ruleName, nameof(ruleName));
            Check.NotNull(scope, nameof(scope));

            var spreadRule = await _spreadRuleRepository.GetAsync(id);

            spreadRule.RuleName = ruleName;
            spreadRule.Scope = scope;
            spreadRule.Description = description;
            spreadRule.IsApproved = isApproved;
            spreadRule.ApprovedByUserId = approvedByUserId;
            spreadRule.ApprovedByUserName = approvedByUserName;
            spreadRule.ApprovedDateTime = approvedDateTime;
            spreadRule.IsArchived = isArchived;
            spreadRule.ArchivedByUserId = archivedByUserId;
            spreadRule.ArchivedByUserName = archivedByUserName;
            spreadRule.ArchivedDateTime = archivedDateTime;
            spreadRule.UnArchivedByUserId = unArchivedByUserId;
            spreadRule.UnArchivedByUserName = unArchivedByUserName;
            spreadRule.UnArchivedByDate = unArchivedByDate;
            spreadRule.ActivationDate = activationDate;

            spreadRule.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _spreadRuleRepository.UpdateAsync(spreadRule);
        }

    
    }
}