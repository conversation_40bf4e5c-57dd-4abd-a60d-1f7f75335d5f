using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public class EfCorePairingRuleRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, PairingRule, Guid>, IPairingRuleRepository
    {
        public EfCorePairingRuleRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<PairingRule>> GetListAsync(
            string filterText = null,
            string name = null,
            DateTime? effectiveDateMin = null,
            DateTime? effectiveDateMax = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedBy = null,
            string approvedByName = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            bool? isArchived = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, name, effectiveDateMin, effectiveDateMax, description, isApproved, approvedBy, approvedByName, approvalDateTimeMin, approvalDateTimeMax, isArchived);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? PairingRuleConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string name = null,
            DateTime? effectiveDateMin = null,
            DateTime? effectiveDateMax = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedBy = null,
            string approvedByName = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            bool? isArchived = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, name, effectiveDateMin, effectiveDateMax, description, isApproved, approvedBy, approvedByName, approvalDateTimeMin, approvalDateTimeMax, isArchived);

            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<PairingRule> ApplyFilter(
            IQueryable<PairingRule> query,
            string filterText,
            string name = null,
            DateTime? effectiveDateMin = null,
            DateTime? effectiveDateMax = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedBy = null,
            string approvedByName = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            bool? isArchived = null)
        {

            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.Name.Contains(filterText) || e.Description.Contains(filterText) || e.ApprovedByName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(name), e => e.Name.Contains(name))
                    .WhereIf(effectiveDateMin.HasValue, e => e.EffectiveDate >= effectiveDateMin.Value)
                    .WhereIf(effectiveDateMax.HasValue, e => e.EffectiveDate <= effectiveDateMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(description), e => e.Description.Contains(description))
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(approvedBy.HasValue, e => e.ApprovedBy == approvedBy)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByName), e => e.ApprovedByName.Contains(approvedByName))
                    .WhereIf(approvalDateTimeMin.HasValue, e => e.ApprovalDateTime >= approvalDateTimeMin.Value)
                    .WhereIf(approvalDateTimeMax.HasValue, e => e.ApprovalDateTime <= approvalDateTimeMax.Value)
                    .WhereIf(isArchived.HasValue, e => e.IsArchived == isArchived);
        }
    }
}