using EMPS.Shared.Enum;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class SpreadRuleUpdateDto : IHasConcurrencyStamp
    {
        [Required]
        public string RuleName { get; set; }
        public DateTime? ActivationDate { get; set; }
        public SpreadRuleScope Scope { get; set; }
        public string? Description { get; set; }
        public bool IsApproved { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public string? ApprovedByUserName { get; set; }
        public DateTime? ApprovedDateTime { get; set; }
        public bool IsArchived { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public string? ArchivedByUserName { get; set; }
        public DateTime? ArchivedDateTime { get; set; }
        public Guid? UnArchivedByUserId { get; set; }
        public string? UnArchivedByUserName { get; set; }
        public DateTime? UnArchivedByDate { get; set; }
        public string ConcurrencyStamp { get; set; }
    }
}