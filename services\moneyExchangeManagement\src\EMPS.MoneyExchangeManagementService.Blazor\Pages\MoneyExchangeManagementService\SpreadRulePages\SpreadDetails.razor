@attribute [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Default)]
@using Blazorise.LoadingIndicator
@using Blazorise.Snackbar
@using EMPS.MoneyExchangeManagementService.SpreadRuleDetails
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp.Application.Dtos
@using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject ISpreadRuleDetailsAppService SpreadRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager
@using EMPS.Shared.Enum
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject IServiceProvider ServiceProvider
<SnackbarStack @ref="snackbarStack" Location="SnackbarStackLocation.Bottom" Color="SnackbarColor.Primary" />
<LoadingIndicator @bind-Visible="@visible">


    <PageHeader Title="@(L["SpreadRuleDetails"]+ " : "+ @SpreadRuleName)" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

    </PageHeader>

    <Card>
        <CardBody>
            <Form id="SpreadRuleDetailSearchForm" class="mb-3">
                <Addons>
                    <Addon AddonType="AddonType.Body">
                        <TextEdit @bind-Text="@Filter.FilterText"
                                  Placeholder="@L["Search"]">
                        </TextEdit>
                    </Addon>
                    <Addon AddonType="AddonType.End">
                        <SubmitButton Form="SpreadRuleDetailSearchForm" Clicked="GetSpreadRuleDetailsAsync">
                            <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                        </SubmitButton>
                    </Addon>
                </Addons>
            </Form>
        </CardBody>
    </Card>


<style>


    .form-group, .form-control, .form-select, .form-label {
        margin-bottom: 5px;
        font-size: 8pt;
        height: 40px;
        text-align: right;
        font-weight: bold;              
        width: 100%;
        padding: 0 !important;
    }

        .card .card-body {
             padding: 0 !important; 
        }

        .form-control:hover, .form-control:focus, .form-select:hover, .form-select:focus {
            box-shadow: aliceblue;
            border-color: #032b4d;
            color: #043e68;
        }

    .b-table.table tbody th, .b-table.table tbody td {
        padding: 0.5rem 0.2rem !important;
        vertical-align: middle;
    }

        .text-danger small {
            font-size: 0.85rem;
        }

</style>



    <Form Model="@SpreadRuleDetail">
    <Validations @ref="@SpreadRuleDetailsValidations"
                 Mode="ValidationMode.Auto"
                     Model="@SpreadRuleDetailList"
                 ValidateOnLoad="false">



  <Card>
            <CardBody>
                <Table>
                        <TableHeader ThemeContrast="ThemeContrast.Light">
                        <TableRow>
                                <TableHeaderCell>@L["CurrencyCode"]</TableHeaderCell>
                                <TableHeaderCell>@L["BidSpread"]</TableHeaderCell>
                                <TableHeaderCell>@L["BidMaxDiscount"]</TableHeaderCell>
                                <TableHeaderCell>@L["BidMaxMarkdown"]</TableHeaderCell>
                                <TableHeaderCell>@L["AskSpread"]</TableHeaderCell>
                                <TableHeaderCell>@L["AskMaxDiscount"]</TableHeaderCell>
                                <TableHeaderCell>@L["AskMaxMarkup"]</TableHeaderCell>
                        </TableRow>
                    </TableHeader>

                        <TableBody>
                            @{


                                @foreach (var rowWithIndex in SpreadRuleDetailList.Select((row, index) => new { row, index }))
                                {
                                    var row = rowWithIndex.row;
                                    var isFirst = rowWithIndex.index == 0;

                                    <TableRow>
                                        <TableRowCell>
                                            <TextEdit id="@(isFirst ? "CurrencyCodeId" : null)"
                                                      Text="@row.SpreadRuleDetail.CurrencyCode"
                                                      @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                      ReadOnly Autofocus="@isFirst">
                                            </TextEdit>
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.BidSpread"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.BidSpread))"
                                                           Style="@GetInputStyle(row.IsBidSpreadInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.BidMaxDiscount"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.BidMaxDiscount))"
                                                           Style="@GetInputStyle(row.IsBidMaxDiscountInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.BidMaxMarkdown"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.BidMaxMarkdown))"
                                                           Style="@GetInputStyle(row.IsBidMaxMarkdownInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.AskSpread"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.AskSpread))"
                                                           Style="@GetInputStyle(row.IsAskSpreadInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.AskMaxDiscount"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.AskMaxDiscount))"
                                                           Style="@GetInputStyle(row.IsAskMaxDiscountInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>

                                        <TableRowCell>
                                            <NumericPicker TValue="double" ShowStepButtons=false Value="@row.SpreadRuleDetail.AskMaxMarkup"
                                                           @onkeydown="@(async (args) => { await HandleKeyDown(args, row); })"
                                                           @onblur="@(async () => await HandleBlur(row))"
                                                           ValueChanged="val => OnValueChanged(val, row, nameof(row.SpreadRuleDetail.AskMaxMarkup))"
                                                           Style="@GetInputStyle(row.IsAskMaxMarkupInvalid)"
                                                           ReadOnly="@IsApproved" />
                                        </TableRowCell>
                                    </TableRow>

                                    @if (!string.IsNullOrEmpty(row.ValidationMessage))
                                    {
                                        <TableRow>
                                            <TableRowCell colspan="8" style="color: red;">
                                                <small>@row.ValidationMessage</small>
                                            </TableRowCell>
                                        </TableRow>
                                    }
                                }
                            }
                        </TableBody>
                </Table>
            </CardBody>


        </Card>

    </Validations>
</Form>
</LoadingIndicator>