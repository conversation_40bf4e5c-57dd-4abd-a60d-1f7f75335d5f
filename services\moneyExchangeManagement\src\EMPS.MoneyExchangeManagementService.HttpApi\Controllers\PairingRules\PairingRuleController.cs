using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.PairingRules;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("PairingRule")]
    [Route("api/money-exchange-management-service/pairing-rules")]
    public class PairingRuleController : AbpController, IPairingRulesAppService
    {
        private readonly IPairingRulesAppService _pairingRulesAppService;

        public PairingRuleController(IPairingRulesAppService pairingRulesAppService)
        {
            _pairingRulesAppService = pairingRulesAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<PairingRuleDto>> GetListAsync(GetPairingRulesInput input)
        {
            return _pairingRulesAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<PairingRuleDto> GetAsync(Guid id)
        {
            return _pairingRulesAppService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<PairingRuleDto> CreateAsync(PairingRuleCreateDto input)
        {
            return _pairingRulesAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<PairingRuleDto> UpdateAsync(Guid id, PairingRuleUpdateDto input)
        {
            return _pairingRulesAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _pairingRulesAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleExcelDownloadDto input)
        {
            return _pairingRulesAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _pairingRulesAppService.GetDownloadTokenAsync();
        }

        [HttpGet]
        [Route("GetLastEffectiveRule")]
        public Task<PairingWithDetailes> GetLastEffectiveRule()
        {
            return _pairingRulesAppService.GetLastEffectiveRule();
        }

        [HttpPut("archive/{id}")]
        public Task SetArchiveAsync(Guid id)
        {
            return _pairingRulesAppService.SetArchiveAsync(id);
        }
        [HttpPut("unarchive/{id}")]
        public Task SetUnArchiveAsync(Guid id)
        {
            return _pairingRulesAppService.SetUnArchiveAsync(id);
        }
        [HttpPut("approval/{id}")]
        public Task SetApproveAsync(Guid id)
        {
            return _pairingRulesAppService.SetApproveAsync(id);
        }
        [HttpPut]
        [Route("duplicate/{id}")]
        public Task DuplicateRuleAsync(Guid id)
        {
            return _pairingRulesAppService.DuplicateRuleAsync(id);
        }
    }
}