@page "/cross-rate-bulletin-master"
@namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
@attribute [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default)]

@inherits AbpCrudPageBase<
ICrossRateBulletinMasterAppService,
CrossRateBulletinMasterDto,
Guid,
GetCrossRateBulletinMasterListDto,
CreateUpdateCrossRateBulletinMasterDto
>
@using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
@using Volo.Abp.Application.Dtos
@using EMPS.MoneyExchangeManagementService.Permissions
@using Blazorise
@using Volo.Abp.Authorization.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Volo.Abp.AspNetCore.Components.Web.Theming.Toolbars
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout

@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client

<PageHeader Title="@L["CrossRateBulletinMaster"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">
</PageHeader>

@if (CanManageConfiguration)
{
    <Card Class="mb-3">
    <CardHeader>
        <CardTitle>
            <Icon Name="IconName.Edit" Class="me-2"></Icon>
            @L["CrossRateProviderConfiguration"]
        </CardTitle>
    </CardHeader>
    <CardBody>
        @if (IsConfigurationLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else
            {
                <Form>
                    <Row>
                        <Column ColumnSize="ColumnSize.Is4">
                            <Field>
                                <FieldLabel>@L["ProviderBaseUrl"]</FieldLabel>
                                <TextEdit @bind-Text="@ConfigurationInput.ProviderBaseUrl"
                                    Placeholder="https://api.provider.com" />
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is4">
                            <Field>
                                <FieldLabel>@L["ProviderAccessToken"]</FieldLabel>
                                <TextEdit @bind-Text="@ConfigurationInput.ProviderAccessToken"
                                    Placeholder="Enter access token" />
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>@L["RequestBaseCurrency"]</FieldLabel>
                                <TextEdit @bind-Text="@ConfigurationInput.RequestBaseCurrency" Placeholder="USD"
                                    MaxLength="3" />
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>&nbsp;</FieldLabel>
                                <Button Color="Color.Primary" Clicked="SaveConfigurationAsync" Loading="IsConfigurationSaving"
                                    Block="true">
                                    <Icon Name="IconName.Save" Class="me-1"></Icon>
                                    @L["Save"]
                                </Button>
                            </Field>
                        </Column>
                    </Row>
                </Form>
            }
        </CardBody>
    </Card>
}

<Card>
    <CardBody>
        <Form id="FilterForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@GetListInput.FilterText" Autofocus="true" Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="FilterForm" Clicked="GetEntitiesAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
            <RadioGroup TValue="bool?" Name="ArchiveFilter" @bind-CheckedValue="@GetListInput.IsArchived" Class="mt-2">
                <Radio TValue="bool?" Group="ArchiveFilter" Value="null">
                    @L["All"]
                </Radio>
                <Radio TValue="bool?" Group="ArchiveFilter" Value="true">
                    @L["Archived"]
                </Radio>
                <Radio TValue="bool?" Group="ArchiveFilter" Value="false">
                    @L["UnArchived"]
                </Radio>
            </RadioGroup>
        </Form>
    </CardBody>
</Card>

<Card>
    <CardBody>
        <DataGrid TItem="CrossRateBulletinMasterDto" Data="@Entities" TotalItems="@TotalCount" ShowPager
            CurrentPage="@CurrentPage" ReadData="OnDataGridReadAsync">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="CrossRateBulletinMasterDto">
                    <DisplayTemplate>
                        <EntityActions TItem="CrossRateBulletinMasterDto">
                            <EntityAction TItem="CrossRateBulletinMasterDto" Visible="true"
                                Clicked="@(() => OpenDetailsModalAsync(context))" Text="@L["ViewDetails"]" />
                            <EntityAction TItem="CrossRateBulletinMasterDto"
                                Visible="@(!context.IsApproved && CanApprove)"
                                Clicked="@(() => ApproveAsync(context.Id))"
                                ConfirmationMessage='()=>L["AreYouSureYouWantToApproveThisItem"]'
                                Text="@L["Approve"]" />
                            <EntityAction TItem="CrossRateBulletinMasterDto"
                                Visible="@(context.IsApproved && !context.IsArchived && CanArchive)"
                                Clicked="@(() => ArchiveAsync(context.Id))" Text="@L["Archive"]" />
                            <EntityAction TItem="CrossRateBulletinMasterDto"
                                Visible="@(context.IsApproved && context.IsArchived && CanArchive)"
                                Clicked="@(() => UnarchiveAsync(context.Id))" Text="@L["Unarchive"]" />
                            <EntityAction TItem="CrossRateBulletinMasterDto"
                                Visible="@(CanCreate && !context.IsApproved)"
                                Clicked="@(() => OpenEditModalAsync(context))" Text="@L["Edit"]" />
                            <EntityAction TItem="CrossRateBulletinMasterDto"
                                Visible="@(CanDelete && !context.IsApproved)"
                                Clicked="@(() => DeleteEntityAsync(context))"
                                ConfirmationMessage='()=>L["AreYouSureYouWantToDeleteThisItem"]' Text="@L["Delete"]" />
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
                <DataGridColumn TItem="CrossRateBulletinMasterDto" Field="@nameof(CrossRateBulletinMasterDto.Name)"
                    Caption="@L["Name"]" />
                <DataGridColumn TItem="CrossRateBulletinMasterDto"
                    Field="@nameof(CrossRateBulletinMasterDto.Description)" Caption="@L["Description"]" />
                <DataGridColumn TItem="CrossRateBulletinMasterDto"
                    Field="@nameof(CrossRateBulletinMasterDto.IsApproved)" Caption="@L["IsApproved"]">
                    <DisplayTemplate>
                        @if (context.IsApproved)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>
                <DataGridColumn TItem="CrossRateBulletinMasterDto"
                    Field="@nameof(CrossRateBulletinMasterDto.ApprovedByUserName)" Caption="@L["ApprovedByUserName"]" />
                <DataGridColumn TItem="CrossRateBulletinMasterDto"
                    Field="@nameof(CrossRateBulletinMasterDto.IsArchived)" Caption="@L["IsArchived"]">
                    <DisplayTemplate>
                        @if (context.IsArchived)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>
            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

<Modal @ref="CreateModal" Closing="@CreateModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreateCrossRateBulletinMasterForm">
            <ModalHeader>
                <ModalTitle>
                    @L["CreateCrossRateBulletinMaster"]
                </ModalTitle>
                <CloseButton Clicked="@(() => CreateModal.Hide())" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="CreateValidationsRef" Model="@NewEntity" ValidateOnLoad="false">
                    <Validation>
                        <Field>
                            <FieldLabel>@L["Name"]</FieldLabel>
                            <TextEdit @bind-Text="NewEntity.Name">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>
                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <MemoEdit @bind-Text="NewEntity.Description">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </MemoEdit>
                        </Field>
                    </Validation>
                </Validations>
            </ModalBody>
            <ModalFooter>

                <SubmitButton Form="CreateCrossRateBulletinMasterForm" Clicked="CreateEntityAsync" />

                <Button Color="Color.Secondary" Clicked="@(() => CreateModal.Hide())">@L["Cancel"]</Button>
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
<Modal @ref="EditModal" Closing="@EditModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditCrossRateBulletinMasterForm">
            <ModalHeader>
                <ModalTitle>
                    @L["EditCrossRateBulletinMaster"]
                </ModalTitle>
                <CloseButton Clicked="@(() => EditModal.Hide())" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="EditValidationsRef" Model="@EditingEntity" ValidateOnLoad="false">
                    <Validation>
                        <Field>
                            <FieldLabel>@L["Name"]</FieldLabel>
                            <TextEdit @bind-Text="EditingEntity.Name">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>
                    <Validation>
                        <Field>
                            <FieldLabel>@L["Description"]</FieldLabel>
                            <MemoEdit @bind-Text="EditingEntity.Description">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </MemoEdit>
                        </Field>
                    </Validation>
                </Validations>
            </ModalBody>
            <ModalFooter>

                <SubmitButton Form="EditCrossRateBulletinMasterForm" Clicked="UpdateEntityAsync" />
                <Button Color="Color.Secondary" Clicked="@(() => EditModal.Hide())">@L["Cancel"]</Button>
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

<Modal @ref="DetailsModal" Closing="@DetailsModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true" Size="ModalSize.Large">
        <ModalHeader>
            <ModalTitle>
                @L["CrossRateBulletinDetail"]
            </ModalTitle>
            <CloseButton Clicked="@(() => DetailsModal.Hide())" />
        </ModalHeader>
        <ModalBody>
            @* @if (SelectedCrossRateBulletinMaster != null)
            { *@
            <CrossRateBulletinDetail @ref="crossRateBulletinDetailRef" IsModal="true" />
            @* } *@
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="@(() => DetailsModal.Hide())">@L["Close"]</Button>
        </ModalFooter>
    </ModalContent>
</Modal>