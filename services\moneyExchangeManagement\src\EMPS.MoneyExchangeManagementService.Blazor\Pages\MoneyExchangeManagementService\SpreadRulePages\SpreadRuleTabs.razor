﻿@page "/SpreadRuleTabs/{SpreadRuleId}/{SpreadRuleName}"
@using EMPS.MoneyExchangeManagementService.SpreadRuleDetails
@using EMPS.MoneyExchangeManagementService.SpreadRules
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject ISpreadRulesAppService SpreadRulesAppService
@inject ISpreadRuleDetailsAppService SpreadRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager
@using EMPS.Shared.Enum
@inject IJSRuntime JSRuntime

<Tabs SelectedTab=@TabName RenderMode=TabsRenderMode.LazyReload TabPosition=TabPosition.Top FullWidth="true"
      SelectedTabChanged="@((Name) => {TabName =Name; StateHasChanged();})">

    <Items>
        <Tab Name="EditSpreadRuleTab"><Icon Name="@("fa fa-edit")" /> @L["EditSpreadRule"]</Tab>
        <Tab Name="SpreadRuleDetailsTab"><Icon Name="@("fas fa-details")" /> @L["SpreadRuleDetails"]</Tab>
    </Items>


    <Content>
        <TabPanel Name="EditSpreadRuleTab">

            @if (!string.IsNullOrWhiteSpace(SpreadRuleId))
            {
                @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Default).Result == true)
                {
                    <EditSpreadRule SpreadRuleId="@SpreadRuleId" SpreadRuleName="@SpreadRuleName"></EditSpreadRule>
                }

            }
        </TabPanel>

        <TabPanel Name="SpreadRuleDetailsTab">
 
            @if (!string.IsNullOrWhiteSpace(SpreadRuleId))
            {
                <Tabs SelectedTab="@DetailsTabName" RenderMode="TabsRenderMode.LazyReload" TabPosition="TabPosition.Top" FullWidth="true"
                      SelectedTabChanged="@((name) => { DetailsTabName = name; StateHasChanged(); })">
                        <Items>
                        @foreach (var tab in Tabs.OrderBy(t => t.Type))
                            {
                            <Tab Name=@(tab.Type.ToString())><Icon Name="@("fa fa-edit")" /> @L[(tab.Type.ToString())]</Tab>

                            }
                        </Items>
                        <Content>
                        @foreach (var tab in Tabs.OrderBy(t => t.Type))
                            {

                            <TabPanel Name=@(tab.Type.ToString())>
                                <SpreadDetails IsApproved="@IsApproved" Type="@tab.Type" SpreadRuleId="@SpreadRuleId" SpreadRuleName="@SpreadRuleName"></SpreadDetails>
                            </TabPanel>
                            }
                        </Content>
                    </Tabs>
            }
        </TabPanel>

    </Content>
</Tabs>

@code {
    public List<SpreadRuleDetailWithNavigationPropertiesDto> Spreads { get; set; }

    [Parameter] public List<TabData> Tabs { get; set; }
    [Parameter] public string SpreadRuleId { get; set; }
    [Parameter] public string SpreadRuleName { get; set; }
    public string TabName { get; set; }
    public string DetailsTabName { get; set; } 
    public bool IsHidden = false;
    public string selectedTab = "EditSpreadRuleTab";
    private bool IsApproved { get; set; } = false;



    protected override async Task OnInitializedAsync()
    {
        IsApproved = await CheckApprovedSpreadRuleStatus();
        Spreads = await FetchSpreadRuleDetails(Guid.Parse(SpreadRuleId));
        Tabs = GetTabs(Spreads);
        TabName = selectedTab;
        DetailsTabName = $"{Tabs?.LastOrDefault()?.Title}";
        StateHasChanged();

    }
    private async Task<bool> CheckApprovedSpreadRuleStatus()
    {
        var result = await SpreadRulesAppService.GetAsync(Guid.Parse(SpreadRuleId));
        return result.IsApproved;
    }

    private List<TabData> GetTabs(List<SpreadRuleDetailWithNavigationPropertiesDto> details)
    {
        
        var result = details
                 // .Where(x=>x.SpreadRuleDetail.Type == SpreadRuleType.OnCash)
            .GroupBy(d => d.SpreadRuleDetail.Type)
            .Select(group => new TabData
                {
                    Type = group.Key,
                    Title = group.Key.ToString(), // Enum name as tab title
                    SpreadRuleDetails = group.ToList()
                })
            .ToList();
        return result;
    }
    private async Task SaveSpread(SpreadRuleDetailDto updatedItem)
    {
        var updatedDetail = ObjectMapper.Map<SpreadRuleDetailDto, SpreadRuleDetailUpdateDto>(updatedItem);
        await SpreadRuleDetailsAppService.UpdateAsync(updatedItem.Id, updatedDetail);
        Spreads = await FetchSpreadRuleDetails(Guid.Parse(SpreadRuleId));
        StateHasChanged();
    }



    private async Task<List<SpreadRuleDetailWithNavigationPropertiesDto>> FetchSpreadRuleDetails(Guid id)
    {
        // Replace with actual service call to fetch details
        return (await SpreadRuleDetailsAppService
            .GetListAsync(new GetSpreadRuleDetailsInput { SpreadRuleId = id, Type = Tabs?.LastOrDefault()?.Type }))
            .Items
            .ToList(); // Explicitly convert IReadOnlyList to List
    }



}
