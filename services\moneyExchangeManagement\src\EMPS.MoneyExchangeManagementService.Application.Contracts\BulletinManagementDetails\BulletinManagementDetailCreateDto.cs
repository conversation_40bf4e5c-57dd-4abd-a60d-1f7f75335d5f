using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class BulletinManagementDetailCreateDto
    {
        [Required]
        public string CurrencyPair { get; set; }
        public double CashBid { get; set; }
        public double CashAsk { get; set; }
        public double AccountBid { get; set; }
        public double AccountAsk { get; set; }
        public int DisplayOrder { get; set; }
        public Guid BulletinManagementMasterId { get; set; }
    }
}