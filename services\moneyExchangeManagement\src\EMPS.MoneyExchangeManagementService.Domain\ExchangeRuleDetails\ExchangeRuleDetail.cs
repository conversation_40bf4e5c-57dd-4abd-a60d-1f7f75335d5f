using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetail : FullAuditedAggregateRoot<Guid>
    {
        [CanBeNull]
        public virtual string? CurrencyName { get; set; }

        public virtual Guid CurrencyID { get; set; }

        public virtual bool AllowedToBuy { get; set; }

        public virtual double MinAmountToBuy { get; set; }

        public virtual double MaxAmountToBuy { get; set; }

        public virtual double MaxDailyAmountToBuy { get; set; }

        public virtual bool AllowedToSell { get; set; }

        public virtual double MinAmountToSell { get; set; }

        public virtual double MaxAmountToSell { get; set; }

        public virtual double MaxDailyAmountToSell { get; set; }

        public virtual bool AllowedToSellBelowCenterCost { get; set; }

        public virtual bool AllowedToSellBelowCompanyCost { get; set; }

        public virtual Guid ExchangeRuleMasterID { get; set; }

        public virtual ExchangeRuleDetailsType ExchangeRuleDetailType { get; set; }

        public virtual bool IsApproved { get; set; }

        [CanBeNull]
        public virtual Guid? ApprovedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ApprovedByUserName { get; set; }

        public virtual DateTime ApprovedDateTime { get; set; }

        public virtual bool IsArchived { get; set; }

        [CanBeNull]
        public virtual Guid? ArchivedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ArchivedByUserName { get; set; }

        public virtual DateTime ArchivedDateTime { get; set; }

        public virtual Guid? UnArchivedByUserId { get; set; }

        public virtual string? UnArchivedByUserName { get; set; }

        public virtual DateTime? UnArchivedByDate { get; set; }



        public ExchangeRuleDetail()
        {

        }

        public ExchangeRuleDetail(Guid id, string currencyName, Guid currencyID, bool allowedToBuy, double minAmountToBuy, double maxAmountToBuy, double maxDailyAmountToBuy, bool allowedToSell, double minAmountToSell, double maxAmountToSell, double maxDailyAmountToSell, bool allowedToSellBelowCenterCost, bool allowedToSellBelowCompanyCost, Guid exchangeRuleMasterID, ExchangeRuleDetailsType exchangeRuleDetailType)
        {

            Id = id;
            CurrencyName = currencyName;
            CurrencyID = currencyID;
        
            AllowedToBuy = allowedToBuy;
            MinAmountToBuy = minAmountToBuy;
            MaxAmountToBuy = maxAmountToBuy;
            MaxDailyAmountToBuy = maxDailyAmountToBuy;
            AllowedToSell = allowedToSell;
            MinAmountToSell = minAmountToSell;
            MaxAmountToSell = maxAmountToSell;
            MaxDailyAmountToSell = maxDailyAmountToSell;
            AllowedToSellBelowCenterCost = allowedToSellBelowCenterCost;
            AllowedToSellBelowCompanyCost = allowedToSellBelowCompanyCost;
            ExchangeRuleMasterID = exchangeRuleMasterID;
            ExchangeRuleDetailType = exchangeRuleDetailType;
        }

        public void Approve(Guid userId, string userName)
        {
            IsApproved = true;
            ApprovedByUserId = userId;
            ApprovedByUserName = userName;
            ApprovedDateTime = DateTime.Now;
        }

        public void Archive(Guid userId, string userName)
        {
            IsArchived = true;
            ArchivedByUserId = userId;
            ArchivedByUserName = userName;
            ArchivedDateTime = DateTime.Now;
        }

        public void UnArchive(Guid userId, string userName)
        {
            IsArchived = false;
            UnArchivedByUserId = userId;
            UnArchivedByUserName = userName;
            UnArchivedByDate = DateTime.Now;
        }

    }
}