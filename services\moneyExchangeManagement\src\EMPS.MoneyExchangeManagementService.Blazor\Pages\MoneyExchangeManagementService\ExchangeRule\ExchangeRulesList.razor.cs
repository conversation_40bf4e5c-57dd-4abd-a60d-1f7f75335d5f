using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.Shared.Enum.ExchangeRules;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Shared;
using EMPS.FinancialManagementService.CreditLimitRules;
using Microsoft.JSInterop;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.ExchangeRule
{
    public partial class ExchangeRulesList
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar {get; set;} = new PageToolbar();
        private IReadOnlyList<ExchangeRulesDto> ExchangeRuleList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreateExchangeRule { get; set; }
        private bool CanEditExchangeRule { get; set; }
        private bool CanDeleteExchangeRule { get; set; }
        private bool CanApproveExchangeRule { get; set; }
        private bool CanArchiveExchangeRule { get; set; }
        private bool CanUnArchiveExchangeRule { get; set; }
        private bool CanDuplicateExchangeRule { get; set; }
        private bool CanApplyExchangeRule { get; set; }
        private bool CanViewDetails { get; set; }
        private ExchangeRulesCreateDto NewExchangeRule { get; set; }
        private Validations NewExchangeRuleValidations { get; set; } = new();
        private ExchangeRulesUpdateDto EditingExchangeRule { get; set; }
        private Validations EditingExchangeRuleValidations { get; set; } = new();
        private Guid EditingExchangeRuleId { get; set; }
        private Modal CreateExchangeRuleModal { get; set; } = new();
        private Modal EditExchangeRuleModal { get; set; } = new();
        private GetExchangeRulessInput Filter { get; set; }
        private DataGridEntityActionsColumn<ExchangeRulesDto> EntityActionsColumn { get; set; } = new();
        protected bool FilterCollapse { get; set; } = false;

        // Apply Rule Modal Properties
        private Modal ApplyExchangeRuleOnServicePointModal { get; set; } = new();
        private ExchangeRulesDto RuleToBeAppliedOnServicePoint { get; set; } = new();

        private List<ServicePointWithNavigationPropertiesDto> StartList = new List<ServicePointWithNavigationPropertiesDto>();
        private List<ServicePointWithNavigationPropertiesDto> EndList = new List<ServicePointWithNavigationPropertiesDto>();
        private List<ServicePointWithNavigationPropertiesDto> FullList = new List<ServicePointWithNavigationPropertiesDto>();

        public ExchangeRulesList()
        {
            NewExchangeRule = new ExchangeRulesCreateDto();
            EditingExchangeRule = new ExchangeRulesUpdateDto();
            Filter = new GetExchangeRulessInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            ExchangeRuleList = new List<ExchangeRulesDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ExchangeRules"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["ExportToExcel"], async () => { await DownloadAsExcelAsync(); }, IconName.Download);

            Toolbar.AddButton(L["NewExchangeRules"], async () =>
            {
                await OpenCreateExchangeRuleModalAsync();
            }, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.ExchangeRuless.Create);

            return ValueTask.CompletedTask;
        }
        private async Task DownloadAsExcelAsync()
        {
            var token = (await ExchangeRulessAppService.GetDownloadTokenAsync()).Token;
            var remoteService = await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("MoneyExchangeManagementService") ??
            await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("Default");
            NavigationManager.NavigateTo($"{remoteService?.BaseUrl.EnsureEndsWith('/') ?? string.Empty}api/money-exchange-management-service/exchange-ruless/as-excel-file?DownloadToken={token}&FilterText={Filter.FilterText}", forceLoad: true);
        }
        private async Task SetPermissionsAsync()
        {
            CanCreateExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Create);
            CanEditExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Edit);
            CanDeleteExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Delete);
            CanApproveExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Approve);
            CanArchiveExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Archive);
            CanUnArchiveExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.UnArchive);
            CanDuplicateExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Duplicate);
            CanApplyExchangeRule = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Apply);
            CanViewDetails = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Default);
        }

        private async Task GetExchangeRulesAsync()
        {
            bool? ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.All) ArchivedValue = null;
            if (RadioArchiveCheckedValue == ArchiveFilter.Archived) ArchivedValue = true;
            if (RadioArchiveCheckedValue == ArchiveFilter.UnArchive) ArchivedValue = false;


            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;
            Filter.IsArchived = ArchivedValue;

            var result = await ExchangeRulessAppService.GetListAsync(Filter);
            ExchangeRuleList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetExchangeRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<ExchangeRulesDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetExchangeRulesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreateExchangeRuleModalAsync()
        {
            NewExchangeRule = new ExchangeRulesCreateDto
            {
                ActivationDate = DateTime.Now.AddDays(1),
                ExchangeRuleScope = ExchangeRulesScope.global
            };

            await NewExchangeRuleValidations.ClearAll();
            await CreateExchangeRuleModal.Show();
        }

        private async Task CloseCreateExchangeRuleModalAsync()
        {
            NewExchangeRule = new ExchangeRulesCreateDto();
            await CreateExchangeRuleModal.Hide();
        }

        private async Task OpenEditExchangeRuleModalAsync(ExchangeRulesDto input)
        {
            var exchangeRule = await ExchangeRulessAppService.GetAsync(input.Id);

            EditingExchangeRuleId = exchangeRule.Id;
            EditingExchangeRule = ObjectMapper.Map<ExchangeRulesDto, ExchangeRulesUpdateDto>(exchangeRule);
            await EditingExchangeRuleValidations.ClearAll();
            await EditExchangeRuleModal.Show();
        }

        private async Task DeleteExchangeRuleAsync(ExchangeRulesDto input)
        {
            await ExchangeRulessAppService.DeleteAsync(input.Id);
            await GetExchangeRulesAsync();
        }

        private async Task CreateExchangeRuleAsync()
        {
            try
            {
                var validate = true;
                if (validate)
                {
                    await ExchangeRulessAppService.CreateAsync(NewExchangeRule);
                    await GetExchangeRulesAsync();
                    await CloseCreateExchangeRuleModalAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditExchangeRuleModalAsync()
        {
            await EditExchangeRuleModal.Hide();
        }

        private async Task UpdateExchangeRuleAsync()
        {
            try
            {
                var validate = true;
                if (validate)
                {
                    await ExchangeRulessAppService.UpdateAsync(EditingExchangeRuleId, EditingExchangeRule);
                    await GetExchangeRulesAsync();
                    await CloseEditExchangeRuleModalAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }
        private enum ArchiveFilter
        {
            All,
            Archived,
            UnArchive
        }
        private ArchiveFilter RadioArchiveCheckedValue = ArchiveFilter.UnArchive;

        private async Task OnRadioArchiveCheckedValueChanged(ArchiveFilter value)
        {
            RadioArchiveCheckedValue = value;
            await Console.Out.WriteLineAsync("RadioArchiveCheckedValue " + RadioArchiveCheckedValue);
            await GetExchangeRulesAsync();
            StateHasChanged();
        }

        private async Task ApproveExchangeRuleAsync(ExchangeRulesDto input)
        {
            await ExchangeRulessAppService.ApproveRuleAsync(input.Id);
            await GetExchangeRulesAsync();
        }

        private async Task ArchiveExchangeRuleAsync(ExchangeRulesDto input)
        {
            await ExchangeRulessAppService.SetArchiveAsync(input.Id);
            await GetExchangeRulesAsync();
        }

        private async Task UnArchiveExchangeRuleAsync(ExchangeRulesDto input)
        {
            await ExchangeRulessAppService.SetUnArchiveAsync(input.Id);
            await GetExchangeRulesAsync();
        }

        private async Task DuplicateExchangeRuleAsync(ExchangeRulesDto input)
        {
            await ExchangeRulessAppService.DuplicateRuleAsync(input.Id);
            await GetExchangeRulesAsync();
        }

        private async Task CloseApplyExchangeRuleOnServicePointModalAsync()
        {
            RuleToBeAppliedOnServicePoint = new ExchangeRulesDto();

            await ApplyExchangeRuleOnServicePointModal.Hide();

            await GetExchangeRulesAsync();
        }
        private async Task<List<ServicePointWithNavigationPropertiesDto>> GetAllServicPointsAsync()
        {
            return await _servicePointService.GetAllWithNavigationAsync();
        }
        private async Task ApplyExchangeRuleAsync(ExchangeRulesDto context)
        {
            RuleToBeAppliedOnServicePoint = context;

            //Todo check if its for sp or its for client

            var servicePoints = await GetAllServicPointsAsync();

            Console.WriteLine("------ApplyRemitanceRuleAsync-----");
            foreach (var servicePoint in servicePoints)
            {
                Console.WriteLine(servicePoint.ServicePoint.Id);
            }

            StartList = servicePoints.Where(x => x.ServicePoint.ExchangeRuleId != context.Id).ToList();

            EndList = servicePoints.Where(x => x.ServicePoint.ExchangeRuleId == context.Id).ToList();

            await ApplyExchangeRuleOnServicePointModal.Show();
        }

        private async Task<bool> HandleAppliedCallback(List<ServicePointDto> appliedServicePoints)
        {
            var ids = appliedServicePoints.Select(x => x.Id).ToList();
            Console.WriteLine("------HandleAppliedCallback-----");
            foreach (var id in ids)
            {
                Console.WriteLine("tada with ApplyDistributionRuleAsync");
                Console.WriteLine(id);
            }
            // Handle the callback from the child component

            return await _servicePointService.ApplyExchangeRuleAsync(RuleToBeAppliedOnServicePoint.Id, ids);

        }

        private async Task<bool> HandleDisappliedCallback(List<ServicePointDto> disappliedServicePoints)
        {
            var ids = disappliedServicePoints.Select(x => x.Id).ToList();
            Console.WriteLine("------HandleDisappliedCallback-----");
            foreach (var id in ids)
            {
                Console.WriteLine(id);
            }
            // Handle the callback from the child component
            return await _servicePointService.DisapplyExchangeRuleAsync(ids);
        }

        private async Task NavigateToDetailsAsync(string ExchangeRuleID)
        {
            string url = $"/ExchangeRuleMaster/{ExchangeRuleID}";
            await JSRuntime.InvokeVoidAsync("open", url, "_blank");
        }
    }
}
