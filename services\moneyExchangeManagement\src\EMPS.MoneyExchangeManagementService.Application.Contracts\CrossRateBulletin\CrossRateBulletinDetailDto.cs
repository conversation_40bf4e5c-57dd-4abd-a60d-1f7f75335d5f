using System;
using EMPS.MoneyExchangeManagementService.PairingRules;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    public class CrossRateBulletinDetailDto : IEntityDto<Guid>
    {
        public Guid Id { get; set; }
        public Guid CrossRateBulletinMasterId { get; set; }
        public string BaseCurrencyCode { get; set; }
        public string QuoteCurrencyCode { get; set; }
        public string PairingFormat { get; set; }
        public double CrossRateValue { get; set; }

        public bool IsUsdSypRecord()
        {
            return (BaseCurrencyCode == PairingRuleConsts.UsdCode && QuoteCurrencyCode == PairingRuleConsts.SypCode) ||
            (BaseCurrencyCode == PairingRuleConsts.SypCode && QuoteCurrencyCode == PairingRuleConsts.UsdCode);
        }
    }
}