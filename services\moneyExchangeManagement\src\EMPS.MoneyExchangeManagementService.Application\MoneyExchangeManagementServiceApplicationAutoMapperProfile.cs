using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using System;
using EMPS.MoneyExchangeManagementService.Shared;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using System;
using EMPS.MoneyExchangeManagementService.Shared;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using AutoMapper;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using System;

namespace EMPS.MoneyExchangeManagementService;

public class MoneyExchangeManagementServiceApplicationAutoMapperProfile : Profile
{
        public MoneyExchangeManagementServiceApplicationAutoMapperProfile()
        {
                /* You can configure your AutoMapper mapping configuration here.
                * Alternatively, you can split your mapping configurations
                * into multiple profile classes for a better organization. */

                CreateMap<CrossRateBulletinMaster, CrossRateBulletinMasterDto>();
                CreateMap<CrossRateBulletinMaster, CreateUpdateCrossRateBulletinMasterDto>().ReverseMap();
                CreateMap<CrossRateBulletinDetail, CrossRateBulletinDetailDto>();
                CreateMap<CrossRateBulletinDetail, CreateUpdateCrossRateBulletinDetailDto>().ReverseMap();



                CreateMap<PairingRule, PairingRuleDto>();
                CreateMap<PairingRule, PairingRuleExcelDto>();

                CreateMap<PairingRuleDetail, PairingRuleDetailDto>();
                CreateMap<PairingRuleDetail, PairingRuleDetailExcelDto>();
                CreateMap<PairingRuleDetailWithNavigationProperties, PairingRuleDetailWithNavigationPropertiesDto>();
                CreateMap<PairingRule, LookupDto<Guid>>().ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.Name));
        CreateMap<SpreadRule, SpreadRuleDto>();
        CreateMap<SpreadRule, SpreadRuleExcelDto>();

        CreateMap<SpreadRuleDetail, SpreadRuleDetailDto>();
        CreateMap<SpreadRuleDetail, SpreadRuleDetailExcelDto>();
        CreateMap<SpreadRuleDetailWithNavigationProperties, SpreadRuleDetailWithNavigationPropertiesDto>();
        CreateMap<SpreadRule, LookupDto<Guid>>().ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.RuleName));

        CreateMap<ExchangeRules, ExchangeRulesDto>();
        CreateMap<ExchangeRules, ExchangeRulesExcelDto>();

        CreateMap<ExchangeRuleDetail, ExchangeRuleDetailDto>();
        CreateMap<ExchangeRuleDetail, ExchangeRuleDetailExcelDto>();

        CreateMap<BulletinManagementMaster, BulletinManagementMasterDto>();

        CreateMap<BulletinManagementDetail, BulletinManagementDetailDto>();

        CreateMap<BulletinManagementDetailWithNavigationProperties, BulletinManagementDetailWithNavigationPropertiesDto>();
        CreateMap<BulletinManagementMaster, LookupDto<Guid>>().ForMember(dest => dest.DisplayName, opt => opt.MapFrom(src => src.BulletinName));
    

                CreateMap<CrossRateProviderConfiguration, CrossRateProviderConfigurationDto>();

        }
}