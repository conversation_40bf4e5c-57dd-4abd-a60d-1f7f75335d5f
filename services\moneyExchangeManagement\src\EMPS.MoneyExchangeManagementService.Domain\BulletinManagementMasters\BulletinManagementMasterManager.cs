using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public class BulletinManagementMasterManager : DomainService
    {
        private readonly IBulletinManagementMasterRepository _bulletinManagementMasterRepository;

        public BulletinManagementMasterManager(IBulletinManagementMasterRepository bulletinManagementMasterRepository)
        {
            _bulletinManagementMasterRepository = bulletinManagementMasterRepository;
        }
        public async Task<BulletinManagementMaster> CreateMasterAsync(BulletinManagementMaster bulletinManagementMaster)
        {
            return await _bulletinManagementMasterRepository.InsertAsync(bulletinManagementMaster);

        }

        public async Task<BulletinManagementMaster> CreateAsync(
        string bulletinNumber, string bulletinName, DateTime bulletinDate, string notes, string servicePointName, string currencyPairingRuleName, string crossRateBulletinName, string spreadRuleName, string publishByUserName, Guid? servicePointId = null, Guid? currencyPairingRuleId = null, Guid? crossRateBulletinId = null, Guid? spreadRuleId = null, Guid? publishByUserId = null, DateTime? publishDate = null)
        {
            Check.NotNullOrWhiteSpace(bulletinNumber, nameof(bulletinNumber));
            Check.NotNullOrWhiteSpace(bulletinName, nameof(bulletinName));
            Check.NotNull(bulletinDate, nameof(bulletinDate));

            var bulletinManagementMaster = new BulletinManagementMaster(
             GuidGenerator.Create(),
             bulletinNumber, bulletinName, bulletinDate, notes, servicePointName, currencyPairingRuleName, crossRateBulletinName, spreadRuleName, publishByUserName, servicePointId, currencyPairingRuleId, crossRateBulletinId, spreadRuleId, publishByUserId, publishDate
             );

            return await _bulletinManagementMasterRepository.InsertAsync(bulletinManagementMaster);
        }

       
        public async Task<BulletinManagementMaster> UpdateAsync(
            Guid id,
            string bulletinNumber, string bulletinName, DateTime bulletinDate, string notes, string servicePointName, string currencyPairingRuleName, string crossRateBulletinName, string spreadRuleName, string publishByUserName, Guid? servicePointId = null, Guid? currencyPairingRuleId = null, Guid? crossRateBulletinId = null, Guid? spreadRuleId = null, Guid? publishByUserId = null, DateTime? publishDate = null, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(bulletinNumber, nameof(bulletinNumber));
            Check.NotNullOrWhiteSpace(bulletinName, nameof(bulletinName));
            Check.NotNull(bulletinDate, nameof(bulletinDate));

            var bulletinManagementMaster = await _bulletinManagementMasterRepository.GetAsync(id);

            bulletinManagementMaster.BulletinNumber = bulletinNumber;
            bulletinManagementMaster.BulletinName = bulletinName;
            bulletinManagementMaster.BulletinDate = bulletinDate;
            bulletinManagementMaster.Notes = notes;
            bulletinManagementMaster.ServicePointName = servicePointName;
            bulletinManagementMaster.CurrencyPairingRuleName = currencyPairingRuleName;
            bulletinManagementMaster.CrossRateBulletinName = crossRateBulletinName;
            bulletinManagementMaster.SpreadRuleName = spreadRuleName;
            bulletinManagementMaster.PublishByUserName = publishByUserName;
            bulletinManagementMaster.ServicePointId = servicePointId;
            bulletinManagementMaster.CurrencyPairingRuleId = currencyPairingRuleId;
            bulletinManagementMaster.CrossRateBulletinId = crossRateBulletinId;
            bulletinManagementMaster.SpreadRuleId = spreadRuleId;
            bulletinManagementMaster.PublishByUserId = publishByUserId;
            bulletinManagementMaster.PublishDate = publishDate;

            bulletinManagementMaster.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _bulletinManagementMasterRepository.UpdateAsync(bulletinManagementMaster);
        }

    }
}