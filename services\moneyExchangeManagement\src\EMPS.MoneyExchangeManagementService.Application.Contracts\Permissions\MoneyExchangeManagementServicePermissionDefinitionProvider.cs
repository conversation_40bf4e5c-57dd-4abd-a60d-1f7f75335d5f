using EMPS.MoneyExchangeManagementService.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace EMPS.MoneyExchangeManagementService.Permissions;

public class MoneyExchangeManagementServicePermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(MoneyExchangeManagementServicePermissions.GroupName, L("Permission:MoneyExchangeManagementService"));


        var crossRateBulletinMaster = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default, L("Permission:CrossRateBulletinMaster"));
        crossRateBulletinMaster.AddChild(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create, L("Permission:Create"));
        crossRateBulletinMaster.AddChild(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Delete, L("Permission:Delete"));
        crossRateBulletinMaster.AddChild(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Approve, L("Permission:Approve"));
        crossRateBulletinMaster.AddChild(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Archive, L("Permission:Archive"));
        crossRateBulletinMaster.AddChild(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Unarchive, L("Permission:Unarchive"));

        var crossRateProviderConfiguration = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.CrossRateProviderConfiguration.Default, L("Permission:CrossRateProviderConfiguration"));
        crossRateProviderConfiguration.AddChild(MoneyExchangeManagementServicePermissions.CrossRateProviderConfiguration.Manage, L("Permission:Manage"));

        //Define your own permissions here. Example:
        //myGroup.AddPermission(BookStorePermissions.MyPermission1, L("Permission:MyPermission1"));

        var bulletinManagementMasterPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Default, L("Permission:BulletinManagementMasters"));
        bulletinManagementMasterPermission.AddChild(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Publish, L("Permission:Publish"));
        bulletinManagementMasterPermission.AddChild(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Preview, L("Permission:Preview"));

        var bulletinManagementDetailPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Default, L("Permission:BulletinManagementDetails"));
        bulletinManagementDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Create, L("Permission:Create"));
        bulletinManagementDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Edit, L("Permission:Edit"));
        bulletinManagementDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.BulletinManagementDetails.Delete, L("Permission:Delete"));

        var pairingRulePermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.PairingRules.Default, L("Permission:PairingRules"));
        pairingRulePermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRules.Create, L("Permission:Create"));
        pairingRulePermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRules.Edit, L("Permission:Edit"));
        pairingRulePermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRules.Delete, L("Permission:Delete"));
        pairingRulePermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRules.Approve, L("Permission:Approve"));

        var pairingRuleDetailPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default, L("Permission:PairingRuleDetails"));
        pairingRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Create, L("Permission:Create"));
        pairingRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Edit, L("Permission:Edit"));
        pairingRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Delete, L("Permission:Delete"));

        var cashiersTransferPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.SpreadRules.Default, L("Permission:SpreadRules"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Create, L("Permission:Create"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Edit, L("Permission:Edit"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Delete, L("Permission:Delete"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Approve, L("Permission:Approve"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Archive, L("Permission:Archive"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.UnArchive, L("Permission:UnArchive"));
        cashiersTransferPermission.AddChild(MoneyExchangeManagementServicePermissions.SpreadRules.Duplicate, L("Permission:Duplicate"));

        var centralDeposit = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Default, L("Permission:SpreadRuleDetails"));
        centralDeposit.AddChild(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Create, L("Permission:Create"));
        centralDeposit.AddChild(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Delete, L("Permission:Delete"));
        centralDeposit.AddChild(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Edit, L("Permission:Edit"));

        var exchangeRulesPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default, L("Permission:ExchangeRuless"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Create, L("Permission:Create"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Edit, L("Permission:Edit"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Delete, L("Permission:Delete"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Approve, L("Permission:Approve"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Archive, L("Permission:Archive"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.UnArchive, L("Permission:UnArchive"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Duplicate, L("Permission:Duplicate"));
        exchangeRulesPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuless.Apply, L("Permission:Apply"));

        var exchangeRuleDetailPermission = myGroup.AddPermission(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Default, L("Permission:ExchangeRuleDetails"));
        exchangeRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Create, L("Permission:Create"));
        exchangeRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit, L("Permission:Edit"));
        exchangeRuleDetailPermission.AddChild(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Delete, L("Permission:Delete"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<MoneyExchangeManagementServiceResource>(name);
    }
}