using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    public interface ICrossRateBulletinMasterAppService : ICrudAppService<
        CrossRateBulletinMasterDto, // The main DTO
        Guid,                       // The primary key type
        GetCrossRateBulletinMasterListDto, // List input (or your custom list input DTO)
        CreateUpdateCrossRateBulletinMasterDto // Create/Update DTO
    >
    {
        Task ApproveAsync(Guid id);
        Task ArchiveAsync(Guid id);
        Task UnarchiveAsync(Guid id);
    }
}