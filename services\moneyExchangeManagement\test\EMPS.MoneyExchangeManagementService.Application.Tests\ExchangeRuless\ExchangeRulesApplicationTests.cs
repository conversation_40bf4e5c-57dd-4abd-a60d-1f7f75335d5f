using System;
using System.Linq;
using Shouldly;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Xunit;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulessAppServiceTests : MoneyExchangeManagementServiceApplicationTestBase
    {
        private readonly IExchangeRulessAppService _exchangeRulessAppService;
        private readonly IRepository<ExchangeRules, Guid> _exchangeRulesRepository;

        public ExchangeRulessAppServiceTests()
        {
            _exchangeRulessAppService = GetRequiredService<IExchangeRulessAppService>();
            _exchangeRulesRepository = GetRequiredService<IRepository<ExchangeRules, Guid>>();
        }

        [Fact]
        public async Task GetListAsync()
        {
            // Act
            var result = await _exchangeRulessAppService.GetListAsync(new GetExchangeRulessInput());

            // Assert
            result.TotalCount.ShouldBe(2);
            result.Items.Count.ShouldBe(2);
            result.Items.Any(x => x.Id == Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2")).ShouldBe(true);
            result.Items.Any(x => x.Id == Guid.Parse("8cb6675c-7eee-4493-a9c8-2092173f1826")).ShouldBe(true);
        }

        [Fact]
        public async Task GetAsync()
        {
            // Act
            var result = await _exchangeRulessAppService.GetAsync(Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"));

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"));
        }

        [Fact]
        public async Task CreateAsync()
        {
            // Arrange
            var input = new ExchangeRulesCreateDto
            {
                Name = "fe76ad8908964072ab7ea809ffdabe000cf",
                Description = "9cd18385a3dc49998b019bdfa99ac2ef590a446c11f748798ac621e4dd03400bb1d5887f9e2d433abacce3dd1ae9",
                ActivationDate = new DateTime(2010, 11, 24),
                ExchangeRuleScope = default
            };

            // Act
            var serviceResult = await _exchangeRulessAppService.CreateAsync(input);

            // Assert
            var result = await _exchangeRulesRepository.FindAsync(c => c.Id == serviceResult.Id);

            result.ShouldNotBe(null);
            result.Name.ShouldBe("fe76ad8908964072ab7ea809ffdabe000cf");
            result.Description.ShouldBe("9cd18385a3dc49998b019bdfa99ac2ef590a446c11f748798ac621e4dd03400bb1d5887f9e2d433abacce3dd1ae9");
            result.ActivationDate.ShouldBe(new DateTime(2010, 11, 24));
            result.ExchangeRuleScope.ShouldBe(default);
        }

        [Fact]
        public async Task UpdateAsync()
        {
            // Arrange
            var input = new ExchangeRulesUpdateDto()
            {
                Name = "4033fbae7c1b4876bfa1d5696246121f190c9aea56e24e03a3b512d06155f396e43f3235bd3f487a896c49c96f56dc63",
                Description = "3ae03cc1c4184781b6021d93c0",
                ActivationDate = new DateTime(2009, 6, 23),
                ExchangeRuleScope = default
            };

            // Act
            var serviceResult = await _exchangeRulessAppService.UpdateAsync(Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"), input);

            // Assert
            var result = await _exchangeRulesRepository.FindAsync(c => c.Id == serviceResult.Id);

            result.ShouldNotBe(null);
            result.Name.ShouldBe("4033fbae7c1b4876bfa1d5696246121f190c9aea56e24e03a3b512d06155f396e43f3235bd3f487a896c49c96f56dc63");
            result.Description.ShouldBe("3ae03cc1c4184781b6021d93c0");
            result.ActivationDate.ShouldBe(new DateTime(2009, 6, 23));
            result.ExchangeRuleScope.ShouldBe(default);
        }

        [Fact]
        public async Task DeleteAsync()
        {
            // Act
            await _exchangeRulessAppService.DeleteAsync(Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"));

            // Assert
            var result = await _exchangeRulesRepository.FindAsync(c => c.Id == Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"));

            result.ShouldBeNull();
        }
    }
}