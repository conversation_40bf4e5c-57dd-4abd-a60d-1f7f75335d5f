﻿using System;
using Microsoft.Extensions.Logging;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using EMPS.Shared.Hosting.Microservices.DbMigrations.EfCore;
using Volo.Abp.DistributedLocking;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;

namespace EMPS.MoneyExchangeManagementService.DbMigrations;

public class MoneyExchangeManagementServiceDatabaseMigrationChecker : PendingEfCoreMigrationsChecker<MoneyExchangeManagementServiceDbContext>
{
    public MoneyExchangeManagementServiceDatabaseMigrationChecker(
        ILoggerFactory loggerFactory,
        IUnitOfWorkManager unitOfWorkManager,
        IServiceProvider serviceProvider,
        ICurrentTenant currentTenant,
        IDistributedEventBus distributedEventBus,
        IAbpDistributedLock abpDistributedLock)
        : base(
            loggerFactory,
            unitOfWorkManager,
            serviceProvider,
            currentTenant,
            distributedEventBus,
            abpDistributedLock,
            MoneyExchangeManagementServiceDbProperties.ConnectionStringName)
    {

    }
}
