@using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
@using EMPS.MoneyExchangeManagementService.ExchangeRuless
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using EMPS.Shared.Enum.ExchangeRules
@inherits MoneyExchangeManagementServiceComponentBase
@inject IExchangeRuleDetailsAppService ExchangeRuleDetailsAppService
@inject IExchangeRulessAppService ExchangeRulessAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager

@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@(L["ExchangeRuleDetails"] + " : "+ @ExchangeRuleName)" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="ExchangeRuleDetailSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText"
                              Autofocus="true"
                              Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="ExchangeRuleDetailSearchForm" Clicked="SearchAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="ExchangeRuleDetailDto"
                  Data="ExchangeRuleDetailList"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  Responsive="true"
                  PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="ExchangeRuleDetailDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="ExchangeRuleDetailDto" EntityActionsColumn="@EntityActionsColumn">
                            @if (!context.IsApproved)
                            {
                                <EntityAction TItem="ExchangeRuleDetailDto"
                                              Visible="@CanEditExchangeRuleDetail"
                                              Clicked="async () => await OpenEditExchangeRuleDetailModalAsync(context)"
                                              Text="@L["Edit"]"></EntityAction>
                                
                            }
                            
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="CurrencyName"
                                Caption="@L["CurrencyName"]">
                </DataGridColumn>



                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="AllowedToBuy"
                                Caption="@L["AllowedToBuy"]">
                    <DisplayTemplate>
                        @if (context.AllowedToBuy)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>


                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MinAmountToBuy"
                                Caption="@L["MinAmountToBuy"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MaxAmountToBuy"
                                Caption="@L["MaxAmountToBuy"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MaxDailyAmountToBuy"
                                Caption="@L["MaxDailyAmountToBuy"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="AllowedToSell"
                                Caption="@L["AllowedToSell"]">
                    <DisplayTemplate>
                        @if (context.AllowedToSell)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>


                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MinAmountToSell"
                                Caption="@L["MinAmountToSell"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MaxAmountToSell"
                                Caption="@L["MaxAmountToSell"]">
                </DataGridColumn>

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="MaxDailyAmountToSell"
                                Caption="@L["MaxDailyAmountToSell"]">
                </DataGridColumn>
                @if (ExchangeRule.ExchangeRuleScope != ExchangeRulesScope.Company)
                {
                    <DataGridColumn TItem="ExchangeRuleDetailDto"
                                    Field="AllowedToSellBelowCenterCost"
                                    Caption="@L["AllowedToSellBelowCenterCost"]">
                        <DisplayTemplate>
                            @if (context.AllowedToSellBelowCenterCost)
                            {
                                <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                            }
                            else
                            {
                                <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                            }
                        </DisplayTemplate>
                    </DataGridColumn>

                }
            

                <DataGridColumn TItem="ExchangeRuleDetailDto"
                                Field="AllowedToSellBelowCompanyCost"
                                Caption="@L["AllowedToSellBelowCompanyCost"]">
                    <DisplayTemplate>
                        @if (context.AllowedToSellBelowCompanyCost)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
                </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditExchangeRuleDetailModal" Closing="@EditExchangeRuleDetailModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditExchangeRuleDetailForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditExchangeRuleDetailModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingExchangeRuleDetailValidations"
                             Mode="ValidationMode.Auto"
                             Model="@EditingExchangeRuleDetail"
                             ValidateOnLoad="false">


                    <Validation>
                        <Field>
                            <FieldLabel>@L["CurrencyName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingExchangeRuleDetail.CurrencyName" ReadOnly="true">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>

           

                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingExchangeRuleDetail.AllowedToBuy" >@L["AllowedToBuy"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["MinAmountToBuy"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MinAmountToBuy">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                             </NumericPicker>
                        </Field>
                     </Validation> 


                    <Validation>
                        <Field>
                            <FieldLabel>@L["MaxAmountToBuy"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MaxAmountToBuy">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["MaxDailyAmountToBuy"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MaxDailyAmountToBuy">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    </Validation>


                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingExchangeRuleDetail.AllowedToSell">@L["AllowedToSell"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["MinAmountToSell"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MinAmountToSell">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["MaxAmountToSell"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MaxAmountToSell">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["MaxDailyAmountToSell"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingExchangeRuleDetail.MaxDailyAmountToSell">
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </NumericPicker>
                        </Field>
                    </Validation>

                     @if (ExchangeRule.ExchangeRuleScope != ExchangeRulesScope.Company)
                {
                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingExchangeRuleDetail.AllowedToSellBelowCenterCost">@L["AllowedToSellBelowCenterCost"]</Check>
                    </Field>
                }
                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingExchangeRuleDetail.AllowedToSellBelowCompanyCost">@L["AllowedToSellBelowCompanyCost"]</Check>
                    </Field>              


                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseEditExchangeRuleDetailModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreateExchangeRuleDetailForm" Clicked="UpdateExchangeRuleDetailAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
