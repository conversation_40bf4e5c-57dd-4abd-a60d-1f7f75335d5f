using EMPS.Shared.Enum;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetail : FullAuditedAggregateRoot<Guid>
    {
        [CanBeNull]
        public virtual Guid CurrencyId { get; set; }

        [CanBeNull]
        public virtual string? CurrencyCode { get; set; }

        public virtual SpreadRuleType Type { get; set; }

        public virtual double BidSpread { get; set; }

        public virtual double BidMaxDiscount { get; set; }

        public virtual double BidMaxMarkdown { get; set; }

        public virtual double AskSpread { get; set; }

        public virtual double AskMaxDiscount { get; set; }

        public virtual double AskMaxMarkup { get; set; }
        public Guid? SpreadRuleId { get; set; }

        public SpreadRuleDetail()
        {

        }

        public SpreadRuleDetail(Guid id, Guid? spreadRuleId, Guid currencyId, string currencyCode, 
            SpreadRuleType type,

            double bidSpread, 
            double bidMaxDiscount,
            double bidMaxMarkdown, 
            double askSpread, 
            double askMaxDiscount, 
            double askMaxMarkup)
        {

            Id = id;
            CurrencyId = currencyId;
            CurrencyCode = currencyCode;
            Type = type;
            BidSpread = bidSpread;
            BidMaxDiscount = bidMaxDiscount;
            BidMaxMarkdown = bidMaxMarkdown;
            AskSpread = askSpread;
            AskMaxDiscount = askMaxDiscount;
            AskMaxMarkup = askMaxMarkup;
            SpreadRuleId = spreadRuleId;
        }

    }
}