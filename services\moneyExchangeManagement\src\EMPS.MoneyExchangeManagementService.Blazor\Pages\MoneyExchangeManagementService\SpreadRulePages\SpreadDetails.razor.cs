﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.Shared.Enum;
using Microsoft.AspNetCore.Components;
using static EMPS.CustomerService.Permissions.CustomerServicePermissions;
using Blazorise.Snackbar;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using static EMPS.MoneyExchangeManagementService.Permissions.MoneyExchangeManagementServicePermissions;
using EMPS.MoneyExchangeManagementService.SpreadRules;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.SpreadRulePages
{
    public partial class SpreadDetails
    {

        [Parameter] public string SpreadRuleId { get; set; }
        [Parameter] public string SpreadRuleName { get; set; }
        [Parameter] public SpreadRuleType Type { get; set; }
        [Parameter] public bool IsApproved { get; set; }


        private SpreadRuleDetailWithNavigationPropertiesDto SpreadRuleDetail { get; set; } = new SpreadRuleDetailWithNavigationPropertiesDto();

        private Validations SpreadRuleDetailsValidations { get; set; } = new();

        public bool visible;
        SnackbarStack snackbarStack;

        private bool IsValueChanged = false;





        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar {get;} = new PageToolbar();
        private IReadOnlyList<SpreadRuleDetailWithNavigationPropertiesDto> SpreadRuleDetailList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreateSpreadRuleDetail { get; set; }
        private bool CanEditSpreadRuleDetail { get; set; }
        private bool CanDeleteSpreadRuleDetail { get; set; }
        private SpreadRuleDetailCreateDto NewSpreadRuleDetail { get; set; }
        private Validations NewSpreadRuleDetailValidations { get; set; } = new();
        private SpreadRuleDetailUpdateDto EditingSpreadRuleDetail { get; set; }
        private Validations EditingSpreadRuleDetailValidations { get; set; } = new();
        private Guid EditingSpreadRuleDetailId { get; set; }
        private Modal CreateSpreadRuleDetailModal { get; set; } = new();
        private Modal EditSpreadRuleDetailModal { get; set; } = new();
        private GetSpreadRuleDetailsInput Filter { get; set; }
        private DataGridEntityActionsColumn<SpreadRuleDetailWithNavigationPropertiesDto> EntityActionsColumn { get; set; } = new();
        protected string SelectedCreateTab = "spreadRuleDetail-create-tab";
        protected string SelectedEditTab = "spreadRuleDetail-edit-tab";
        private IReadOnlyList<LookupDto<Guid>> SpreadRulesCollection { get; set; } = new List<LookupDto<Guid>>();

        public SpreadDetails()
        {
            NewSpreadRuleDetail = new SpreadRuleDetailCreateDto();
            EditingSpreadRuleDetail = new SpreadRuleDetailUpdateDto();

            Filter = new GetSpreadRuleDetailsInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };


            SpreadRuleDetailList = new List<SpreadRuleDetailWithNavigationPropertiesDto>();
        }
        void ShowIndecatoe()
        {
            visible = true;
            StateHasChanged();
        }
        void HideIndecatoe()
        {
            visible = false;
            StateHasChanged();
        }
      
        protected override async Task OnInitializedAsync()
        {
            try
            {
                ShowIndecatoe();
                await SetToolbarItemsAsync();
                await SetBreadcrumbItemsAsync();
                await SetPermissionsAsync();
                await GetSpreadRuleDetailsAsync();
                HideIndecatoe();
            }
            catch (Exception ex)
            {
                HideIndecatoe();
                throw;
            }
       

        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:SpreadRuleDetails"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["ExportToExcel"], async () =>{ await DownloadAsExcelAsync(); }, IconName.Download);
            
            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateSpreadRuleDetail = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Create);
            CanEditSpreadRuleDetail = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Edit);
            CanDeleteSpreadRuleDetail = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Delete);
        }

        private async Task GetSpreadRuleDetailsAsync()
        {
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;
            if (!string.IsNullOrWhiteSpace(SpreadRuleId))
                Filter.SpreadRuleId = Guid.Parse(SpreadRuleId);
            if (Type != default)
                Filter.Type = Type;
            var result = await SpreadRuleDetailsAppService.GetListAsync(Filter);
            SpreadRuleDetailList = result.Items;
            foreach (var row in SpreadRuleDetailList)
                ValidateRow(row);
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetSpreadRuleDetailsAsync();
            await InvokeAsync(StateHasChanged);
        }

        private  async Task DownloadAsExcelAsync()
        {
            var token = (await SpreadRuleDetailsAppService.GetDownloadTokenAsync()).Token;
            var remoteService = await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("MoneyExchangeManagementService") ??
            await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("Default");
            NavigationManager.NavigateTo($"{remoteService?.BaseUrl.EnsureEndsWith('/') ?? string.Empty}api/liquidity-service/spread-rule-details/as-excel-file?DownloadToken={token}&FilterText={Filter.FilterText}", forceLoad: true);
        }


        private void ValidateRow(SpreadRuleDetailWithNavigationPropertiesDto row)
        {
            var bid = row.SpreadRuleDetail.BidSpread;
            var ask = row.SpreadRuleDetail.AskSpread;

            row.ValidationMessage = null;
            row.IsBidSpreadInvalid = row.IsAskSpreadInvalid = false;
            row.IsBidMaxDiscountInvalid = row.IsBidMaxMarkdownInvalid = false;
            row.IsAskMaxDiscountInvalid = row.IsAskMaxMarkupInvalid = false;

            if (bid < 0 && ask < 0)
            {
                row.ValidationMessage = L["BidAndAskNegative"];
                row.IsBidSpreadInvalid = row.IsAskSpreadInvalid = true;
            }
            else if (bid < 0 && Math.Abs(bid) > ask)
            {
                row.ValidationMessage = L["MustBeAbsBidLargerThanAsk"];
                row.IsBidSpreadInvalid = true;
            }
            else if (ask < 0 && Math.Abs(ask) > bid)
            {
                row.ValidationMessage = L["MustBeAbsAskLargerThanBid"];
                row.IsAskSpreadInvalid = true;
            }

            if ((row.SpreadRuleDetail.BidMaxDiscount ) < 0)
                row.IsBidMaxDiscountInvalid = true;
            if ((row.SpreadRuleDetail.BidMaxMarkdown ) < 0)
                row.IsBidMaxMarkdownInvalid = true;
            if ((row.SpreadRuleDetail.AskMaxDiscount) < 0)
                row.IsAskMaxDiscountInvalid = true;
            if ((row.SpreadRuleDetail.AskMaxMarkup) < 0)
                row.IsAskMaxMarkupInvalid = true;

            if (row.IsBidMaxDiscountInvalid || row.IsBidMaxMarkdownInvalid ||
                row.IsAskMaxDiscountInvalid || row.IsAskMaxMarkupInvalid)
            {
                row.ValidationMessage ??= L["DiscountAndMarkupMustBePositive"];
            }
        }

        private string GetInputStyle(bool isInvalid)
        {
            return isInvalid
                ? "background-color: red;"
                : "background-color: greenyellow;";
        }

        private async Task HandleKeyDown(KeyboardEventArgs args, SpreadRuleDetailWithNavigationPropertiesDto currentDetail)
        {
            if (args.Key == "ArrowLeft")
            {
                // Check if it's the last element in the row
                bool isLastElement = await JSRuntime.InvokeAsync<bool>("isLastElementInRow");
                if (isLastElement)
                    await JSRuntime.InvokeVoidAsync("moveFocusToNextRow");
                else
                    await JSRuntime.InvokeVoidAsync("moveFocusToNextElement");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.Key == "ArrowRight")
            {
                await JSRuntime.InvokeVoidAsync("moveFocusToPreviousElement");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.Key == "ArrowUp")
            {
                await JSRuntime.InvokeVoidAsync("moveFocusToPreviousRowSameColumn");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.Key == "ArrowDown")
            {
                await JSRuntime.InvokeVoidAsync("moveFocusToNextRowSameColumn");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.ShiftKey && args.Key == "Tab")
            {
                await JSRuntime.InvokeVoidAsync("moveFocusToPreviousElement");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.Key == "Tab")
            {
                // Check if it's the last element in the row
                bool isLastElement = await JSRuntime.InvokeAsync<bool>("isLastElementInRow");
                if (isLastElement)
                    await JSRuntime.InvokeVoidAsync("moveFocusToNextRow");
                else
                    await JSRuntime.InvokeVoidAsync("moveFocusToNextElement");
                ExecuteJS("event.preventDefault();");
            }
            else if (args.Key == "Enter")
            {
                Console.WriteLine("Pass__EnterClicked");

                if (IsValueChanged)
                {
                    await ProcessData(currentDetail);

                }
            }
        }

        private async Task HandleBlur(SpreadRuleDetailWithNavigationPropertiesDto currentDetail)
        {
            Console.WriteLine("Pass__HandleBlur");

            if (visible == false && IsValueChanged)
            {
                await ProcessData(currentDetail);
            }
        }
        private void OnValueChanged(double newValue, SpreadRuleDetailWithNavigationPropertiesDto row, string propertyName)
        {
            double oldValue = propertyName switch
            {
                nameof(row.SpreadRuleDetail.BidSpread) => row.SpreadRuleDetail.BidSpread,
                nameof(row.SpreadRuleDetail.BidMaxDiscount) => row.SpreadRuleDetail.BidMaxDiscount,
                nameof(row.SpreadRuleDetail.BidMaxMarkdown) => row.SpreadRuleDetail.BidMaxMarkdown,
                nameof(row.SpreadRuleDetail.AskSpread) => row.SpreadRuleDetail.AskSpread,
                nameof(row.SpreadRuleDetail.AskMaxDiscount) => row.SpreadRuleDetail.AskMaxDiscount,
                nameof(row.SpreadRuleDetail.AskMaxMarkup) => row.SpreadRuleDetail.AskMaxMarkup,
                _ => 0
            };

            if (newValue != oldValue)
            {
                // تحديث القيمة الجديدة فقط إذا اختلفت
                switch (propertyName)
                {
                    case nameof(row.SpreadRuleDetail.BidSpread):
                        row.SpreadRuleDetail.BidSpread = newValue;
                        break;
                    case nameof(row.SpreadRuleDetail.BidMaxDiscount):
                        row.SpreadRuleDetail.BidMaxDiscount = newValue;
                        break;
                    case nameof(row.SpreadRuleDetail.BidMaxMarkdown):
                        row.SpreadRuleDetail.BidMaxMarkdown = newValue;
                        break;
                    case nameof(row.SpreadRuleDetail.AskSpread):
                        row.SpreadRuleDetail.AskSpread = newValue;
                        break;
                    case nameof(row.SpreadRuleDetail.AskMaxDiscount):
                        row.SpreadRuleDetail.AskMaxDiscount = newValue;
                        break;
                    case nameof(row.SpreadRuleDetail.AskMaxMarkup):
                        row.SpreadRuleDetail.AskMaxMarkup = newValue;
                        break;
                }

                // التحقق والتحديث بعد التغيير
                ValidateRow(row);
                IsValueChanged = true; // علامة على أنه تم تغيير قيمة
                 ProcessData(row).GetAwaiter().GetResult();

            }
        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        private async Task ProcessData(SpreadRuleDetailWithNavigationPropertiesDto currentDetail)
        {
            try
            {
                Console.WriteLine("Pass__ProcessData");

                if (IsApproved || !string.IsNullOrEmpty(currentDetail.ValidationMessage))
                    return;


                var detailUpdateDto = ObjectMapper.Map<SpreadRuleDetailDto, SpreadRuleDetailUpdateDto>(currentDetail.SpreadRuleDetail);
                detailUpdateDto.ConcurrencyStamp = null;
                SpreadRuleDto spreadRuleDto = await SpreadRuleDetailsAppService.UpdateSpreadRuleRowAsync(currentDetail.SpreadRuleDetail.Id, detailUpdateDto);
                IsApproved = spreadRuleDto.IsApproved;
                IsValueChanged = false;
                await snackbarStack.PushAsync(L["UpdateSuccesfully"], SnackbarColor.Success);
            }
            catch (Exception ex)
            {
                await snackbarStack.PushAsync(L["UpdateFailed"] + ": " + ex.Message, SnackbarColor.Danger);
            }

        }

    }
}
