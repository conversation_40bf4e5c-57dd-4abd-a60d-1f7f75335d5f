using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using AutoMapper;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRules;


namespace EMPS.MoneyExchangeManagementService.Blazor;

public class MoneyExchangeManagementServiceBlazorAutoMapperProfile : Profile
{
    public MoneyExchangeManagementServiceBlazorAutoMapperProfile()
    {

        CreateMap<CrossRateBulletinMasterDto, CreateUpdateCrossRateBulletinMasterDto>();


        CreateMap<PairingRuleDto, PairingRuleUpdateDto>();

        CreateMap<PairingRuleDetailDto, PairingRuleDetailUpdateDto>();
        CreateMap<CrossRateBulletinDetailDto, CreateUpdateCrossRateBulletinDetailDto>();


        CreateMap<BulletinManagementMasterDto, BulletinManagementMasterUpdateDto>();

        CreateMap<BulletinManagementDetailDto, BulletinManagementDetailUpdateDto>();
        CreateMap<SpreadRuleDto, SpreadRuleUpdateDto>();

        CreateMap<SpreadRuleDetailDto, SpreadRuleDetailUpdateDto>();

        CreateMap<ExchangeRulesDto, ExchangeRulesUpdateDto>();

        CreateMap<ExchangeRuleDetailDto, ExchangeRuleDetailUpdateDto>();
    }
}