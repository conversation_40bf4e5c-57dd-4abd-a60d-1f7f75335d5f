using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public interface ISpreadRuleDetailsAppService : IApplicationService
    {
        Task<PagedResultDto<SpreadRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetSpreadRuleDetailsInput input);

        Task<SpreadRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id);

        Task<SpreadRuleDetailDto> GetAsync(Guid id);

        Task<PagedResultDto<LookupDto<Guid>>> GetSpreadRuleLookupAsync(LookupRequestDto input);

        Task DeleteAsync(Guid id);

        Task<SpreadRuleDetailDto> CreateAsync(SpreadRuleDetailCreateDto input);

        Task<SpreadRuleDetailDto> UpdateAsync(Guid id, SpreadRuleDetailUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleDetailExcelDownloadDto input);

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();
        Task<SpreadRuleDto> UpdateSpreadRuleRowAsync(Guid spreadRuleDetailId, SpreadRuleDetailUpdateDto input);
    }
}