using EMPS.MoneyExchangeManagementService.Shared;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public interface IPairingRuleDetailsAppService : IApplicationService
    {
        Task<PagedResultDto<PairingRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetPairingRuleDetailsInput input);

        Task<PairingRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id);

        Task<PairingRuleDetailDto> GetAsync(Guid id);

        Task<PagedResultDto<LookupDto<Guid>>> GetPairingRuleLookupAsync(LookupRequestDto input);

        Task DeleteAsync(Guid id);

        Task<PairingRuleDetailDto> CreateAsync(PairingRuleDetailCreateDto input);

        Task<PairingRuleDetailDto> UpdateAsync(Guid id, PairingRuleDetailUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleDetailExcelDownloadDto input);

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();

        Task CreateAllActiveCurrenciesPairings(Guid PairingRuleId);

        Task MoveUp(Guid id, Guid pairingRuleId);

        Task MoveDown(Guid id, Guid pairingRuleId);

        Task SwitchPairings(Guid id, Guid pairingRuleId);
    }
}