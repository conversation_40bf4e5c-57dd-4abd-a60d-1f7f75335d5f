using System;
using System.Collections;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin
{
    public class CrossRateBulletinMaster : FullAuditedAggregateRoot<Guid>
    {
        public string Name { get; set; }
        public string? Description { get; set; }
        public bool IsApproved { get; set; }
        public string? ApprovedByUserName { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public bool IsArchived { get; set; }
        public string? ArchivedByUserName { get; set; }
        public Guid? ArchivedByUserId { get; set; }
        public DateTime? ArchivedAt { get; set; }
        public List<CrossRateBulletinDetail> CrossRateBulletinDetails { get; set; }
    }
}