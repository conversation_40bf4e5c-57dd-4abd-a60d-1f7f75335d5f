using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
{
    /// <summary>
    /// DTO for currency exchange rate data displayed in the grid
    /// </summary>
    public class CurrencyExchangeRateDto
    {
        /// <summary>
        /// Currency pair (e.g., USD/EUR, GBP/JPY)
        /// </summary>
        public string CurrencyPair { get; set; }
        
        /// <summary>
        /// Cash bid rate
        /// </summary>
        public double CashBid { get; set; }
        
        /// <summary>
        /// Account bid rate
        /// </summary>
        public double AccountBid { get; set; }
        
        /// <summary>
        /// Cash ask rate
        /// </summary>
        public double CashAsk { get; set; }
        
        /// <summary>
        /// Account ask rate
        /// </summary>
        public double AccountAsk { get; set; }
    }

    /// <summary>
    /// DTO for selectable options in the right panel
    /// </summary>
    public class SelectableOptionDto
    {
        /// <summary>
        /// Unique identifier for the option
        /// </summary>
        public string Id { get; set; }
        
        /// <summary>
        /// Display label for the option
        /// </summary>
        public string Label { get; set; }
        
        /// <summary>
        /// Whether the option is selected
        /// </summary>
        public bool IsSelected { get; set; }
    }

    /// <summary>
    /// Static class to generate dummy data for presentation purposes
    /// </summary>
    public static class DummyDataGenerator
    {
        /// <summary>
        /// Generates a list of dummy currency exchange rate data
        /// </summary>
        public static List<CurrencyExchangeRateDto> GenerateDummyCurrencyRates()
        {
            return new List<CurrencyExchangeRateDto>
            {
                new CurrencyExchangeRateDto { CurrencyPair = "USD/EUR", CashBid = 0.9245, AccountBid = 0.9240, CashAsk = 0.9255, AccountAsk = 0.9260 },
                new CurrencyExchangeRateDto { CurrencyPair = "USD/GBP", CashBid = 0.7845, AccountBid = 0.7840, CashAsk = 0.7855, AccountAsk = 0.7860 },
                new CurrencyExchangeRateDto { CurrencyPair = "USD/JPY", CashBid = 145.45, AccountBid = 145.40, CashAsk = 145.55, AccountAsk = 145.60 },
                new CurrencyExchangeRateDto { CurrencyPair = "EUR/USD", CashBid = 1.0815, AccountBid = 1.0810, CashAsk = 1.0825, AccountAsk = 1.0830 },
                new CurrencyExchangeRateDto { CurrencyPair = "GBP/USD", CashBid = 1.2745, AccountBid = 1.2740, CashAsk = 1.2755, AccountAsk = 1.2760 },
                new CurrencyExchangeRateDto { CurrencyPair = "EUR/GBP", CashBid = 0.8495, AccountBid = 0.8490, CashAsk = 0.8505, AccountAsk = 0.8510 },
                new CurrencyExchangeRateDto { CurrencyPair = "GBP/JPY", CashBid = 185.45, AccountBid = 185.40, CashAsk = 185.55, AccountAsk = 185.60 },
                new CurrencyExchangeRateDto { CurrencyPair = "AUD/USD", CashBid = 0.6645, AccountBid = 0.6640, CashAsk = 0.6655, AccountAsk = 0.6660 },
                new CurrencyExchangeRateDto { CurrencyPair = "USD/CAD", CashBid = 1.3645, AccountBid = 1.3640, CashAsk = 1.3655, AccountAsk = 1.3660 },
                new CurrencyExchangeRateDto { CurrencyPair = "USD/CHF", CashBid = 0.8945, AccountBid = 0.8940, CashAsk = 0.8955, AccountAsk = 0.8960 }
            };
        }

        /// <summary>
        /// Generates a list of dummy selectable options
        /// </summary>
        public static List<SelectableOptionDto> GenerateDummyOptions()
        {
            return new List<SelectableOptionDto>
            {
                new SelectableOptionDto { Id = "opt1", Label = "Option 1", IsSelected = false },
                new SelectableOptionDto { Id = "opt2", Label = "Option 2", IsSelected = false },
                new SelectableOptionDto { Id = "opt3", Label = "Option 3", IsSelected = false },
                new SelectableOptionDto { Id = "opt4", Label = "Option 4", IsSelected = false },
                new SelectableOptionDto { Id = "opt5", Label = "Option 5", IsSelected = false },
                new SelectableOptionDto { Id = "opt6", Label = "Option 6", IsSelected = false },
                new SelectableOptionDto { Id = "opt7", Label = "Option 7", IsSelected = false },
                new SelectableOptionDto { Id = "opt8", Label = "Option 8", IsSelected = false }
            };
        }
    }
}
