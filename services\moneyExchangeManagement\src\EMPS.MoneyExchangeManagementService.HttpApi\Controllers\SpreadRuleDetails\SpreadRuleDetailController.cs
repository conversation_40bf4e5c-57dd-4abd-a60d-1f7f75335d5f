using EMPS.MoneyExchangeManagementService.Shared;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.SpreadRules;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("MoneyExchangeManagementService")]
    [ControllerName("SpreadRuleDetail")]
    [Route("api/money-exchange-management-service/spread-rule-details")]
    public class SpreadRuleDetailController : AbpController, ISpreadRuleDetailsAppService
    {
        private readonly ISpreadRuleDetailsAppService _spreadRuleDetailsAppService;

        public SpreadRuleDetailController(ISpreadRuleDetailsAppService spreadRuleDetailsAppService)
        {
            _spreadRuleDetailsAppService = spreadRuleDetailsAppService;
        }

        [HttpGet]
        public Task<PagedResultDto<SpreadRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetSpreadRuleDetailsInput input)
        {
            return _spreadRuleDetailsAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("with-navigation-properties/{id}")]
        public Task<SpreadRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return _spreadRuleDetailsAppService.GetWithNavigationPropertiesAsync(id);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<SpreadRuleDetailDto> GetAsync(Guid id)
        {
            return _spreadRuleDetailsAppService.GetAsync(id);
        }

        [HttpGet]
        [Route("spread-rule-lookup")]
        public Task<PagedResultDto<LookupDto<Guid>>> GetSpreadRuleLookupAsync(LookupRequestDto input)
        {
            return _spreadRuleDetailsAppService.GetSpreadRuleLookupAsync(input);
        }

        [HttpPost]
        public virtual Task<SpreadRuleDetailDto> CreateAsync(SpreadRuleDetailCreateDto input)
        {
            return _spreadRuleDetailsAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<SpreadRuleDetailDto> UpdateAsync(Guid id, SpreadRuleDetailUpdateDto input)
        {
            return _spreadRuleDetailsAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _spreadRuleDetailsAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleDetailExcelDownloadDto input)
        {
            return _spreadRuleDetailsAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _spreadRuleDetailsAppService.GetDownloadTokenAsync();
        }

        [HttpGet]
        [Route("UpdateSpreadRuleRowAsync")]
        public Task<SpreadRuleDto> UpdateSpreadRuleRowAsync(Guid spreadRuleDetailId, SpreadRuleDetailUpdateDto input)
        {
            return _spreadRuleDetailsAppService.UpdateSpreadRuleRowAsync(spreadRuleDetailId,input);
        }
    }
}