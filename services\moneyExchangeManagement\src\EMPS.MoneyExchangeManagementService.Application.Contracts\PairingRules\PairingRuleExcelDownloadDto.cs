using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{
    public class PairingRuleExcelDownloadDto
    {
        public string DownloadToken { get; set; }

        public string? FilterText { get; set; }

        public string? Name { get; set; }
        public DateTime? EffectiveDateMin { get; set; }
        public DateTime? EffectiveDateMax { get; set; }
        public string? Description { get; set; }
        public bool? IsApproved { get; set; }
        public Guid? ApprovedBy { get; set; }
        public string? ApprovedByName { get; set; }
        public DateTime? ApprovalDateTimeMin { get; set; }
        public DateTime? ApprovalDateTimeMax { get; set; }
        public bool? IsArchived { get; set; }

        public PairingRuleExcelDownloadDto()
        {

        }
    }
}