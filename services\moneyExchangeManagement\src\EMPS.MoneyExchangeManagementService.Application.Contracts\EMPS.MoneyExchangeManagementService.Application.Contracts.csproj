﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>net7.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeManagementService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Authorization" Version="7.3.2" />

    <ProjectReference
      Include="..\..\..\company\src\EMPS.CompanyService.Application.Contracts\EMPS.CompanyService.Application.Contracts.csproj" />

    <ProjectReference
      Include="..\EMPS.MoneyExchangeManagementService.Domain.Shared\EMPS.MoneyExchangeManagementService.Domain.Shared.csproj" />
    <ProjectReference
      Include="..\..\..\company\src\EMPS.CompanyService.Application.Contracts\EMPS.CompanyService.Application.Contracts.csproj" />

    <ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Enum\EMPS.Shared.Enum.csproj" />


  </ItemGroup>

  <ItemGroup>

    <ProjectReference
      Include="..\..\..\customer\src\EMPS.CustomerService.Application.Contracts\EMPS.CustomerService.Application.Contracts.csproj" />

  </ItemGroup>

</Project>