using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using System.Net;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public class EfCoreBulletinManagementMasterRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, BulletinManagementMaster, Guid>, IBulletinManagementMasterRepository
    {
        public EfCoreBulletinManagementMasterRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<BulletinManagementMaster>> GetListAsync(
            string filterText = null,
            string bulletinNumber = null,
            string bulletinName = null,
            DateTime? bulletinDateMin = null,
            DateTime? bulletinDateMax = null,
            string notes = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string currencyPairingRuleName = null,
            Guid? currencyPairingRuleId = null,
            string crossRateBulletinName = null,
            Guid? crossRateBulletinId = null,
            string spreadRuleName = null,
            Guid? spreadRuleId = null,
            string publishByUserName = null,
            Guid? publishByUserId = null,
            DateTime? publishDateMin = null,
            DateTime? publishDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, bulletinNumber, bulletinName, bulletinDateMin, bulletinDateMax, notes, servicePointName, servicePointId, currencyPairingRuleName, currencyPairingRuleId, crossRateBulletinName, crossRateBulletinId, spreadRuleName, spreadRuleId, publishByUserName, publishByUserId, publishDateMin, publishDateMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? BulletinManagementMasterConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string bulletinNumber = null,
            string bulletinName = null,
            DateTime? bulletinDateMin = null,
            DateTime? bulletinDateMax = null,
            string notes = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string currencyPairingRuleName = null,
            Guid? currencyPairingRuleId = null,
            string crossRateBulletinName = null,
            Guid? crossRateBulletinId = null,
            string spreadRuleName = null,
            Guid? spreadRuleId = null,
            string publishByUserName = null,
            Guid? publishByUserId = null,
            DateTime? publishDateMin = null,
            DateTime? publishDateMax = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, bulletinNumber, bulletinName, bulletinDateMin, bulletinDateMax, notes, servicePointName, servicePointId, currencyPairingRuleName, currencyPairingRuleId, crossRateBulletinName, crossRateBulletinId, spreadRuleName, spreadRuleId, publishByUserName, publishByUserId, publishDateMin, publishDateMax);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<BulletinManagementMaster> ApplyFilter(
            IQueryable<BulletinManagementMaster> query,
            string filterText,
            string bulletinNumber = null,
            string bulletinName = null,
            DateTime? bulletinDateMin = null,
            DateTime? bulletinDateMax = null,
            string notes = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string currencyPairingRuleName = null,
            Guid? currencyPairingRuleId = null,
            string crossRateBulletinName = null,
            Guid? crossRateBulletinId = null,
            string spreadRuleName = null,
            Guid? spreadRuleId = null,
            string publishByUserName = null,
            Guid? publishByUserId = null,
            DateTime? publishDateMin = null,
            DateTime? publishDateMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.BulletinNumber.Contains(filterText) || e.BulletinName.Contains(filterText) || e.Notes.Contains(filterText) || e.ServicePointName.Contains(filterText) || e.CurrencyPairingRuleName.Contains(filterText) || e.CrossRateBulletinName.Contains(filterText) || e.SpreadRuleName.Contains(filterText) || e.PublishByUserName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(bulletinNumber), e => e.BulletinNumber.Contains(bulletinNumber))
                    .WhereIf(!string.IsNullOrWhiteSpace(bulletinName), e => e.BulletinName.Contains(bulletinName))
                    .WhereIf(bulletinDateMin.HasValue, e => e.BulletinDate >= bulletinDateMin.Value)
                    .WhereIf(bulletinDateMax.HasValue, e => e.BulletinDate <= bulletinDateMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(notes), e => e.Notes.Contains(notes))
                    .WhereIf(!string.IsNullOrWhiteSpace(servicePointName), e => e.ServicePointName.Contains(servicePointName))
                    .WhereIf(servicePointId.HasValue, e => e.ServicePointId == servicePointId)
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyPairingRuleName), e => e.CurrencyPairingRuleName.Contains(currencyPairingRuleName))
                    .WhereIf(currencyPairingRuleId.HasValue, e => e.CurrencyPairingRuleId == currencyPairingRuleId)
                    .WhereIf(!string.IsNullOrWhiteSpace(crossRateBulletinName), e => e.CrossRateBulletinName.Contains(crossRateBulletinName))
                    .WhereIf(crossRateBulletinId.HasValue, e => e.CrossRateBulletinId == crossRateBulletinId)
                    .WhereIf(!string.IsNullOrWhiteSpace(spreadRuleName), e => e.SpreadRuleName.Contains(spreadRuleName))
                    .WhereIf(spreadRuleId.HasValue, e => e.SpreadRuleId == spreadRuleId)
                    .WhereIf(!string.IsNullOrWhiteSpace(publishByUserName), e => e.PublishByUserName.Contains(publishByUserName))
                    .WhereIf(publishByUserId.HasValue, e => e.PublishByUserId == publishByUserId)
                    .WhereIf(publishDateMin.HasValue, e => e.PublishDate >= publishDateMin.Value)
                    .WhereIf(publishDateMax.HasValue, e => e.PublishDate <= publishDateMax.Value);
        }


        public async Task<int> GenerateNewNo()
        {
            var transactions = await (await GetQueryableAsync())
                .IgnoreQueryFilters()
                .ToListAsync();

            var lastTransaction = transactions
              .OrderByDescending(x => x.BulletinNumber)
              .FirstOrDefault();

            if (lastTransaction == null)
            {
                return 1;
            }

            if (int.TryParse(lastTransaction.BulletinNumber, out int lastIndex))
            {
                return (lastIndex + 1);
            }

            return -1;
        }

        public async Task<BulletinManagementMaster?> GetLastBulletinManagementMasterWithNavForServicePoint(Guid servicePointId)
        {
            var query =(await GetQueryableAsync()).Where(x=>x.ServicePointId== servicePointId);
            if (!query.Any())
                return await GetLastGlobalBulletinManagementMasterWithNav();
            return query.OrderByDescending(c => c.PublishDate).Include(x => x.Details).First();
        }

        public async Task<BulletinManagementMaster?> GetLastGlobalBulletinManagementMasterWithNav()
        {
            var query = (await GetQueryableAsync()).Where(x => x.IsGlobal);
            if (!query.Any())
                return null;
            return query.OrderByDescending(c => c.PublishDate).Include(x => x.Details).First();
        }
    }
}