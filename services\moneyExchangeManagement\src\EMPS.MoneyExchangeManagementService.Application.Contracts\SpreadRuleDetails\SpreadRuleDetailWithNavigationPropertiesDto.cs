using EMPS.MoneyExchangeManagementService.SpreadRules;

using System;
using Volo.Abp.Application.Dtos;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetailWithNavigationPropertiesDto
    {
        public SpreadRuleDetailDto SpreadRuleDetail { get; set; }

        public SpreadRuleDto SpreadRule { get; set; }



        public string? ValidationMessage { get; set; }

        public bool IsBidSpreadInvalid { get; set; }
        public bool IsAskSpreadInvalid { get; set; }
        public bool IsBidMaxDiscountInvalid { get; set; }
        public bool IsBidMaxMarkdownInvalid { get; set; }
        public bool IsAskMaxDiscountInvalid { get; set; }
        public bool IsAskMaxMarkupInvalid { get; set; }

    }
}