﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.Samples;

[RemoteService(Name = MoneyExchangeManagementServiceRemoteServiceConsts.RemoteServiceName)]
[Area("MoneyExchangeManagementService")]
[ControllerName("MoneyExchangeManagementService")]
[Route("api/MoneyExchangeManagementService/sample")]
public class SampleController : MoneyExchangeManagementServiceController, ISampleAppService
{
    private readonly ISampleAppService _sampleAppService;

    public SampleController(ISampleAppService sampleAppService)
    {
        _sampleAppService = sampleAppService;
    }

    [HttpGet]
    public async Task<SampleDto> GetAsync()
    {
        return await _sampleAppService.GetAsync();
    }

    [HttpGet]
    [Route("authorized")]
    public async Task<SampleDto> GetAuthorizedAsync()
    {
        return await _sampleAppService.GetAsync();
    }
}
