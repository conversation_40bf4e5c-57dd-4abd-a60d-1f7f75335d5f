{"Id": "3af24dd0-3947-4063-a559-041fa9e3dd9d", "Name": "SpreadRuleDetail", "OriginalName": "SpreadRuleDetail", "NamePlural": "SpreadRuleDetails", "DatabaseTableName": "SpreadRuleDetails", "Namespace": "SpreadRuleDetails", "BaseClass": "FullAuditedAggregateRoot", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "IsMultiTenant": false, "CheckConcurrency": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": true, "ShouldAddMigration": false, "ShouldUpdateDatabase": false, "CreateTests": true, "Properties": [{"Id": "30d04482-ad1a-474f-a7e6-28bd3d849855", "Name": "CurrencyId", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "2015206d-dfaf-45aa-a7ab-90340a04017c", "Name": "CurrencyCode", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "dd4151d5-a83b-4414-8b8a-ec4feebaf40c", "Name": "Type", "Type": "enum", "EnumType": "SpreadRuleType", "EnumNamespace": "EMPS.Shared.Enum", "EnumAngularImport": "shared/enums/spread-rule-type", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": {"OnCash": 1, "OnAccount": 2}, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "3a20d56a-7924-4615-9654-92326fa1e76e", "Name": "BidSpread", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "c5403e14-d916-4dff-a402-5cbc58a4d84e", "Name": "BidMaxDiscount", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "a9196acf-d7be-42b7-ae10-6f328e844d9f", "Name": "BidMaxMarkdown", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "fee31d5c-ff12-4dbd-abc7-e72a9362e174", "Name": "AskSpread", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "ac47aff3-9954-496a-9867-4af70276f4b8", "Name": "AskMaxDiscount", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "16cf23c8-8629-4662-b7c2-500e2b24d4c7", "Name": "AskMaxMarkup", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": "0", "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}], "NavigationProperties": [{"EntityNameWithDuplicationNumber": "SpreadRule", "EntitySetNameWithDuplicationNumber": "SpreadRules", "ReferencePropertyName": "SpreadRule", "UiPickType": "Dropdown", "IsRequired": false, "Name": "SpreadRuleId", "DisplayProperty": "RuleName", "Namespace": "EMPS.LiquidityService.SpreadRules", "EntityName": "SpreadRule", "EntitySetName": "SpreadRules", "DtoNamespace": "EMPS.LiquidityService.SpreadRules", "DtoEntityName": "SpreadRuleDto", "Type": "Guid"}], "NavigationConnections": [], "PhysicalFileName": "SpreadRuleDetail.json"}