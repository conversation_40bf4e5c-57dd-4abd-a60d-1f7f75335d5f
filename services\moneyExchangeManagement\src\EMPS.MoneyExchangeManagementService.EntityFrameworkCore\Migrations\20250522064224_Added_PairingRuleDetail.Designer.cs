﻿// <auto-generated />
using System;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    [DbContext(typeof(MoneyExchangeManagementServiceDbContext))]
    [Migration("20250522064224_Added_PairingRuleDetail")]
    partial class AddedPairingRuleDetail
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRuleDetails.PairingRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BaseCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BaseCode");

                    b.Property<Guid>("BaseId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("BaseId");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int")
                        .HasColumnName("DisplayOrder");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("PairingFormat")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PairingFormat");

                    b.Property<Guid>("PairingRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("QuoteCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("QuoteCode");

                    b.Property<Guid>("QuoteId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("QuoteId");

                    b.HasKey("Id");

                    b.HasIndex("PairingRuleId");

                    b.ToTable("PairingRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRules.PairingRule", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovalDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDateTime");

                    b.Property<Guid?>("ApprovedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedBy");

                    b.Property<string>("ApprovedByName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByName");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<DateTime?>("EffectiveDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("EffectiveDate");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit")
                        .HasColumnName("IsArchived");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.HasKey("Id");

                    b.ToTable("PairingRules", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.PairingRuleDetails.PairingRuleDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.PairingRules.PairingRule", null)
                        .WithMany()
                        .HasForeignKey("PairingRuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
