using EMPS.Shared.Enum;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetailUpdateDto : IHasConcurrencyStamp
    {
        public Guid CurrencyId { get; set; }
        public string? CurrencyCode { get; set; }
        public SpreadRuleType Type { get; set; }
        public double BidSpread { get; set; }
        public double BidMaxDiscount { get; set; }
        public double BidMaxMarkdown { get; set; }
        public double AskSpread { get; set; }
        public double AskMaxDiscount { get; set; }
        public double AskMaxMarkup { get; set; }
        public Guid? SpreadRuleId { get; set; }

        public string? ConcurrencyStamp { get; set; }
    }
}