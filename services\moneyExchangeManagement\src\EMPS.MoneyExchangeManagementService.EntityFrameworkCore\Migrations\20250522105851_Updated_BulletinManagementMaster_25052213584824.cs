﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class UpdatedBulletinManagementMaster25052213584824 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "PublishByUserId",
                table: "BulletinManagementMasters",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PublishByUserName",
                table: "BulletinManagementMasters",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PublishDate",
                table: "BulletinManagementMasters",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PublishByUserId",
                table: "BulletinManagementMasters");

            migrationBuilder.DropColumn(
                name: "PublishByUserName",
                table: "BulletinManagementMasters");

            migrationBuilder.DropColumn(
                name: "PublishDate",
                table: "BulletinManagementMasters");
        }
    }
}
