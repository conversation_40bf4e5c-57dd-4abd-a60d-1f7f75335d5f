using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class EfCoreExchangeRulesRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, ExchangeRules, Guid>, IExchangeRulesRepository
    {
        public EfCoreExchangeRulesRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<ExchangeRules>> GetListAsync(
            string filterText = null,
            string name = null,
            string description = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            ExchangeRulesScope? exchangeRuleScope = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, name, description, activationDateMin, activationDateMax, exchangeRuleScope, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax, unArchivedByUserId, unArchivedByUserName, unArchivedByDateMin, unArchivedByDateMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? ExchangeRulesConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string name = null,
            string description = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            ExchangeRulesScope? exchangeRuleScope = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, name, description, activationDateMin, activationDateMax, exchangeRuleScope, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax, unArchivedByUserId, unArchivedByUserName, unArchivedByDateMin, unArchivedByDateMax);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<ExchangeRules> ApplyFilter(
            IQueryable<ExchangeRules> query,
            string filterText,
            string name = null,
            string description = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            ExchangeRulesScope? exchangeRuleScope = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string? approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string? archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            Guid? unArchivedByUserId = null,
            string? unArchivedByUserName = null,
            DateTime? unArchivedByDateMin = null,
            DateTime? unArchivedByDateMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.Name.Contains(filterText) || e.Description.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(name), e => e.Name.Contains(name))
                    .WhereIf(!string.IsNullOrWhiteSpace(description), e => e.Description.Contains(description))
                    .WhereIf(activationDateMin.HasValue, e => e.ActivationDate >= activationDateMin.Value)
                    .WhereIf(activationDateMax.HasValue, e => e.ActivationDate <= activationDateMax.Value)
                    .WhereIf(exchangeRuleScope.HasValue, e => e.ExchangeRuleScope == exchangeRuleScope)
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(approvedByUserId.HasValue, e => e.ApprovedByUserId == approvedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserName), e => e.ApprovedByUserName.Contains(approvedByUserName))
                    .WhereIf(approvedDateTimeMin.HasValue, e => e.ApprovedDateTime >= approvedDateTimeMin.Value)
                    .WhereIf(approvedDateTimeMax.HasValue, e => e.ApprovedDateTime <= approvedDateTimeMax.Value)
                    .WhereIf(isArchived.HasValue, e => e.IsArchived == isArchived)
                    .WhereIf(archivedByUserId.HasValue, e => e.ArchivedByUserId == archivedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(archivedByUserName), e => e.ArchivedByUserName.Contains(archivedByUserName))
                    .WhereIf(archivedDateTimeMin.HasValue, e => e.ArchivedDateTime >= archivedDateTimeMin.Value)
                    .WhereIf(archivedDateTimeMax.HasValue, e => e.ArchivedDateTime <= archivedDateTimeMax.Value)
                    .WhereIf(unArchivedByUserId.HasValue, e => e.UnArchivedByUserId == unArchivedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(unArchivedByUserName), e => e.UnArchivedByUserName.Contains(unArchivedByUserName))
                    .WhereIf(unArchivedByDateMin.HasValue, e => e.UnArchivedByDate >= unArchivedByDateMin.Value)
                    .WhereIf(unArchivedByDateMax.HasValue, e => e.UnArchivedByDate <= unArchivedByDateMax.Value);
        }
    }
}