using System;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider;
using EMPS.MoneyExchangeManagementService.Permissions;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using System.Linq;
using EMPS.MoneyExchangeManagementService.Localization;
using System.Collections.Generic;
using EMPS.CompanyService.Currencies;

namespace EMPS.MoneyExchangeManagementService.Application.CrossRateBulletin
{
    [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default)]
    public class CrossRateBulletinMasterAppService :
    CrudAppService<
    CrossRateBulletinMaster,
    CrossRateBulletinMasterDto,
    Guid,
    GetCrossRateBulletinMasterListDto,
    CreateUpdateCrossRateBulletinMasterDto
    >,
        ICrossRateBulletinMasterAppService
    {
        private readonly CrossRateDetailGenerator _detailGenerator;
        private readonly ICurrenciesAppService _currenciesAppService;

        public CrossRateBulletinMasterAppService(
            IRepository<CrossRateBulletinMaster, Guid> repository,
            CrossRateDetailGenerator detailGenerator,
            ICurrenciesAppService currenciesAppService)
            : base(repository)
        {
            _currenciesAppService = currenciesAppService;
            _detailGenerator = detailGenerator;
            LocalizationResource = typeof(MoneyExchangeManagementServiceResource);
            CreatePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create;
            UpdatePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create;
            DeletePolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Delete;
            GetPolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default;
            GetListPolicyName = MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default;
        }

        protected override async Task<IQueryable<CrossRateBulletinMaster>> CreateFilteredQueryAsync(GetCrossRateBulletinMasterListDto input)
        {
            var query = await base.CreateFilteredQueryAsync(input);
            query = query.WhereIf(!string.IsNullOrWhiteSpace(input.FilterText), e => e.Name.Contains(input.FilterText) || e.Description.Contains(input.FilterText) || e.ApprovedByUserName.Contains(input.FilterText) || e.ArchivedByUserName.Contains(input.FilterText));
            query = query.WhereIf(input.IsArchived.HasValue, e => e.IsArchived == input.IsArchived);
            return query;
        }
        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Approve)]
        public async Task ApproveAsync(Guid id)
        {
            var entity = await Repository.GetAsync(id);
            entity.IsApproved = true;
            entity.ApprovedByUserId = CurrentUser.Id;
            entity.ApprovedByUserName = $"{CurrentUser.Name} {CurrentUser.SurName}";
            entity.ApprovedAt = DateTime.Now;
            await Repository.UpdateAsync(entity);


        }

        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Archive)]
        public async Task ArchiveAsync(Guid id)
        {
            var entity = await Repository.GetAsync(id);
            entity.IsArchived = true;
            entity.ArchivedByUserId = CurrentUser.Id;
            entity.ArchivedByUserName = $"{CurrentUser.Name} {CurrentUser.SurName}";
            entity.ArchivedAt = DateTime.Now;
            await Repository.UpdateAsync(entity);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Unarchive)]
        public async Task UnarchiveAsync(Guid id)
        {
            var entity = await Repository.GetAsync(id);
            entity.IsArchived = false;
            entity.ArchivedByUserId = null;
            entity.ArchivedByUserName = null;
            entity.ArchivedAt = null;
            await Repository.UpdateAsync(entity);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create)]
        public override async Task<CrossRateBulletinMasterDto> CreateAsync(CreateUpdateCrossRateBulletinMasterDto input)
        {
            if (await Repository.AnyAsync(x => x.Name == input.Name))
            {
                throw new UserFriendlyException(L["Error:TheNameIsExists"]);
            }
            var entity = await base.CreateAsync(input);
            var currencies = await _currenciesAppService.GetActiveAsync();

            await _detailGenerator.GenerateDetailsForMasterAsync(entity.Id, currencies.Select(x => x.Code).ToList(), 0);
            return entity;
        }

        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create)]
        public override async Task<CrossRateBulletinMasterDto> UpdateAsync(Guid id, CreateUpdateCrossRateBulletinMasterDto input)
        {
            var entity = await Repository.GetAsync(id);
            if (entity.IsApproved)
            {
                throw new UserFriendlyException(L["CannotDeleteApprovedCrossRateBulletinMaster"]);
            }
            if (await Repository.AnyAsync(x => x.Name == input.Name && x.Id != id))
            {
                throw new UserFriendlyException(L["Error:TheNameIsExists"]);
            }
            var currencies = await _currenciesAppService.GetActiveAsync();
            await _detailGenerator.RegenerateDetailsForMasterAsync(entity.Id, currencies.Select(x => x.Code).ToList());

            return await base.UpdateAsync(id, input);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Delete)]
        public override async Task DeleteAsync(Guid id)
        {
            var entity = await Repository.GetAsync(id);
            if (entity.IsApproved)
            {
                throw new UserFriendlyException(L["CannotDeleteApprovedCrossRateBulletinMaster"]);
            }
            await base.DeleteAsync(id);
        }

        public void GetLastApprovedPairingRule()
        {
            List<PairingRuleDto> pairingRules = new List<PairingRuleDto>();
        }

        class PairingRuleDto
        {
            public Guid QuoteCurrencyId { get; set; }
            public string QuoteCurrencyString { get; set; } = string.Empty;

            public Guid BaseCurrencyId { get; set; }
            public string BaseCurrencyString { get; set; } = string.Empty;

            public string Pairing { get; set; } = string.Empty;
        }
    }
}