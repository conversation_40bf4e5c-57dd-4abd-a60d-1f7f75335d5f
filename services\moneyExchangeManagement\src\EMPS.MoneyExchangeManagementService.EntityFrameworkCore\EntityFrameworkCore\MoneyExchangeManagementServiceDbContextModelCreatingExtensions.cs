using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

public static class MoneyExchangeManagementServiceDbContextModelCreatingExtensions
{
    public static void ConfigureMoneyExchangeManagementService(this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        if (builder.IsHostDatabase())
        {
            builder.Entity<SpreadRule>(b =>
            {
                b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "SpreadRules", MoneyExchangeManagementServiceDbProperties.DbSchema);
                b.ConfigureByConvention();
                b.Property(x => x.RuleName).HasColumnName(nameof(SpreadRule.RuleName)).IsRequired();
                b.Property(x => x.ActivationDate).HasColumnName(nameof(SpreadRule.ActivationDate));
                b.Property(x => x.Scope).HasColumnName(nameof(SpreadRule.Scope));
                b.Property(x => x.Description).HasColumnName(nameof(SpreadRule.Description));
                b.Property(x => x.IsApproved).HasColumnName(nameof(SpreadRule.IsApproved));
                b.Property(x => x.ApprovedByUserId).HasColumnName(nameof(SpreadRule.ApprovedByUserId));
                b.Property(x => x.ApprovedByUserName).HasColumnName(nameof(SpreadRule.ApprovedByUserName));
                b.Property(x => x.ApprovedDateTime).HasColumnName(nameof(SpreadRule.ApprovedDateTime));
                b.Property(x => x.IsArchived).HasColumnName(nameof(SpreadRule.IsArchived));
                b.Property(x => x.ArchivedByUserId).HasColumnName(nameof(SpreadRule.ArchivedByUserId));
                b.Property(x => x.ArchivedByUserName).HasColumnName(nameof(SpreadRule.ArchivedByUserName));
                b.Property(x => x.ArchivedDateTime).HasColumnName(nameof(SpreadRule.ArchivedDateTime));
            });


            if (builder.IsHostDatabase())
            {
                builder.Entity<BulletinManagementMaster>(b =>
    {
        b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "BulletinManagementMasters", MoneyExchangeManagementServiceDbProperties.DbSchema);
        b.ConfigureByConvention();
        b.Property(x => x.BulletinNumber).HasColumnName(nameof(BulletinManagementMaster.BulletinNumber)).IsRequired();
        b.Property(x => x.BulletinName).HasColumnName(nameof(BulletinManagementMaster.BulletinName)).IsRequired();
        b.Property(x => x.BulletinDate).HasColumnName(nameof(BulletinManagementMaster.BulletinDate));
        b.Property(x => x.Notes).HasColumnName(nameof(BulletinManagementMaster.Notes));
        b.Property(x => x.ServicePointName).HasColumnName(nameof(BulletinManagementMaster.ServicePointName));
        b.Property(x => x.ServicePointId).HasColumnName(nameof(BulletinManagementMaster.ServicePointId));
        b.Property(x => x.CurrencyPairingRuleName).HasColumnName(nameof(BulletinManagementMaster.CurrencyPairingRuleName));
        b.Property(x => x.CurrencyPairingRuleId).HasColumnName(nameof(BulletinManagementMaster.CurrencyPairingRuleId));
        b.Property(x => x.CrossRateBulletinName).HasColumnName(nameof(BulletinManagementMaster.CrossRateBulletinName));
        b.Property(x => x.CrossRateBulletinId).HasColumnName(nameof(BulletinManagementMaster.CrossRateBulletinId));
        b.Property(x => x.SpreadRuleName).HasColumnName(nameof(BulletinManagementMaster.SpreadRuleName));
        b.Property(x => x.SpreadRuleId).HasColumnName(nameof(BulletinManagementMaster.SpreadRuleId));
        b.Property(x => x.PublishByUserName).HasColumnName(nameof(BulletinManagementMaster.PublishByUserName));
        b.Property(x => x.PublishByUserId).HasColumnName(nameof(BulletinManagementMaster.PublishByUserId));
        b.Property(x => x.PublishDate).HasColumnName(nameof(BulletinManagementMaster.PublishDate));
    });

            }

            if (builder.IsHostDatabase())
            {
                builder.Entity<BulletinManagementDetail>(b =>
    {
        b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "BulletinManagementDetails", MoneyExchangeManagementServiceDbProperties.DbSchema);
        b.ConfigureByConvention();
        b.Property(x => x.CurrencyPair).HasColumnName(nameof(BulletinManagementDetail.CurrencyPair)).IsRequired();
        b.Property(x => x.CashBid).HasColumnName(nameof(BulletinManagementDetail.CashBid));
        b.Property(x => x.CashAsk).HasColumnName(nameof(BulletinManagementDetail.CashAsk));
        b.Property(x => x.AccountBid).HasColumnName(nameof(BulletinManagementDetail.AccountBid));
        b.Property(x => x.AccountAsk).HasColumnName(nameof(BulletinManagementDetail.AccountAsk));
        b.Property(x => x.DisplayOrder).HasColumnName(nameof(BulletinManagementDetail.DisplayOrder));
    });

            }
            if (builder.IsHostDatabase())
            {
                builder.Entity<PairingRule>(b =>
    {
        b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "PairingRules", MoneyExchangeManagementServiceDbProperties.DbSchema);
        b.ConfigureByConvention();
        b.Property(x => x.Name).HasColumnName(nameof(PairingRule.Name)).IsRequired();
        b.Property(x => x.EffectiveDate).HasColumnName(nameof(PairingRule.EffectiveDate));
        b.Property(x => x.Description).HasColumnName(nameof(PairingRule.Description));
        b.Property(x => x.IsApproved).HasColumnName(nameof(PairingRule.IsApproved));
        b.Property(x => x.ApprovedBy).HasColumnName(nameof(PairingRule.ApprovedBy));
        b.Property(x => x.ApprovedByName).HasColumnName(nameof(PairingRule.ApprovedByName));
        b.Property(x => x.ApprovalDateTime).HasColumnName(nameof(PairingRule.ApprovalDateTime));
        b.Property(x => x.IsArchived).HasColumnName(nameof(PairingRule.IsArchived));
    });

            }
            if (builder.IsHostDatabase())
            {
                builder.Entity<PairingRuleDetail>(b =>
    {
        b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "PairingRuleDetails", MoneyExchangeManagementServiceDbProperties.DbSchema);
        b.ConfigureByConvention();
        b.Property(x => x.BaseId).HasColumnName(nameof(PairingRuleDetail.BaseId)).IsRequired();
        b.Property(x => x.BaseCode).HasColumnName(nameof(PairingRuleDetail.BaseCode)).IsRequired();
        b.Property(x => x.QuoteId).HasColumnName(nameof(PairingRuleDetail.QuoteId)).IsRequired();
        b.Property(x => x.QuoteCode).HasColumnName(nameof(PairingRuleDetail.QuoteCode)).IsRequired();
        b.Property(x => x.PairingFormat).HasColumnName(nameof(PairingRuleDetail.PairingFormat));
        b.Property(x => x.DisplayOrder).HasColumnName(nameof(PairingRuleDetail.DisplayOrder));
        b.HasOne<PairingRule>().WithMany().IsRequired().HasForeignKey(x => x.PairingRuleId).OnDelete(DeleteBehavior.NoAction);
    });

            }
        }
        if (builder.IsHostDatabase())
        {
            builder.Entity<SpreadRuleDetail>(b =>
            {
                b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "SpreadRuleDetails", MoneyExchangeManagementServiceDbProperties.DbSchema);
                b.ConfigureByConvention();
                b.Property(x => x.CurrencyId).HasColumnName(nameof(SpreadRuleDetail.CurrencyId));
                b.Property(x => x.CurrencyCode).HasColumnName(nameof(SpreadRuleDetail.CurrencyCode));
                b.Property(x => x.Type).HasColumnName(nameof(SpreadRuleDetail.Type));
                b.Property(x => x.BidSpread).HasColumnName(nameof(SpreadRuleDetail.BidSpread));
                b.Property(x => x.BidMaxDiscount).HasColumnName(nameof(SpreadRuleDetail.BidMaxDiscount));
                b.Property(x => x.BidMaxMarkdown).HasColumnName(nameof(SpreadRuleDetail.BidMaxMarkdown));
                b.Property(x => x.AskSpread).HasColumnName(nameof(SpreadRuleDetail.AskSpread));
                b.Property(x => x.AskMaxDiscount).HasColumnName(nameof(SpreadRuleDetail.AskMaxDiscount));
                b.Property(x => x.AskMaxMarkup).HasColumnName(nameof(SpreadRuleDetail.AskMaxMarkup));
            });
        }


        // Configure CrossRateProviderConfiguration
        if (builder.IsHostDatabase())
        {
            builder.Entity<CrossRateProviderConfiguration>(b =>
            {
                b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "CrossRateProviderConfigurations", MoneyExchangeManagementServiceDbProperties.DbSchema);
                b.ConfigureByConvention();
                b.Property(x => x.ProviderBaseUrl).HasColumnName(nameof(CrossRateProviderConfiguration.ProviderBaseUrl)).IsRequired().HasMaxLength(500);
                b.Property(x => x.ProviderAccessToken).HasColumnName(nameof(CrossRateProviderConfiguration.ProviderAccessToken)).IsRequired().HasMaxLength(1000);
                b.Property(x => x.RequestBaseCurrency).HasColumnName(nameof(CrossRateProviderConfiguration.RequestBaseCurrency)).IsRequired().HasMaxLength(3);
            });
        }
        if (builder.IsHostDatabase())
        {
            builder.Entity<ExchangeRules>(b =>
{
    b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "ExchangeRuless", MoneyExchangeManagementServiceDbProperties.DbSchema);
    b.ConfigureByConvention();
    b.Property(x => x.Name).HasColumnName(nameof(ExchangeRules.Name)).IsRequired();
    b.Property(x => x.Description).HasColumnName(nameof(ExchangeRules.Description));
    b.Property(x => x.ActivationDate).HasColumnName(nameof(ExchangeRules.ActivationDate));
    b.Property(x => x.ExchangeRuleScope).HasColumnName(nameof(ExchangeRules.ExchangeRuleScope));
});

        }
        if (builder.IsHostDatabase())
        {
            builder.Entity<ExchangeRuleDetail>(b =>
{
    b.ToTable(MoneyExchangeManagementServiceDbProperties.DbTablePrefix + "ExchangeRuleDetails", MoneyExchangeManagementServiceDbProperties.DbSchema);
    b.ConfigureByConvention();
    b.Property(x => x.CurrencyName).HasColumnName(nameof(ExchangeRuleDetail.CurrencyName));
    b.Property(x => x.CurrencyID).HasColumnName(nameof(ExchangeRuleDetail.CurrencyID));
    b.Property(x => x.AllowedToBuy).HasColumnName(nameof(ExchangeRuleDetail.AllowedToBuy));
    b.Property(x => x.MinAmountToBuy).HasColumnName(nameof(ExchangeRuleDetail.MinAmountToBuy));
    b.Property(x => x.MaxAmountToBuy).HasColumnName(nameof(ExchangeRuleDetail.MaxAmountToBuy));
    b.Property(x => x.MaxDailyAmountToBuy).HasColumnName(nameof(ExchangeRuleDetail.MaxDailyAmountToBuy));
    b.Property(x => x.AllowedToSell).HasColumnName(nameof(ExchangeRuleDetail.AllowedToSell));
    b.Property(x => x.MinAmountToSell).HasColumnName(nameof(ExchangeRuleDetail.MinAmountToSell));
    b.Property(x => x.MaxAmountToSell).HasColumnName(nameof(ExchangeRuleDetail.MaxAmountToSell));
    b.Property(x => x.MaxDailyAmountToSell).HasColumnName(nameof(ExchangeRuleDetail.MaxDailyAmountToSell));
    b.Property(x => x.AllowedToSellBelowCenterCost).HasColumnName(nameof(ExchangeRuleDetail.AllowedToSellBelowCenterCost));
    b.Property(x => x.AllowedToSellBelowCompanyCost).HasColumnName(nameof(ExchangeRuleDetail.AllowedToSellBelowCompanyCost));
    b.Property(x => x.ExchangeRuleMasterID).HasColumnName(nameof(ExchangeRuleDetail.ExchangeRuleMasterID));
    b.Property(x => x.ExchangeRuleDetailType).HasColumnName(nameof(ExchangeRuleDetail.ExchangeRuleDetailType));
});

        }
    }
}