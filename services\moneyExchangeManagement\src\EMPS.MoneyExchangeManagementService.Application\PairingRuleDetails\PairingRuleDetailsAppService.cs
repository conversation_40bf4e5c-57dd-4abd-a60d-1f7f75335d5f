using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.PairingRules;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.CompanyService.Currencies;
using Bogus.DataSets;
using Org.BouncyCastle.Utilities;
using Volo.Abp.ObjectMapping;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{

    [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Default)]
    public class PairingRuleDetailsAppService : ApplicationService, IPairingRuleDetailsAppService
    {
        private readonly IDistributedCache<PairingRuleDetailExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly IPairingRuleDetailRepository _pairingRuleDetailRepository;
        private readonly PairingRuleDetailManager _pairingRuleDetailManager;
        private readonly IRepository<PairingRule, Guid> _pairingRuleRepository;
        private readonly ICurrenciesAppService _currenciesAppService;

        public PairingRuleDetailsAppService(IPairingRuleDetailRepository pairingRuleDetailRepository, PairingRuleDetailManager pairingRuleDetailManager, IDistributedCache<PairingRuleDetailExcelDownloadTokenCacheItem, string> excelDownloadTokenCache, IRepository<PairingRule, Guid> pairingRuleRepository, ICurrenciesAppService currenciesAppService)
        {
            _excelDownloadTokenCache = excelDownloadTokenCache;
            _pairingRuleDetailRepository = pairingRuleDetailRepository;
            _pairingRuleDetailManager = pairingRuleDetailManager; _pairingRuleRepository = pairingRuleRepository;
            _currenciesAppService = currenciesAppService;
        }

        public virtual async Task<PagedResultDto<PairingRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetPairingRuleDetailsInput input)
        {
            var totalCount = await _pairingRuleDetailRepository.GetCountAsync(input.FilterText, input.BaseId, input.BaseCode, input.QuoteId, input.QuoteCode, input.PairingFormat, input.DisplayOrderMin, input.DisplayOrderMax, input.PairingRuleId);
            var items = await _pairingRuleDetailRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.BaseId, input.BaseCode, input.QuoteId, input.QuoteCode, input.PairingFormat, input.DisplayOrderMin, input.DisplayOrderMax, input.PairingRuleId, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<PairingRuleDetailWithNavigationPropertiesDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<PairingRuleDetailWithNavigationProperties>, List<PairingRuleDetailWithNavigationPropertiesDto>>(items)
            };
        }

        public virtual async Task<PairingRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return ObjectMapper.Map<PairingRuleDetailWithNavigationProperties, PairingRuleDetailWithNavigationPropertiesDto>
                (await _pairingRuleDetailRepository.GetWithNavigationPropertiesAsync(id));
        }

        public virtual async Task<PairingRuleDetailDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<PairingRuleDetail, PairingRuleDetailDto>(await _pairingRuleDetailRepository.GetAsync(id));
        }

        public virtual async Task<PagedResultDto<LookupDto<Guid>>> GetPairingRuleLookupAsync(LookupRequestDto input)
        {
            var query = (await _pairingRuleRepository.GetQueryableAsync())
                .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                    x => x.Name != null &&
                         x.Name.Contains(input.Filter));

            var lookupData = await query.PageBy(input.SkipCount, input.MaxResultCount).ToDynamicListAsync<PairingRule>();
            var totalCount = query.Count();
            return new PagedResultDto<LookupDto<Guid>>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<PairingRule>, List<LookupDto<Guid>>>(lookupData)
            };
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _pairingRuleDetailRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Create)]
        public virtual async Task<PairingRuleDetailDto> CreateAsync(PairingRuleDetailCreateDto input)
        {


            if (input.PairingRuleId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["PairingRule"]]);
            }

            var pairingRuleDetail = await _pairingRuleDetailManager.CreateAsync(
            input.PairingRuleId, input.BaseId, input.BaseCode, input.QuoteId, input.QuoteCode, input.PairingFormat, input.DisplayOrder
            );

            return ObjectMapper.Map<PairingRuleDetail, PairingRuleDetailDto>(pairingRuleDetail);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Edit)]
        public virtual async Task<PairingRuleDetailDto> UpdateAsync(Guid id, PairingRuleDetailUpdateDto input)
        {
            if (input.PairingRuleId == default)
            {
                throw new UserFriendlyException(L["The {0} field is required.", L["PairingRule"]]);
            }

            var pairingRuleDetail = await _pairingRuleDetailManager.UpdateAsync(
            id,
            input.PairingRuleId, input.BaseId, input.BaseCode, input.QuoteId, input.QuoteCode, input.PairingFormat, input.DisplayOrder, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<PairingRuleDetail, PairingRuleDetailDto>(pairingRuleDetail);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleDetailExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var pairingRuleDetails = await _pairingRuleDetailRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.BaseId, input.BaseCode, input.QuoteId, input.QuoteCode, input.PairingFormat, input.DisplayOrderMin, input.DisplayOrderMax);
            var items = pairingRuleDetails.Select(item => new
            {
                BaseId = item.PairingRuleDetail.BaseId,
                BaseCode = item.PairingRuleDetail.BaseCode,
                QuoteId = item.PairingRuleDetail.QuoteId,
                QuoteCode = item.PairingRuleDetail.QuoteCode,
                PairingFormat = item.PairingRuleDetail.PairingFormat,
                DisplayOrder = item.PairingRuleDetail.DisplayOrder,

                PairingRule = item.PairingRule?.Name,

            });

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(items);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "PairingRuleDetails.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new PairingRuleDetailExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }


        

        public async Task CreateAllActiveCurrenciesPairings(Guid PairingRuleId)
        {

            PairingRuleDetailCreateDto input = new PairingRuleDetailCreateDto();

            input.PairingRuleId = PairingRuleId;

            int dispOrder = 1;

            var currencies = await _currenciesAppService.GetActiveAsync();
            var syp = await _currenciesAppService.GetLocalCurrency();
            input.QuoteCode = syp.Code;
            input.QuoteId = syp.Id;

            foreach ( var currency in currencies)
            {
                input.DisplayOrder = dispOrder; 
                input.BaseId = currency.Id;
                input.BaseCode = currency.Code;
                input.PairingFormat = input.BaseCode + "/" + input.QuoteCode;
                await CreateAsync(input);
                dispOrder++;
            }

        }

        public async Task MoveUp(Guid id, Guid pairingRuleId)
        {
            var currentPair = await _pairingRuleDetailRepository.GetAsync(id);

            var secondPair = await _pairingRuleDetailRepository.FirstOrDefaultAsync(
                x => x.PairingRuleId == pairingRuleId && x.DisplayOrder == currentPair.DisplayOrder - 1
            );

            if (secondPair == null)
            {
                return;
            }


            currentPair.DisplayOrder -= 1;

            secondPair.DisplayOrder += 1;


            await _pairingRuleDetailManager.UpdateOrder(currentPair);
            await _pairingRuleDetailManager.UpdateOrder(secondPair);
        }

        public async Task MoveDown(Guid id, Guid pairingRuleId)
        {
            var currentPair = await _pairingRuleDetailRepository.GetAsync(id);

            var secondPair = await _pairingRuleDetailRepository.FirstOrDefaultAsync(
                x => x.PairingRuleId == pairingRuleId && x.DisplayOrder == currentPair.DisplayOrder + 1
            );

            if (secondPair == null)
            {
                return;
            }

            currentPair.DisplayOrder += 1;

            secondPair.DisplayOrder -= 1;

            await _pairingRuleDetailManager.UpdateOrder(currentPair);
            await _pairingRuleDetailManager.UpdateOrder(secondPair);
        }

        public async Task SwitchPairings(Guid id, Guid pairingRuleId)
        {
            var currentPairings = await _pairingRuleDetailRepository.GetAsync(id);

            var pairingToBeUpdated = currentPairings;

            // Swap BaseCode and QuoteCode
            var tempCode = pairingToBeUpdated.BaseCode;
            pairingToBeUpdated.BaseCode = pairingToBeUpdated.QuoteCode;
            pairingToBeUpdated.QuoteCode = tempCode;

            // Swap BaseId and QuoteId
            var tempId = pairingToBeUpdated.BaseId;
            pairingToBeUpdated.BaseId = pairingToBeUpdated.QuoteId;
            pairingToBeUpdated.QuoteId = tempId;


            pairingToBeUpdated.PairingFormat = pairingToBeUpdated.BaseCode + "/" + pairingToBeUpdated.QuoteCode;

            pairingToBeUpdated.ConcurrencyStamp = currentPairings.ConcurrencyStamp;

            await _pairingRuleDetailManager.Switch(pairingToBeUpdated);



        }
    }
}