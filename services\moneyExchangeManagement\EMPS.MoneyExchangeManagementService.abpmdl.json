{"folders": {"items": {"src": {}, "test": {}}}, "packages": {"EMPS.MoneyExchangeManagementService.Domain.Shared": {"path": "src/EMPS.MoneyExchangeManagementService.Domain.Shared/EMPS.MoneyExchangeManagementService.Domain.Shared.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.Application.Contracts": {"path": "src/EMPS.MoneyExchangeManagementService.Application.Contracts/EMPS.MoneyExchangeManagementService.Application.Contracts.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.Domain": {"path": "src/EMPS.MoneyExchangeManagementService.Domain/EMPS.MoneyExchangeManagementService.Domain.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.Application": {"path": "src/EMPS.MoneyExchangeManagementService.Application/EMPS.MoneyExchangeManagementService.Application.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.EntityFrameworkCore": {"path": "src/EMPS.MoneyExchangeManagementService.EntityFrameworkCore/EMPS.MoneyExchangeManagementService.EntityFrameworkCore.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.HttpApi": {"path": "src/EMPS.MoneyExchangeManagementService.HttpApi/EMPS.MoneyExchangeManagementService.HttpApi.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.HttpApi.Client": {"path": "src/EMPS.MoneyExchangeManagementService.HttpApi.Client/EMPS.MoneyExchangeManagementService.HttpApi.Client.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.HttpApi.Host": {"path": "src/EMPS.MoneyExchangeManagementService.HttpApi.Host/EMPS.MoneyExchangeManagementService.HttpApi.Host.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.Blazor": {"path": "src/EMPS.MoneyExchangeManagementService.Blazor/EMPS.MoneyExchangeManagementService.Blazor.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeManagementService.Domain.Tests": {"path": "test/EMPS.MoneyExchangeManagementService.Domain.Tests/EMPS.MoneyExchangeManagementService.Domain.Tests.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeManagementService.EntityFrameworkCore.Tests": {"path": "test/EMPS.MoneyExchangeManagementService.EntityFrameworkCore.Tests/EMPS.MoneyExchangeManagementService.EntityFrameworkCore.Tests.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeManagementService.TestBase": {"path": "test/EMPS.MoneyExchangeManagementService.TestBase/EMPS.MoneyExchangeManagementService.TestBase.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeManagementService.Application.Tests": {"path": "test/EMPS.MoneyExchangeManagementService.Application.Tests/EMPS.MoneyExchangeManagementService.Application.Tests.abppkg.json", "folder": "test"}}}