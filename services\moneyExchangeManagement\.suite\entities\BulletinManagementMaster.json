{"Id": "3f953e58-1899-4834-a2dc-caa380849d17", "Name": "BulletinManagementMaster", "OriginalName": "BulletinManagementMaster", "NamePlural": "BulletinManagementMasters", "DatabaseTableName": "BulletinManagementMasters", "Namespace": "BulletinManagementMasters", "BaseClass": "FullAuditedAggregateRoot", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "IsMultiTenant": false, "CheckConcurrency": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": false, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": false, "Properties": [{"Id": "4dc5a957-9e8f-4b41-a67f-5a5a1fd81633", "Name": "BulletinNumber", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "9ab0a9f6-0227-4226-9c8c-10095ec3441c", "Name": "BulletinName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "1c733f39-3ce9-403e-91c1-edc6964422d5", "Name": "BulletinDate", "Type": "DateTime", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "af5174f8-65fa-4936-aafb-5c3b94eac14e", "Name": "Notes", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": true, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "742e656e-aa6d-4ac1-a3c8-1a79149f93bf", "Name": "ServicePointName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "eff7dd4b-e57d-47da-bee0-0f9638d8522b", "Name": "ServicePointId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "04021067-f679-412e-bf92-8cd751324745", "Name": "CurrencyPairingRuleName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "8aa1c177-87c1-438d-85c3-707fa219ad61", "Name": "CurrencyPairingRuleId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "9d94e1c4-f5a2-4c71-a0dc-fba4e35dbd6e", "Name": "CrossRateBulletinName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "12e93230-6379-475d-a318-91625b9eb95f", "Name": "CrossRateBulletinId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "28737e23-b553-41bd-b3f2-7f931b6cd2c6", "Name": "SpreadRuleName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "8e9104b4-460d-4f65-b3e9-0e669485f269", "Name": "SpreadRuleId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "a9f5ef40-fc27-4364-8080-e51f6acb5d4a", "Name": "PublishByUserName", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "27c4e470-fcbe-40e1-bba8-d27aaf68eb4f", "Name": "PublishByUserId", "Type": "Guid", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "b62cdf7e-8efd-496c-945e-1e0473aea5df", "Name": "PublishDate", "Type": "DateTime", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": true, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}], "NavigationProperties": [], "NavigationConnections": [], "PhysicalFileName": "BulletinManagementMaster.json"}