using EMPS.MoneyExchangeManagementService.Shared;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("BulletinManagementDetail")]
    [Route("api/money-exchange-management-service/bulletin-management-details")]
    public class BulletinManagementDetailController : AbpController, IBulletinManagementDetailsAppService
    {
        private readonly IBulletinManagementDetailsAppService _bulletinManagementDetailsAppService;

        public BulletinManagementDetailController(IBulletinManagementDetailsAppService bulletinManagementDetailsAppService)
        {
            _bulletinManagementDetailsAppService = bulletinManagementDetailsAppService;
        }

        [HttpGet]
        public Task<PagedResultDto<BulletinManagementDetailWithNavigationPropertiesDto>> GetListAsync(GetBulletinManagementDetailsInput input)
        {
            return _bulletinManagementDetailsAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("with-navigation-properties/{id}")]
        public Task<BulletinManagementDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return _bulletinManagementDetailsAppService.GetWithNavigationPropertiesAsync(id);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<BulletinManagementDetailDto> GetAsync(Guid id)
        {
            return _bulletinManagementDetailsAppService.GetAsync(id);
        }

        [HttpGet]
        [Route("bulletin-management-master-lookup")]
        public Task<PagedResultDto<LookupDto<Guid>>> GetBulletinManagementMasterLookupAsync(LookupRequestDto input)
        {
            return _bulletinManagementDetailsAppService.GetBulletinManagementMasterLookupAsync(input);
        }

        [HttpPost]
        public virtual Task<BulletinManagementDetailDto> CreateAsync(BulletinManagementDetailCreateDto input)
        {
            return _bulletinManagementDetailsAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<BulletinManagementDetailDto> UpdateAsync(Guid id, BulletinManagementDetailUpdateDto input)
        {
            return _bulletinManagementDetailsAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _bulletinManagementDetailsAppService.DeleteAsync(id);
        }
    }
}