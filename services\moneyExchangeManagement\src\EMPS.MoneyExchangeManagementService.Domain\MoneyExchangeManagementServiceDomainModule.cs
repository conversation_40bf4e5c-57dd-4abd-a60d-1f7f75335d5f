﻿using EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Domain;
using Volo.Abp.Caching;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService;

[DependsOn(
    typeof(AbpDddDomainModule),
    typeof(AbpCachingModule),
    typeof(MoneyExchangeManagementServiceDomainSharedModule)
)]
public class MoneyExchangeManagementServiceDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Register HttpClient for CurrencyLayerProvider
        // context.Services.AddHttpClient<CurrencyLayerProvider>();

        // Register the CurrencyLayerProvider as the implementation for ICrossLimitProvider
        // Comment out the line below to use FakeCrossLimitProvider instead
        context.Services.AddTransient<ICrossLimitProvider, CurrencyLayerProvider>();

        // Uncomment the line below to use FakeCrossLimitProvider for testing
        // context.Services.AddTransient<ICrossLimitProvider, FakeCrossLimitProvider>();

        // Register the CrossRateDetailGenerator service
        context.Services.AddTransient<CrossRateDetailGenerator>();
    }
}
