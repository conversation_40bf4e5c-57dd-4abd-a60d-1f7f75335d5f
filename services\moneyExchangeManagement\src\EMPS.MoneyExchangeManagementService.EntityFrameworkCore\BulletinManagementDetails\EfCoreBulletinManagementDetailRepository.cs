using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class EfCoreBulletinManagementDetailRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, BulletinManagementDetail, Guid>, IBulletinManagementDetailRepository
    {
        public EfCoreBulletinManagementDetailRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<BulletinManagementDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            return (await GetDbSetAsync()).Where(b => b.Id == id)
                .Select(bulletinManagementDetail => new BulletinManagementDetailWithNavigationProperties
                {
                    BulletinManagementDetail = bulletinManagementDetail,
                    BulletinManagementMaster = dbContext.Set<BulletinManagementMaster>().FirstOrDefault(c => c.Id == bulletinManagementDetail.BulletinManagementMasterId)
                }).FirstOrDefault();
        }

        public async Task<List<BulletinManagementDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? bulletinManagementMasterId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, currencyPair, cashBidMin, cashBidMax, cashAskMin, cashAskMax, accountBidMin, accountBidMax, accountAskMin, accountAskMax, displayOrderMin, displayOrderMax, bulletinManagementMasterId);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? BulletinManagementDetailConsts.GetDefaultSorting(true) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        protected virtual async Task<IQueryable<BulletinManagementDetailWithNavigationProperties>> GetQueryForNavigationPropertiesAsync()
        {
            return from bulletinManagementDetail in (await GetDbSetAsync())
                   join bulletinManagementMaster in (await GetDbContextAsync()).Set<BulletinManagementMaster>() on bulletinManagementDetail.BulletinManagementMasterId equals bulletinManagementMaster.Id into bulletinManagementMasters
                   from bulletinManagementMaster in bulletinManagementMasters.DefaultIfEmpty()
                   select new BulletinManagementDetailWithNavigationProperties
                   {
                       BulletinManagementDetail = bulletinManagementDetail,
                       BulletinManagementMaster = bulletinManagementMaster
                   };
        }

        protected virtual IQueryable<BulletinManagementDetailWithNavigationProperties> ApplyFilter(
            IQueryable<BulletinManagementDetailWithNavigationProperties> query,
            string filterText,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? bulletinManagementMasterId = null)
        {
            return query
                .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.BulletinManagementDetail.CurrencyPair.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyPair), e => e.BulletinManagementDetail.CurrencyPair.Contains(currencyPair))
                    .WhereIf(cashBidMin.HasValue, e => e.BulletinManagementDetail.CashBid >= cashBidMin.Value)
                    .WhereIf(cashBidMax.HasValue, e => e.BulletinManagementDetail.CashBid <= cashBidMax.Value)
                    .WhereIf(cashAskMin.HasValue, e => e.BulletinManagementDetail.CashAsk >= cashAskMin.Value)
                    .WhereIf(cashAskMax.HasValue, e => e.BulletinManagementDetail.CashAsk <= cashAskMax.Value)
                    .WhereIf(accountBidMin.HasValue, e => e.BulletinManagementDetail.AccountBid >= accountBidMin.Value)
                    .WhereIf(accountBidMax.HasValue, e => e.BulletinManagementDetail.AccountBid <= accountBidMax.Value)
                    .WhereIf(accountAskMin.HasValue, e => e.BulletinManagementDetail.AccountAsk >= accountAskMin.Value)
                    .WhereIf(accountAskMax.HasValue, e => e.BulletinManagementDetail.AccountAsk <= accountAskMax.Value)
                    .WhereIf(displayOrderMin.HasValue, e => e.BulletinManagementDetail.DisplayOrder >= displayOrderMin.Value)
                    .WhereIf(displayOrderMax.HasValue, e => e.BulletinManagementDetail.DisplayOrder <= displayOrderMax.Value)
                    .WhereIf(bulletinManagementMasterId != null && bulletinManagementMasterId != Guid.Empty, e => e.BulletinManagementMaster != null && e.BulletinManagementMaster.Id == bulletinManagementMasterId);
        }

        public async Task<List<BulletinManagementDetail>> GetListAsync(
            string filterText = null,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, currencyPair, cashBidMin, cashBidMax, cashAskMin, cashAskMax, accountBidMin, accountBidMax, accountAskMin, accountAskMax, displayOrderMin, displayOrderMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? BulletinManagementDetailConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? bulletinManagementMasterId = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, currencyPair, cashBidMin, cashBidMax, cashAskMin, cashAskMax, accountBidMin, accountBidMax, accountAskMin, accountAskMax, displayOrderMin, displayOrderMax, bulletinManagementMasterId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<BulletinManagementDetail> ApplyFilter(
            IQueryable<BulletinManagementDetail> query,
            string filterText,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.CurrencyPair.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyPair), e => e.CurrencyPair.Contains(currencyPair))
                    .WhereIf(cashBidMin.HasValue, e => e.CashBid >= cashBidMin.Value)
                    .WhereIf(cashBidMax.HasValue, e => e.CashBid <= cashBidMax.Value)
                    .WhereIf(cashAskMin.HasValue, e => e.CashAsk >= cashAskMin.Value)
                    .WhereIf(cashAskMax.HasValue, e => e.CashAsk <= cashAskMax.Value)
                    .WhereIf(accountBidMin.HasValue, e => e.AccountBid >= accountBidMin.Value)
                    .WhereIf(accountBidMax.HasValue, e => e.AccountBid <= accountBidMax.Value)
                    .WhereIf(accountAskMin.HasValue, e => e.AccountAsk >= accountAskMin.Value)
                    .WhereIf(accountAskMax.HasValue, e => e.AccountAsk <= accountAskMax.Value)
                    .WhereIf(displayOrderMin.HasValue, e => e.DisplayOrder >= displayOrderMin.Value)
                    .WhereIf(displayOrderMax.HasValue, e => e.DisplayOrder <= displayOrderMax.Value);
        }
    }
}