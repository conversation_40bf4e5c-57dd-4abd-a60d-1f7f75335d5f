using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class PairingRuleDetailManager : DomainService
    {
        private readonly IPairingRuleDetailRepository _pairingRuleDetailRepository;

        public PairingRuleDetailManager(IPairingRuleDetailRepository pairingRuleDetailRepository)
        {
            _pairingRuleDetailRepository = pairingRuleDetailRepository;
        }

        public async Task<PairingRuleDetail> CreateAsync(
        Guid pairingRuleId, Guid baseId, string baseCode, Guid quoteId, string quoteCode, string pairingFormat, int displayOrder)
        {
            Check.NotNull(pairingRuleId, nameof(pairingRuleId));
            Check.NotNullOrWhiteSpace(baseCode, nameof(baseCode));
            Check.NotNullOrWhiteSpace(quoteCode, nameof(quoteCode));

            var pairingRuleDetail = new PairingRuleDetail(
             GuidGenerator.Create(),
             pairingRuleId, baseId, baseCode, quoteId, quoteCode, pairingFormat, displayOrder
             );

            return await _pairingRuleDetailRepository.InsertAsync(pairingRuleDetail);
        }

        public async Task<PairingRuleDetail> UpdateAsync(
            Guid id,
            Guid pairingRuleId, Guid baseId, string baseCode, Guid quoteId, string quoteCode, string pairingFormat, int displayOrder, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(pairingRuleId, nameof(pairingRuleId));
            Check.NotNullOrWhiteSpace(baseCode, nameof(baseCode));
            Check.NotNullOrWhiteSpace(quoteCode, nameof(quoteCode));

            var pairingRuleDetail = await _pairingRuleDetailRepository.GetAsync(id);

            pairingRuleDetail.PairingRuleId = pairingRuleId;
            pairingRuleDetail.BaseId = baseId;
            pairingRuleDetail.BaseCode = baseCode;
            pairingRuleDetail.QuoteId = quoteId;
            pairingRuleDetail.QuoteCode = quoteCode;
            pairingRuleDetail.PairingFormat = pairingFormat;
            pairingRuleDetail.DisplayOrder = displayOrder;

            pairingRuleDetail.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _pairingRuleDetailRepository.UpdateAsync(pairingRuleDetail);
        }


        public async Task<PairingRuleDetail> UpdateOrder(PairingRuleDetail pair)
        {
           
            var pairingRuleDetail = await _pairingRuleDetailRepository.GetAsync(pair.Id);

            pairingRuleDetail.DisplayOrder = pair.DisplayOrder;

            pairingRuleDetail.SetConcurrencyStampIfNotNull(pair.ConcurrencyStamp);
            return await _pairingRuleDetailRepository.UpdateAsync(pairingRuleDetail);
        }

        public async Task<PairingRuleDetail> Switch(PairingRuleDetail pair)
        {

            
            pair.SetConcurrencyStampIfNotNull(pair.ConcurrencyStamp);
            return await _pairingRuleDetailRepository.UpdateAsync(pair);
        }

    }
}