@page "/cross-rate-bulletin-detail/{MasterId}"
@namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
@attribute [Authorize(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Default)]
@inherits MoneyExchangeManagementServiceComponentBase
@using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Blazorise
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.Application.Dtos
@using EMPS.MoneyExchangeManagementService.Localization

@inject ICrossRateBulletinDetailAppService AppService
@inject IStringLocalizer<MoneyExchangeManagementServiceResource> L

@if (!IsModal)
{
    <PageHeader Title="@L["CrossRateBulletinDetail"]" BreadcrumbItems="BreadcrumbItems">
</PageHeader>
}

<Card>

    <CardBody>
        <Div>
            <Button Disabled="@((masterDto?.IsApproved ?? false) || IsRefreshingData)" Color="Color.Secondary"
                Clicked="RefetchData" Loading="IsRefreshingData" Size="Size.Small">
                @if (IsRefreshingData)
                {
                    <Icon Name="IconName.Download" Class="me-1"></Icon>
                    @L["Refreshing"]
                }
                else
                {
                    <Icon Name="IconName.Download" Class="me-1"></Icon>
                    @L["FetchData"]
                }
            </Button>
        </Div>
        <DataGrid TItem="CrossRateBulletinDetailDto" Data="@Details" ReadData="GetDetailsAsync" ShowPager="false">
            <DataGridColumns>

                <DataGridColumn TItem="CrossRateBulletinDetailDto"
                    Field="@nameof(CrossRateBulletinDetailDto.PairingFormat)" Caption="@L["PairingFormat"]" />


                <DataGridColumn TItem="CrossRateBulletinDetailDto" Width="200px"
                    Field="@nameof(CrossRateBulletinDetailDto.CrossRateValue)" Caption="@L["CrossRateValue"]">
                    <DisplayTemplate>
                        @if (context.IsUsdSypRecord() && !masterDto.IsApproved)
                        {
                            @* <NumericPicker TValue="double" Value="@context.CrossRateValue" GroupSeparator=","
                                ValueChanged="async (newVal)=>await OnUsdSypRowRateValueChanged(context,newVal)" /> *@

                            <NumericEdit TValue="double" Value="@context.CrossRateValue"
                                ValueChanged="async (newVal)=>await OnUsdSypRowRateValueChanged(context,newVal)" />
                        }
                        else
                        {
                            @context.CrossRateValue.ToString("F4")
                        }


                    </DisplayTemplate>
                </DataGridColumn>
            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>