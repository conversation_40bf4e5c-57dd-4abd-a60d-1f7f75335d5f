using EMPS.MoneyExchangeManagementService.PairingRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class EfCorePairingRuleDetailRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, PairingRuleDetail, Guid>, IPairingRuleDetailRepository
    {
        public EfCorePairingRuleDetailRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<PairingRuleDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            return (await GetDbSetAsync()).Where(b => b.Id == id)
                .Select(pairingRuleDetail => new PairingRuleDetailWithNavigationProperties
                {
                    PairingRuleDetail = pairingRuleDetail,
                    PairingRule = dbContext.Set<PairingRule>().FirstOrDefault(c => c.Id == pairingRuleDetail.PairingRuleId)
                }).FirstOrDefault();
        }

        public async Task<List<PairingRuleDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? pairingRuleId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, baseId, baseCode, quoteId, quoteCode, pairingFormat, displayOrderMin, displayOrderMax, pairingRuleId);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? PairingRuleDetailConsts.GetDefaultSorting(true) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        protected virtual async Task<IQueryable<PairingRuleDetailWithNavigationProperties>> GetQueryForNavigationPropertiesAsync()
        {
            return from pairingRuleDetail in (await GetDbSetAsync())
                   join pairingRule in (await GetDbContextAsync()).Set<PairingRule>() on pairingRuleDetail.PairingRuleId equals pairingRule.Id into pairingRules
                   from pairingRule in pairingRules.DefaultIfEmpty()
                   select new PairingRuleDetailWithNavigationProperties
                   {
                       PairingRuleDetail = pairingRuleDetail,
                       PairingRule = pairingRule
                   };
        }

        protected virtual IQueryable<PairingRuleDetailWithNavigationProperties> ApplyFilter(
            IQueryable<PairingRuleDetailWithNavigationProperties> query,
            string filterText,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? pairingRuleId = null)
        {
            return query
                .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.PairingRuleDetail.BaseCode.Contains(filterText) || e.PairingRuleDetail.QuoteCode.Contains(filterText) || e.PairingRuleDetail.PairingFormat.Contains(filterText))
                    .WhereIf(baseId.HasValue, e => e.PairingRuleDetail.BaseId == baseId)
                    .WhereIf(!string.IsNullOrWhiteSpace(baseCode), e => e.PairingRuleDetail.BaseCode.Contains(baseCode))
                    .WhereIf(quoteId.HasValue, e => e.PairingRuleDetail.QuoteId == quoteId)
                    .WhereIf(!string.IsNullOrWhiteSpace(quoteCode), e => e.PairingRuleDetail.QuoteCode.Contains(quoteCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(pairingFormat), e => e.PairingRuleDetail.PairingFormat.Contains(pairingFormat))
                    .WhereIf(displayOrderMin.HasValue, e => e.PairingRuleDetail.DisplayOrder >= displayOrderMin.Value)
                    .WhereIf(displayOrderMax.HasValue, e => e.PairingRuleDetail.DisplayOrder <= displayOrderMax.Value)
                    .WhereIf(pairingRuleId != null && pairingRuleId != Guid.Empty, e => e.PairingRule != null && e.PairingRule.Id == pairingRuleId);
        }

        public async Task<List<PairingRuleDetail>> GetListAsync(
            string filterText = null,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, baseId, baseCode, quoteId, quoteCode, pairingFormat, displayOrderMin, displayOrderMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? PairingRuleDetailConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? pairingRuleId = null,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryForNavigationPropertiesAsync();
            query = ApplyFilter(query, filterText, baseId, baseCode, quoteId, quoteCode, pairingFormat, displayOrderMin, displayOrderMax, pairingRuleId);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<PairingRuleDetail> ApplyFilter(
            IQueryable<PairingRuleDetail> query,
            string filterText,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.BaseCode.Contains(filterText) || e.QuoteCode.Contains(filterText) || e.PairingFormat.Contains(filterText))
                    .WhereIf(baseId.HasValue, e => e.BaseId == baseId)
                    .WhereIf(!string.IsNullOrWhiteSpace(baseCode), e => e.BaseCode.Contains(baseCode))
                    .WhereIf(quoteId.HasValue, e => e.QuoteId == quoteId)
                    .WhereIf(!string.IsNullOrWhiteSpace(quoteCode), e => e.QuoteCode.Contains(quoteCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(pairingFormat), e => e.PairingFormat.Contains(pairingFormat))
                    .WhereIf(displayOrderMin.HasValue, e => e.DisplayOrder >= displayOrderMin.Value)
                    .WhereIf(displayOrderMax.HasValue, e => e.DisplayOrder <= displayOrderMax.Value);
        }
    }
}