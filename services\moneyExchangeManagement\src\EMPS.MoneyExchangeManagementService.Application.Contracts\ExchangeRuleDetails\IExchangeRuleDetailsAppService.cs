using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public interface IExchangeRuleDetailsAppService : IApplicationService
    {
        Task<PagedResultDto<ExchangeRuleDetailDto>> GetListAsync(GetExchangeRuleDetailsInput input);

        Task<ExchangeRuleDetailDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<ExchangeRuleDetailDto> CreateAsync(ExchangeRuleDetailCreateDto input);

        Task<ExchangeRuleDetailDto> UpdateAsync(Guid id, ExchangeRuleDetailUpdateDto input);

        Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRuleDetailExcelDownloadDto input);

        Task<DownloadTokenResultDto> GetDownloadTokenAsync();

        Task ApproveRuleAsync(Guid exchangeRuleMasterID);

        Task SetArchiveAsync(Guid exchangeRuleMasterID);

        Task SetUnArchiveAsync(Guid exchangeRuleMasterID);
        Task DeleteManyAsync(Guid exchangeRuleMasterID);
    }
}