using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.PairingRules;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using Volo.Abp.ObjectMapping;

namespace EMPS.MoneyExchangeManagementService.PairingRules
{

    [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Default)]
    public class PairingRulesAppService : MoneyExchangeManagementServiceAppService, IPairingRulesAppService
    {
        private readonly IDistributedCache<PairingRuleExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly IPairingRuleRepository _pairingRuleRepository;
        private readonly PairingRuleManager _pairingRuleManager;
        private readonly IPairingRuleDetailRepository _pairingRuleDetailRepository;
        private readonly IPairingRuleDetailsAppService _pairingRuleDetailsAppService;

        public PairingRulesAppService(IDistributedCache<PairingRuleExcelDownloadTokenCacheItem, string> excelDownloadTokenCache, IPairingRuleRepository pairingRuleRepository, PairingRuleManager pairingRuleManager, IPairingRuleDetailRepository pairingRuleDetailRepository, IPairingRuleDetailsAppService pairingRuleDetailsAppService)
        {
            _excelDownloadTokenCache=excelDownloadTokenCache;
            _pairingRuleRepository=pairingRuleRepository;
            _pairingRuleManager=pairingRuleManager;
            _pairingRuleDetailRepository=pairingRuleDetailRepository;
            _pairingRuleDetailsAppService = pairingRuleDetailsAppService;
        }

        public virtual async Task<PagedResultDto<PairingRuleDto>> GetListAsync(GetPairingRulesInput input)
        {
            var totalCount = await _pairingRuleRepository.GetCountAsync(input.FilterText, input.Name, input.EffectiveDateMin, input.EffectiveDateMax, input.Description, input.IsApproved, input.ApprovedBy, input.ApprovedByName, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.IsArchived);
            var items = await _pairingRuleRepository.GetListAsync(input.FilterText, input.Name, input.EffectiveDateMin, input.EffectiveDateMax, input.Description, input.IsApproved, input.ApprovedBy, input.ApprovedByName, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.IsArchived, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<PairingRuleDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<PairingRule>, List<PairingRuleDto>>(items)
            };
        }

        public virtual async Task<PairingRuleDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<PairingRule, PairingRuleDto>(await _pairingRuleRepository.GetAsync(id));
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {

            var RuleBeforeDelete = await _pairingRuleRepository.GetAsync(id);

            if (RuleBeforeDelete.IsApproved == true)
            {
                throw new UserFriendlyException(L["CannotDeleteApprovedPairingRule"]);
            }
            await DeleteAllDetailsByPairingRuleIdAsync(id);
            await _pairingRuleRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Create)]
        public virtual async Task<PairingRuleDto> CreateAsync(PairingRuleCreateDto input)
        {
            await CheckRuleName(input.Name);

            CheckApplyDate(input.EffectiveDate);

            var pairingRule = await _pairingRuleManager.CreateAsync(
            input.Name, input.Description, input.IsApproved, input.ApprovedByName, input.IsArchived, input.EffectiveDate, input.ApprovedBy, input.ApprovalDateTime
            );

            await _pairingRuleDetailsAppService.CreateAllActiveCurrenciesPairings(pairingRule.Id);

            return ObjectMapper.Map<PairingRule, PairingRuleDto>(pairingRule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Edit)]
        public virtual async Task<PairingRuleDto> UpdateAsync(Guid id, PairingRuleUpdateDto input)
        {

            var RuleBeforeUpdate = await _pairingRuleRepository.GetAsync(id);

            if (RuleBeforeUpdate.IsApproved == true)
            {
                throw new UserFriendlyException(L["CannotUpdateApprovedPairingRule"]);
            }
            if (RuleBeforeUpdate.Name != input.Name)
            {
                await CheckRuleName(input.Name);
            }
            if (RuleBeforeUpdate.EffectiveDate != input.EffectiveDate)
            {
                CheckApplyDate(input.EffectiveDate);
            }
            var pairingRule = await _pairingRuleManager.UpdateAsync(
            id,
            input.Name, input.Description, input.IsApproved, input.ApprovedByName, input.IsArchived, input.EffectiveDate, input.ApprovedBy, input.ApprovalDateTime, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<PairingRule, PairingRuleDto>(pairingRule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Edit)]
        public virtual async Task SetArchiveAsync(Guid id)
        {
            var Rule = await _pairingRuleRepository.GetAsync(id);
            if (Rule.IsApproved == false)
            {
                throw new UserFriendlyException(L["CannotArchiveUnApprovedPairingRule"]);
            }
            Rule.IsArchived = true;
            await _pairingRuleRepository.UpdateAsync(Rule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Edit)]
        public virtual async Task SetUnArchiveAsync(Guid id)
        {
            var Rule = await _pairingRuleRepository.GetAsync(id);
            if (Rule.IsApproved == false)
            {
                throw new UserFriendlyException(L["CannotUnArchiveUnApprovedPairingRule"]);
            }
            Rule.IsArchived = false;
            await _pairingRuleRepository.UpdateAsync(Rule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Approve)]
        public virtual async Task SetApproveAsync(Guid id)
        {
            var Rule = await _pairingRuleRepository.GetAsync(id);
            
            if(Rule.IsApproved)
            {
                throw new UserFriendlyException(L["AlreadyApproved"]);
            }
            
            
            
            
            Rule.IsApproved = true;
            Rule.ApprovedBy = CurrentUser.Id;
            Rule.ApprovedByName = CurrentUser.Name;
            Rule.ApprovalDateTime = DateTime.Now;
            await _pairingRuleRepository.UpdateAsync(Rule);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Create)]
        public async Task DuplicateRuleAsync(Guid id)
        {
            var Rule = await _pairingRuleRepository.GetAsync(id);

            await CheckRuleName(Rule.Name + "_Copy");

            var pairingRule = await _pairingRuleManager.CreateAsync(
            Rule.Name + "_Copy", Rule.Description, false, string.Empty , false, Rule.EffectiveDate, null , null);

            //TODO
            // call MHD method to duplicate details and pass id and pairingRule.id

            await DuplicatePairingRuleDetailsAsync(id, pairingRule.Id);

        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var items = await _pairingRuleRepository.GetListAsync(input.FilterText, input.Name, input.EffectiveDateMin, input.EffectiveDateMax, input.Description, input.IsApproved, input.ApprovedBy, input.ApprovedByName, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.IsArchived);

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(ObjectMapper.Map<List<PairingRule>, List<PairingRuleExcelDto>>(items));
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "PairingRules.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new PairingRuleExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }

        public async Task<PairingWithDetailes> GetLastEffectiveRule()
        {

            PairingWithDetailes pairingWithDetailes = new PairingWithDetailes();

            pairingWithDetailes.PairingRule = new PairingRuleDto();
            pairingWithDetailes.PairingRuleDetail = new List<PairingRuleDetailDto>();



            var lastEffectiveRule = await _pairingRuleRepository.GetListAsync(isApproved: true,
                sorting: "EffectiveDate DESC",
                maxResultCount: 1);

            if(lastEffectiveRule == null)
            {
                throw new UserFriendlyException(L["NoRuleIsActive"]);
            }

            pairingWithDetailes.PairingRule = ObjectMapper.Map<PairingRule, PairingRuleDto>(lastEffectiveRule.First());

            var ruleId = pairingWithDetailes.PairingRule.Id;

            var details = await _pairingRuleDetailRepository.GetListAsync(detail => detail.PairingRuleId == ruleId);

            pairingWithDetailes.PairingRuleDetail = ObjectMapper.Map<List<PairingRuleDetail>, List<PairingRuleDetailDto>>(details);

            return pairingWithDetailes;
        }

        private async Task CheckRuleName(string name)
        {
            if (await _pairingRuleRepository.AnyAsync(x => x.Name == name))
            {
                throw new UserFriendlyException(L["PairingRuleNameAlreadyExist"]);
            }
        }
        private void CheckApplyDate(DateTime? EffectiveDate)
        {
            if (EffectiveDate == default)
            {
                throw new UserFriendlyException(L["EffectiveDateIsRequired"]);
            }
            if (EffectiveDate < DateTime.Now)
            {
                throw new UserFriendlyException(L["EffectiveDateShouldbeLargerThanNow"]);
            }
        }

        public async Task DuplicatePairingRuleDetailsAsync(Guid duplicatedPairingRuleId, Guid newPairingRuleId)
        {
            // Get all existing details for the old pairing rule
            var oldDetails = await _pairingRuleDetailRepository
                .GetListAsync(d => d.PairingRuleId == duplicatedPairingRuleId);

            foreach (var oldDetail in oldDetails)
            {
                var newDetail = new PairingRuleDetailCreateDto
                {
                    PairingRuleId = newPairingRuleId,
                    BaseId = oldDetail.BaseId,
                    BaseCode = oldDetail.BaseCode,
                    QuoteId = oldDetail.QuoteId,
                    QuoteCode = oldDetail.QuoteCode,
                    PairingFormat = oldDetail.PairingFormat,
                    DisplayOrder = oldDetail.DisplayOrder
                };

                await _pairingRuleDetailsAppService.CreateAsync(newDetail);
            }
        }
        public async Task DeleteAllDetailsByPairingRuleIdAsync(Guid pairingRuleId)
        {
            var details = await _pairingRuleDetailRepository
                .GetListAsync(d => d.PairingRuleId == pairingRuleId);

            foreach (var detail in details)
            {
                await _pairingRuleDetailRepository.DeleteAsync(detail);
            }
        }

    }
}