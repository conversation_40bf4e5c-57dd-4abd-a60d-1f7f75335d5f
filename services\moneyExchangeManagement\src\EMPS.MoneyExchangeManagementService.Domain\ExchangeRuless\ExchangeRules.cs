using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRules : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string Name { get; set; }

        [CanBeNull]
        public virtual string? Description { get; set; }

        public virtual DateTime ActivationDate { get; set; }

        public virtual ExchangeRulesScope ExchangeRuleScope { get; set; }

        public virtual bool IsApproved { get; set; }

        [CanBeNull]
        public virtual Guid? ApprovedByUserId { get; set; }
        public virtual bool IsReprintReceiptAllowed { get; set; }
        [CanBeNull]
        public virtual string? ApprovedByUserName { get; set; }

        public virtual DateTime ApprovedDateTime { get; set; }

        public virtual bool IsArchived { get; set; }

        [CanBeNull]
        public virtual Guid? ArchivedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ArchivedByUserName { get; set; }

        public virtual DateTime ArchivedDateTime { get; set; }

        public virtual Guid? UnArchivedByUserId { get; set; }

        public virtual string? UnArchivedByUserName { get; set; }

        public virtual DateTime? UnArchivedByDate { get; set; }
        public virtual double ApprovalLimit { get; set; }

        public virtual double RoundUpFee { get; set; }

        public ExchangeRules()
        {

        }

        public ExchangeRules(Guid id, string name, string description, DateTime activationDate, 
            ExchangeRulesScope exchangeRuleScope, bool isReprintReceiptAllowed ,double approvalLimit, double roundUpFee)
        {

            Id = id;
            Check.NotNull(name, nameof(name));
            ApprovalLimit = approvalLimit;
            RoundUpFee = roundUpFee;
            Name = name;
            IsReprintReceiptAllowed = isReprintReceiptAllowed;
            Description = description;
            ActivationDate = activationDate;
            ExchangeRuleScope = exchangeRuleScope;
        }

        public void Approve(Guid userId, string userName)
        {
            IsApproved = true;
            ApprovedByUserId = userId;
            ApprovedByUserName = userName;
            ApprovedDateTime = DateTime.Now;
        }

        public void Archive(Guid userId, string userName)
        {
            IsArchived = true;
            ArchivedByUserId = userId;
            ArchivedByUserName = userName;
            ArchivedDateTime = DateTime.Now;
        }

        public void UnArchive(Guid userId, string userName)
        {
            IsArchived = false;
            UnArchivedByUserId = userId;
            UnArchivedByUserName = userName;
            UnArchivedByDate = DateTime.Now;
        }

    }
}