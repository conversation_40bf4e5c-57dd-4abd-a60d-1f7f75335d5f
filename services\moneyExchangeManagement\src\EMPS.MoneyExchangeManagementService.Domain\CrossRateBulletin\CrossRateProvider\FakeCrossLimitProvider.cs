using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// A fake implementation of ICrossLimitProvider that returns dummy data
    /// for testing and development purposes
    /// </summary>
    public class FakeCrossLimitProvider : ICrossLimitProvider
    {
        private readonly Random _random = new Random();

        /// <summary>
        /// Returns a collection of dummy cross rate data
        /// </summary>
        /// <param name="input">Request parameters (not used in this implementation)</param>
        /// <returns>Collection of dummy cross rate responses</returns>
        public Task<ICollection<CrossLimitProviderResponse>> GetCrossLimitsAsync(CrossLimitProviderRequest input)
        {
            // Create a list to hold our dummy responses
            var responses = new List<CrossLimitProviderResponse>();

            // Define some common currency pairs
            var currencyPairs = new List<(string Base, string Quote)>
            {
                ("USD", "EUR"),
                ("USD", "GBP"),
                ("USD", "JPY"),
                ("USD", "CHF"),
                ("USD", "CAD"),
                ("USD", "AUD"),
                ("EUR", "USD"),
                ("EUR", "GBP"),
                ("EUR", "JPY"),
                ("EUR", "CHF"),
                ("GBP", "USD"),
                ("GBP", "EUR"),
                ("GBP", "JPY"),
                ("JPY", "USD"),
                ("CHF", "USD"),
                ("CAD", "USD"),
                ("AUD", "USD"),
                ("SAR", "USD"),
                ("AED", "USD"),
                ("KWD", "USD")
            };

            // Generate a response for each currency pair
            foreach (var pair in currencyPairs)
            {
                // Generate a realistic but random cross rate value
                double crossRateValue = GenerateRealisticCrossRate(pair.Base, pair.Quote);

                responses.Add(new CrossLimitProviderResponse
                {
                    SourceCurrencyCode = pair.Base,
                    QuoteCurrencyCode = pair.Quote,
                    CrossRateValue = crossRateValue
                });
            }

            return Task.FromResult<ICollection<CrossLimitProviderResponse>>(responses);
        }

        /// <summary>
        /// Generates a realistic cross rate value for the given currency pair
        /// </summary>
        /// <param name="baseCurrency">Base currency code</param>
        /// <param name="quoteCurrency">Quote currency code</param>
        /// <returns>A realistic cross rate value</returns>
        private double GenerateRealisticCrossRate(string baseCurrency, string quoteCurrency)
        {
            // Base values for common currency pairs (approximate as of 2023)
            var baseRates = new Dictionary<string, Dictionary<string, double>>
            {
                ["USD"] = new Dictionary<string, double>
                {
                    ["EUR"] = 0.92,
                    ["GBP"] = 0.79,
                    ["JPY"] = 145.0,
                    ["CHF"] = 0.88,
                    ["CAD"] = 1.35,
                    ["AUD"] = 1.50,
                    ["SAR"] = 3.75,
                    ["AED"] = 3.67,
                    ["KWD"] = 0.31
                },
                ["EUR"] = new Dictionary<string, double>
                {
                    ["USD"] = 1.09,
                    ["GBP"] = 0.86,
                    ["JPY"] = 158.0,
                    ["CHF"] = 0.96
                },
                ["GBP"] = new Dictionary<string, double>
                {
                    ["USD"] = 1.27,
                    ["EUR"] = 1.16,
                    ["JPY"] = 183.0
                },
                ["JPY"] = new Dictionary<string, double>
                {
                    ["USD"] = 0.0069
                },
                ["CHF"] = new Dictionary<string, double>
                {
                    ["USD"] = 1.14
                },
                ["CAD"] = new Dictionary<string, double>
                {
                    ["USD"] = 0.74
                },
                ["AUD"] = new Dictionary<string, double>
                {
                    ["USD"] = 0.67
                }
            };

            // If we have a base rate for this pair, use it with some random variation
            if (baseRates.ContainsKey(baseCurrency) &&
                baseRates[baseCurrency].ContainsKey(quoteCurrency))
            {
                double baseRate = baseRates[baseCurrency][quoteCurrency];
                // Add up to ±2% random variation
                double variation = ((_random.NextDouble() * 4) - 2) / 100;
                return Math.Round(baseRate * (1 + variation), 4);
            }

            // For pairs we don't have base rates for, generate a random value
            // that's somewhat realistic
            if (quoteCurrency == "JPY")
            {
                // JPY rates are typically larger numbers
                return Math.Round(_random.NextDouble() * 100 + 50, 2);
            }
            else if (baseCurrency == "JPY")
            {
                // When JPY is the base, rates are typically very small
                return Math.Round(_random.NextDouble() * 0.01, 4);
            }
            else
            {
                // For other pairs, generate a value between 0.5 and 2.0
                return Math.Round(_random.NextDouble() * 1.5 + 0.5, 4);
            }
        }
    }
}
