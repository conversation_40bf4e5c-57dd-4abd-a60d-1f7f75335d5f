using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

[DependsOn(
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpEntityFrameworkCoreModule),
    typeof(MoneyExchangeManagementServiceDomainModule)
)]
public class MoneyExchangeManagementServiceEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        MoneyExchangeManagementServiceEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<MoneyExchangeManagementServiceDbContext>(options =>
        {
            /* Remove "includeAllEntities: true" to create
             * default repositories only for aggregate roots */


            options.AddDefaultRepositories(includeAllEntities: true);
            options.AddRepository<SpreadRule, MoneyExchangeManagementService.SpreadRules.EfCoreSpreadRuleRepository>();

            options.AddRepository<SpreadRuleDetail, MoneyExchangeManagementService.SpreadRuleDetails.EfCoreSpreadRuleDetailRepository>();

            options.AddRepository<BulletinManagementMaster, BulletinManagementMasters.EfCoreBulletinManagementMasterRepository>();

            options.AddRepository<BulletinManagementDetail, BulletinManagementDetails.EfCoreBulletinManagementDetailRepository>();

            options.AddRepository<PairingRule, PairingRules.EfCorePairingRuleRepository>();

            options.AddRepository<PairingRuleDetail, PairingRuleDetails.EfCorePairingRuleDetailRepository>();
            options.AddRepository<CrossRateBulletinMaster, EfCoreCrossRateBulletinMasterRepository>();

            options.AddRepository<ExchangeRules, ExchangeRuless.EfCoreExchangeRulesRepository>();

            options.AddRepository<ExchangeRuleDetail, ExchangeRuleDetails.EfCoreExchangeRuleDetailRepository>();

        });

        Configure<AbpDbContextOptions>(options =>
        {
            options.Configure<MoneyExchangeManagementServiceDbContext>(c =>
            {
                c.UseSqlServer(b =>
                {
                    b.MigrationsHistoryTable("__MoneyExchangeManagementService_Migrations");
                });
            });
        });
    }
}