using EMPS.MoneyExchangeManagementService.Shared;
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("moneyExchangeManagementService")]
    [ControllerName("PairingRuleDetail")]
    [Route("api/money-exchange-management-service/pairing-rule-details")]
    public class PairingRuleDetailController : AbpController, IPairingRuleDetailsAppService
    {
        private readonly IPairingRuleDetailsAppService _pairingRuleDetailsAppService;

        public PairingRuleDetailController(IPairingRuleDetailsAppService pairingRuleDetailsAppService)
        {
            _pairingRuleDetailsAppService = pairingRuleDetailsAppService;
        }

        [HttpGet]
        public Task<PagedResultDto<PairingRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetPairingRuleDetailsInput input)
        {
            return _pairingRuleDetailsAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("with-navigation-properties/{id}")]
        public Task<PairingRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return _pairingRuleDetailsAppService.GetWithNavigationPropertiesAsync(id);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<PairingRuleDetailDto> GetAsync(Guid id)
        {
            return _pairingRuleDetailsAppService.GetAsync(id);
        }

        [HttpGet]
        [Route("pairing-rule-lookup")]
        public Task<PagedResultDto<LookupDto<Guid>>> GetPairingRuleLookupAsync(LookupRequestDto input)
        {
            return _pairingRuleDetailsAppService.GetPairingRuleLookupAsync(input);
        }

        [HttpPost]
        public virtual Task<PairingRuleDetailDto> CreateAsync(PairingRuleDetailCreateDto input)
        {
            return _pairingRuleDetailsAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<PairingRuleDetailDto> UpdateAsync(Guid id, PairingRuleDetailUpdateDto input)
        {
            return _pairingRuleDetailsAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _pairingRuleDetailsAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(PairingRuleDetailExcelDownloadDto input)
        {
            return _pairingRuleDetailsAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _pairingRuleDetailsAppService.GetDownloadTokenAsync();
        }

        [HttpPost]
        [Route("CreateAllActiveCurrenciesPairings")]
        public Task CreateAllActiveCurrenciesPairings(Guid PairingRuleId)
        {
            return _pairingRuleDetailsAppService.CreateAllActiveCurrenciesPairings(PairingRuleId);
        }

        [HttpGet]
        [Route("MoveUp")]
        public Task MoveUp(Guid id, Guid pairingRuleId)
        {
            return _pairingRuleDetailsAppService.MoveUp(id, pairingRuleId);
        }

        [HttpGet]
        [Route("MoveDown")]
        public Task MoveDown(Guid id, Guid pairingRuleId)
        {
            return _pairingRuleDetailsAppService.MoveDown(id, pairingRuleId);
        }

        [HttpGet]
        [Route("SwitchPairings")]
        public Task SwitchPairings(Guid id, Guid pairingRuleId)
        {
            return _pairingRuleDetailsAppService.SwitchPairings(id, pairingRuleId);
        }
    }
}