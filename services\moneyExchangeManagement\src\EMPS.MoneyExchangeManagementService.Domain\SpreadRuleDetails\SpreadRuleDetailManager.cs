using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetailManager : DomainService
    {
        private readonly ISpreadRuleDetailRepository _spreadRuleDetailRepository;

        public SpreadRuleDetailManager(ISpreadRuleDetailRepository spreadRuleDetailRepository)
        {
            _spreadRuleDetailRepository = spreadRuleDetailRepository;
        }

        public async Task<SpreadRuleDetail> CreateAsync(
        Guid? spreadRuleId, Guid currencyId, string currencyCode, SpreadRuleType type, double bidSpread, double bidMaxDiscount, double bidMaxMarkdown, double askSpread, double askMaxDiscount, double askMaxMarkup)
        {
            Check.NotNull(type, nameof(type));

            var spreadRuleDetail = new SpreadRuleDetail(
             GuidGenerator.Create(),
             spreadRuleId, currencyId, currencyCode, type, bidSpread, bidMaxDiscount, bidMaxMarkdown, askSpread, askMaxDiscount, askMaxMarkup
             );

            return await _spreadRuleDetailRepository.InsertAsync(spreadRuleDetail);
        }

        public async Task<SpreadRuleDetail> UpdateAsync(
            Guid id,
            Guid? spreadRuleId, Guid currencyId, string currencyCode, SpreadRuleType type, double bidSpread, double bidMaxDiscount, double bidMaxMarkdown, double askSpread, double askMaxDiscount, double askMaxMarkup, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(type, nameof(type));

            var spreadRuleDetail = await _spreadRuleDetailRepository.GetAsync(id);

            spreadRuleDetail.SpreadRuleId = spreadRuleId;
            spreadRuleDetail.CurrencyId = currencyId;
            spreadRuleDetail.CurrencyCode = currencyCode;
            spreadRuleDetail.Type = type;
            spreadRuleDetail.BidSpread = bidSpread;
            spreadRuleDetail.BidMaxDiscount = bidMaxDiscount;
            spreadRuleDetail.BidMaxMarkdown = bidMaxMarkdown;
            spreadRuleDetail.AskSpread = askSpread;
            spreadRuleDetail.AskMaxDiscount = askMaxDiscount;
            spreadRuleDetail.AskMaxMarkup = askMaxMarkup;

            spreadRuleDetail.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _spreadRuleDetailRepository.UpdateAsync(spreadRuleDetail);
        }

    }
}