using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// Request parameters for cross limit providers
    /// </summary>
    public class CrossLimitProviderRequest
    {
        /// <summary>
        /// Access token for authentication with the provider
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Base URL for the provider API
        /// </summary>
        public string ProviderBaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// Base currency for the exchange rates (e.g., "USD")
        /// </summary>
        public string RequestBaseCurrency { get; set; } = string.Empty;

        /// <summary>
        /// List of currencies to fetch exchange rates for
        /// </summary>
        public List<string> RequestedCurrencies { get; set; } = new();
    }
}
