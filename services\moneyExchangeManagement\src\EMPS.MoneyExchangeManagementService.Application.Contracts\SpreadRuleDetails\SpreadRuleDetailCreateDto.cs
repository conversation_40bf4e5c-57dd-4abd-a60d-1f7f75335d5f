using EMPS.Shared.Enum;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetailCreateDto
    {
        public Guid CurrencyId { get; set; }
        public string? CurrencyCode { get; set; }
        public SpreadRuleType Type { get; set; } = ((SpreadRuleType[])Enum.GetValues(typeof(SpreadRuleType)))[0];
        public double BidSpread { get; set; } = 0;
        public double BidMaxDiscount { get; set; } = 0;
        public double BidMaxMarkdown { get; set; } = 0;
        public double AskSpread { get; set; } = 0;
        public double AskMaxDiscount { get; set; } = 0;
        public double AskMaxMarkup { get; set; } = 0;
        public Guid? SpreadRuleId { get; set; }
    }
}