using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailManager : DomainService
    {
        private readonly IExchangeRuleDetailRepository _exchangeRuleDetailRepository;

        public ExchangeRuleDetailManager(IExchangeRuleDetailRepository exchangeRuleDetailRepository)
        {
            _exchangeRuleDetailRepository = exchangeRuleDetailRepository;
        }

        public async Task<ExchangeRuleDetail> CreateAsync(
        string currencyName, Guid currencyID,bool allowedToBuy, double minAmountToBuy, double maxAmountToBuy, double maxDailyAmountToBuy, bool allowedToSell, double minAmountToSell, double maxAmountToSell, double maxDailyAmountToSell, bool allowedToSellBelowCenterCost, bool allowedToSellBelowCompanyCost, Guid exchangeRuleMasterID, ExchangeRuleDetailsType exchangeRuleDetailType)
        {
            Check.NotNull(exchangeRuleDetailType, nameof(exchangeRuleDetailType));

            var exchangeRuleDetail = new ExchangeRuleDetail(
             GuidGenerator.Create(),
             currencyName, currencyID,  allowedToBuy, minAmountToBuy, maxAmountToBuy, maxDailyAmountToBuy, allowedToSell, minAmountToSell, maxAmountToSell, maxDailyAmountToSell, allowedToSellBelowCenterCost, allowedToSellBelowCompanyCost, exchangeRuleMasterID, exchangeRuleDetailType
             );

            return await _exchangeRuleDetailRepository.InsertAsync(exchangeRuleDetail);
        }

        public async Task<ExchangeRuleDetail> UpdateAsync(
            Guid id,
            string currencyName, Guid currencyID,  bool allowedToBuy, double minAmountToBuy, double maxAmountToBuy, double maxDailyAmountToBuy, bool allowedToSell, double minAmountToSell, double maxAmountToSell, double maxDailyAmountToSell, bool allowedToSellBelowCenterCost, bool allowedToSellBelowCompanyCost, Guid exchangeRuleMasterID, ExchangeRuleDetailsType exchangeRuleDetailType, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(exchangeRuleDetailType, nameof(exchangeRuleDetailType));

            var exchangeRuleDetail = await _exchangeRuleDetailRepository.GetAsync(id);

            exchangeRuleDetail.CurrencyName = currencyName;
            exchangeRuleDetail.CurrencyID = currencyID;
  
            exchangeRuleDetail.AllowedToBuy = allowedToBuy;
            exchangeRuleDetail.MinAmountToBuy = minAmountToBuy;
            exchangeRuleDetail.MaxAmountToBuy = maxAmountToBuy;
            exchangeRuleDetail.MaxDailyAmountToBuy = maxDailyAmountToBuy;
            exchangeRuleDetail.AllowedToSell = allowedToSell;
            exchangeRuleDetail.MinAmountToSell = minAmountToSell;
            exchangeRuleDetail.MaxAmountToSell = maxAmountToSell;
            exchangeRuleDetail.MaxDailyAmountToSell = maxDailyAmountToSell;
            exchangeRuleDetail.AllowedToSellBelowCenterCost = allowedToSellBelowCenterCost;
            exchangeRuleDetail.AllowedToSellBelowCompanyCost = allowedToSellBelowCompanyCost;
            exchangeRuleDetail.ExchangeRuleMasterID = exchangeRuleMasterID;
            exchangeRuleDetail.ExchangeRuleDetailType = exchangeRuleDetailType;

            exchangeRuleDetail.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _exchangeRuleDetailRepository.UpdateAsync(exchangeRuleDetail);
        }

    }
}