using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.Shared.Enum.ExchangeRules;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{

    [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Default)]
    public class ExchangeRuleDetailsAppService : ApplicationService, IExchangeRuleDetailsAppService
    {
        private readonly IDistributedCache<ExchangeRuleDetailExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly IExchangeRuleDetailRepository _exchangeRuleDetailRepository;
        private readonly ExchangeRuleDetailManager _exchangeRuleDetailManager;

        public ExchangeRuleDetailsAppService(IExchangeRuleDetailRepository exchangeRuleDetailRepository, ExchangeRuleDetailManager exchangeRuleDetailManager, IDistributedCache<ExchangeRuleDetailExcelDownloadTokenCacheItem, string> excelDownloadTokenCache)
        {
            _excelDownloadTokenCache = excelDownloadTokenCache;
            _exchangeRuleDetailRepository = exchangeRuleDetailRepository;
            _exchangeRuleDetailManager = exchangeRuleDetailManager;
        }

        public virtual async Task<PagedResultDto<ExchangeRuleDetailDto>> GetListAsync(GetExchangeRuleDetailsInput input)
        {
            var totalCount = await _exchangeRuleDetailRepository.GetCountAsync(input.ExchangeRuleMasterID, input.FilterText, input.ExchangeRuleDetailType, input.CurrencyName, input.CurrencyID, input.AllowedToBuy, input.MinAmountToBuyMin, input.MinAmountToBuyMax, input.MaxAmountToBuyMin, input.MaxAmountToBuyMax, input.MaxDailyAmountToBuyMin, input.MaxDailyAmountToBuyMax, input.AllowedToSell, input.MinAmountToSellMin, input.MinAmountToSellMax, input.MaxAmountToSellMin, input.MaxAmountToSellMax, input.MaxDailyAmountToSellMin, input.MaxDailyAmountToSellMax, input.AllowedToSellBelowCenterCost, input.AllowedToSellBelowCompanyCost, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax);
            var items = await _exchangeRuleDetailRepository.GetListAsync(input.ExchangeRuleMasterID, input.FilterText, input.ExchangeRuleDetailType, input.CurrencyName, input.CurrencyID, input.AllowedToBuy, input.MinAmountToBuyMin, input.MinAmountToBuyMax, input.MaxAmountToBuyMin, input.MaxAmountToBuyMax, input.MaxDailyAmountToBuyMin, input.MaxDailyAmountToBuyMax, input.AllowedToSell, input.MinAmountToSellMin, input.MinAmountToSellMax, input.MaxAmountToSellMin, input.MaxAmountToSellMax, input.MaxDailyAmountToSellMin, input.MaxDailyAmountToSellMax, input.AllowedToSellBelowCenterCost, input.AllowedToSellBelowCompanyCost, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<ExchangeRuleDetailDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<ExchangeRuleDetail>, List<ExchangeRuleDetailDto>>(items)
            };
        }

        public virtual async Task<ExchangeRuleDetailDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<ExchangeRuleDetail, ExchangeRuleDetailDto>(await _exchangeRuleDetailRepository.GetAsync(id));
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _exchangeRuleDetailRepository.DeleteAsync(id);
        }
        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Delete)]
        public virtual async Task DeleteManyAsync(Guid ExchangeRuleMasterID)
        {
           var toDelete =  await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleMasterID:ExchangeRuleMasterID);
            await _exchangeRuleDetailRepository.DeleteManyAsync(toDelete);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Create)]
        public virtual async Task<ExchangeRuleDetailDto> CreateAsync(ExchangeRuleDetailCreateDto input)
        {

            var exchangeRuleDetail = await _exchangeRuleDetailManager.CreateAsync(
            input.CurrencyName, input.CurrencyID,input.AllowedToBuy, input.MinAmountToBuy, input.MaxAmountToBuy, input.MaxDailyAmountToBuy, input.AllowedToSell, input.MinAmountToSell, input.MaxAmountToSell, input.MaxDailyAmountToSell, input.AllowedToSellBelowCenterCost, input.AllowedToSellBelowCompanyCost, input.ExchangeRuleMasterID, input.ExchangeRuleDetailType
            );

            return ObjectMapper.Map<ExchangeRuleDetail, ExchangeRuleDetailDto>(exchangeRuleDetail);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit)]
        public virtual async Task<ExchangeRuleDetailDto> UpdateAsync(Guid id, ExchangeRuleDetailUpdateDto input)
        {
            if (input.MinAmountToBuy < 0)
                throw new UserFriendlyException(L["MinAmountToBuy should be greater than or equal to zero."]);

            if (input.MaxAmountToBuy < 0)
                throw new UserFriendlyException(L["MaxAmountToBuy should be greater than or equal to zero."]);

            if (input.MaxDailyAmountToBuy < 0)
                throw new UserFriendlyException(L["MaxDailyAmountToBuy should be greater than or equal to zero."]);

            if (input.MinAmountToSell < 0)
                throw new UserFriendlyException(L["MinAmountToSell should be greater than or equal to zero."]);

            if (input.MaxAmountToSell < 0)
                throw new UserFriendlyException(L["MaxAmountToSell should be greater than or equal to zero."]);

            if (input.MaxDailyAmountToSell < 0)
                throw new UserFriendlyException(L["MaxDailyAmountToSell should be greater than or equal to zero."]);

            var exchangeRuleDetail = await _exchangeRuleDetailManager.UpdateAsync(
            id,
            input.CurrencyName, input.CurrencyID,  input.AllowedToBuy, input.MinAmountToBuy, input.MaxAmountToBuy, input.MaxDailyAmountToBuy, input.AllowedToSell, input.MinAmountToSell, input.MaxAmountToSell, input.MaxDailyAmountToSell, input.AllowedToSellBelowCenterCost, input.AllowedToSellBelowCompanyCost, input.ExchangeRuleMasterID, input.ExchangeRuleDetailType, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<ExchangeRuleDetail, ExchangeRuleDetailDto>(exchangeRuleDetail);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(ExchangeRuleDetailExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var items = await _exchangeRuleDetailRepository.GetListAsync(input.ExchangeRuleMasterID, input.FilterText ,input.ExchangeRuleDetailType, input.CurrencyName, input.CurrencyID, input.AllowedToBuy, input.MinAmountToBuyMin, input.MinAmountToBuyMax, input.MaxAmountToBuyMin, input.MaxAmountToBuyMax, input.MaxDailyAmountToBuyMin, input.MaxDailyAmountToBuyMax, input.AllowedToSell, input.MinAmountToSellMin, input.MinAmountToSellMax, input.MaxAmountToSellMin, input.MaxAmountToSellMax, input.MaxDailyAmountToSellMin, input.MaxDailyAmountToSellMax, input.AllowedToSellBelowCenterCost, input.AllowedToSellBelowCompanyCost, input.IsApproved, input.ApprovedByUserId, input.ApprovedByUserName, input.ApprovedDateTimeMin, input.ApprovedDateTimeMax, input.IsArchived, input.ArchivedByUserId, input.ArchivedByUserName, input.ArchivedDateTimeMin, input.ArchivedDateTimeMax, input.UnArchivedByUserId, input.UnArchivedByUserName, input.UnArchivedByDateMin, input.UnArchivedByDateMax);

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(ObjectMapper.Map<List<ExchangeRuleDetail>, List<ExchangeRuleDetailExcelDto>>(items));
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "ExchangeRuleDetails.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new ExchangeRuleDetailExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit)]
        public virtual async Task ApproveRuleAsync(Guid exchangeRuleMasterID)
        {
            var allDetails = await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleMasterID);

            foreach (var detail in allDetails)
            {
                detail.Approve((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            }
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit)]
        public virtual async Task SetArchiveAsync(Guid exchangeRuleMasterID)
        {
            var allDetails = await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleMasterID);

            foreach (var detail in allDetails)
            {
                detail.Archive((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            }
        }

        [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit)]
        public virtual async Task SetUnArchiveAsync(Guid exchangeRuleMasterID)
        {
            var allDetails = await _exchangeRuleDetailRepository.GetListAsync(exchangeRuleMasterID);

            foreach (var detail in allDetails)
            {
                detail.UnArchive((Guid)CurrentUser.Id, $"{CurrentUser.Name} {CurrentUser.SurName}");
            }
        }
    }
}