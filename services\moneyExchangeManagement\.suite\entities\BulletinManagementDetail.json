{"Id": "f5fe68ea-62e8-4e6c-99cc-777428806eed", "Name": "BulletinManagementDetail", "OriginalName": "BulletinManagementDetail", "NamePlural": "BulletinManagementDetails", "DatabaseTableName": "BulletinManagementDetails", "Namespace": "BulletinManagementDetails", "BaseClass": "FullAuditedAggregateRoot", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "IsMultiTenant": false, "CheckConcurrency": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": false, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": false, "Properties": [{"Id": "b02e8418-2dbd-43c9-b6eb-dd1bcad66195", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "7e5aaac0-36a1-41cf-b2e7-2a814d1d8d28", "Name": "CashBid", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "6be4eac9-7cb9-4eaf-924d-f0e1873ff9b9", "Name": "CashAsk", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "b1ec56c5-73f5-44f8-b6b6-dfcbf4bdfaaf", "Name": "AccountBid", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "93ba0dc7-f9d7-4082-b78a-a5101a2400bf", "Name": "AccountAsk", "Type": "double", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "cb3a448e-1259-428b-9c44-9a683a7d12fc", "Name": "DisplayOrder", "Type": "int", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}], "NavigationProperties": [{"EntityNameWithDuplicationNumber": "BulletinManagementMaster", "EntitySetNameWithDuplicationNumber": "BulletinManagementMasters", "ReferencePropertyName": "BulletinManagementMaster", "UiPickType": "Dropdown", "IsRequired": true, "Name": "BulletinManagementMasterId", "DisplayProperty": "BulletinName", "Namespace": "EMPS.MoneyExchangeManagementService.BulletinManagementMasters", "EntityName": "BulletinManagementMaster", "EntitySetName": "BulletinManagementMasters", "DtoNamespace": "EMPS.MoneyExchangeManagementService.BulletinManagementMasters", "DtoEntityName": "BulletinManagementMasterDto", "Type": "Guid"}], "NavigationConnections": [], "PhysicalFileName": "BulletinManagementDetail.json"}