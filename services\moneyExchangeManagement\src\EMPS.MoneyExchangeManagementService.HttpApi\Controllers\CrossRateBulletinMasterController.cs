using System;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace EMPS.MoneyExchangeManagementService.Controllers.CrossRateBulletin
{
    [RemoteService]
    [Area("MoneyExchangeManagementService")]
    [ControllerName("CrossRateBulletinMaster")]
    [Route("api/money-exchange-management-service/cross-rate-bulletin-master")]
    public class CrossRateBulletinMasterController : AbpController, ICrossRateBulletinMasterAppService
    {
        private readonly ICrossRateBulletinMasterAppService _appService;

        public CrossRateBulletinMasterController(ICrossRateBulletinMasterAppService appService)
        {
            _appService = appService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<CrossRateBulletinMasterDto>> GetListAsync(GetCrossRateBulletinMasterListDto input)
        {
            return _appService.GetListAsync(input);
        }

        [HttpGet("{id}")]
        public virtual Task<CrossRateBulletinMasterDto> GetAsync(Guid id)
        {
            return _appService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<CrossRateBulletinMasterDto> CreateAsync(CreateUpdateCrossRateBulletinMasterDto input)
        {
            return _appService.CreateAsync(input);
        }

        [HttpPut("{id}")]
        public virtual Task<CrossRateBulletinMasterDto> UpdateAsync(Guid id, CreateUpdateCrossRateBulletinMasterDto input)
        {
            return _appService.UpdateAsync(id, input);
        }

        [HttpDelete("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _appService.DeleteAsync(id);
        }

        [HttpPost("{id}/approve")]
        public virtual Task ApproveAsync(Guid id)
        {
            return _appService.ApproveAsync(id);
        }

        [HttpPost("{id}/archive")]
        public virtual Task ArchiveAsync(Guid id)
        {
            return _appService.ArchiveAsync(id);
        }

        [HttpPost("{id}/unarchive")]
        public virtual Task UnarchiveAsync(Guid id)
        {
            return _appService.UnarchiveAsync(id);
        }


    }
}