using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class GetPairingRuleDetailsInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public Guid? BaseId { get; set; }
        public string? BaseCode { get; set; }
        public Guid? QuoteId { get; set; }
        public string? QuoteCode { get; set; }
        public string? PairingFormat { get; set; }
        public int? DisplayOrderMin { get; set; }
        public int? DisplayOrderMax { get; set; }
        public Guid? PairingRuleId { get; set; }

        public GetPairingRuleDetailsInput()
        {

        }
    }
}