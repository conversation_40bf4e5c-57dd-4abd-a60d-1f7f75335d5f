﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\..\..\common.props" />

    <PropertyGroup>
        <TargetFramework>net7.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>EMPS.MoneyExchangeManagementService</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Volo.Abp.Http.Client.IdentityModel" Version="7.3.2" />

      <ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Hosting.Microservices\EMPS.Shared.Hosting.Microservices.csproj" />
		<PackageReference Include="Volo.Abp.EventBus.RabbitMQ" Version="7.3.2" />

		<ProjectReference Include="..\..\..\..\shared\EMPS.Shared.Hosting.Microservices\EMPS.Shared.Hosting.Microservices.csproj" />
        <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.Application\EMPS.MoneyExchangeManagementService.Application.csproj" />
        <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.EntityFrameworkCore\EMPS.MoneyExchangeManagementService.EntityFrameworkCore.csproj" />
        <ProjectReference Include="..\EMPS.MoneyExchangeManagementService.HttpApi\EMPS.MoneyExchangeManagementService.HttpApi.csproj" />

      <PackageReference Include="Volo.Abp.Http.Client.IdentityModel" Version="7.3.2" />

    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Logs\**" />
        <Content Remove="Logs\**" />
        <EmbeddedResource Remove="Logs\**" />
        <None Remove="Logs\**" />
    </ItemGroup>

</Project>
