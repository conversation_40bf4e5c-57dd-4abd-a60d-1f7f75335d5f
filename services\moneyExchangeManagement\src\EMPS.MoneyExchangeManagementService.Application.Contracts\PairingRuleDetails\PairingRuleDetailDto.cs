using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class PairingRuleDetailDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public Guid BaseId { get; set; }
        public string BaseCode { get; set; }
        public Guid QuoteId { get; set; }
        public string QuoteCode { get; set; }
        public string? PairingFormat { get; set; }
        public int DisplayOrder { get; set; }
        public Guid PairingRuleId { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}