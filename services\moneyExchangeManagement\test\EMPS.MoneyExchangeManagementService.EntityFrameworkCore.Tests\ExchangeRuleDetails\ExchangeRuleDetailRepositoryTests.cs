using Shouldly;
using System;
using System.Linq;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Xunit;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
{
    public class ExchangeRuleDetailRepositoryTests : MoneyExchangeManagementServiceEntityFrameworkCoreTestBase
    {
        private readonly IExchangeRuleDetailRepository _exchangeRuleDetailRepository;

        public ExchangeRuleDetailRepositoryTests()
        {
            _exchangeRuleDetailRepository = GetRequiredService<IExchangeRuleDetailRepository>();
        }

        [Fact]
        public async Task GetListAsync()
        {
            // Arrange
            //await WithUnitOfWorkAsync(async () =>
            //{
            //    // Act
            //    var result = await _exchangeRuleDetailRepository.GetListAsync(
            //        currencyName: "5c6ff25a996243bdaa6c276d1090139e1632bb7947fd4a0e8f506101612c78214ddf5854dfc54e6a80fa2e674853a450",
            //        currencyID: "19d1fcbc3a42416ca9caa1ec882d832ef6301e1271c54b64b2ee949ade15",
            //        allowedToBuy: true,
            //        allowedToSell: true,
            //        maxDailyAmountToSell: "7e77cead3c85496fbe6",
            //        allowedToSellBelowCenterCost: true,
            //        allowedToSellBelowCompanyCost: true,
            //        exchangeRuleMasterID: "09a8b90d2c5f4e279abb76ac1c34d7978c4c0e1e5b4c",
            //        exchangeRuleDetailType: default
            //    );

            //    // Assert
            //    result.Count.ShouldBe(1);
            //    result.FirstOrDefault().ShouldNotBe(null);
            //    result.First().Id.ShouldBe(Guid.Parse("96fc4a72-3b4e-4541-a3ce-03f4bdd15bf3"));
            //});
        }

        [Fact]
        public async Task GetCountAsync()
        {
            // Arrange
            //await WithUnitOfWorkAsync(async () =>
            //{
            //    // Act
            //    var result = await _exchangeRuleDetailRepository.GetCountAsync(
            //        currencyName: "619b37334d7",
            //        currencyID: "b85be643a4054fefaf6c50f57bfde09192e2064b",
            //        allowedToBuy: true,
            //        allowedToSell: true,
            //        maxDailyAmountToSell: "6ed831562f734322a870f11cc240e5d3f0f89743a1d640acaebaa1defd14c3f0b92a45a154664a77997f2ba8288",
            //        allowedToSellBelowCenterCost: true,
            //        allowedToSellBelowCompanyCost: true,
            //        exchangeRuleMasterID: "41afd05f807344e3ac14a73c7465918f8d0a19108bd341cc9b",
            //        exchangeRuleDetailType: default
            //    );

            //    // Assert
            //    result.ShouldBe(1);
            //});
        }
    }
}