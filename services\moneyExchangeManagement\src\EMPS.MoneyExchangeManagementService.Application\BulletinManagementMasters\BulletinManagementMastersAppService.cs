using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using EMPS.CompanyService.ServicePoints;
using System.Linq;
using Volo.Abp.Identity;
using Volo.Abp;
using EMPS.MoneyExchangeManagementService.PairingRules;
using EMPS.MoneyExchangeManagementService.CrossRateBulletin;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using Bogus.Extensions.UnitedKingdom;
using EMPS.CompanyService.Currencies;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using Org.BouncyCastle.Asn1.Ocsp;
using Castle.Components.DictionaryAdapter.Xml;
using System.Collections;
using EMPS.Shared.Enum;
using System.Security.Cryptography;
using System.Threading.Channels;
using Volo.Abp.Uow;
using NUglify.JavaScript.Syntax;
using MediatR;
using Volo.Abp.ObjectMapping;
using System.Net;
using AutoMapper.Internal.Mappers;
using System.Data.Entity.Infrastructure;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{

    [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Default)]
    public class BulletinManagementMastersAppService : MoneyExchangeManagementServiceAppService, IBulletinManagementMastersAppService
    {

        private readonly IBulletinManagementMasterRepository _bulletinManagementMasterRepository;
        private readonly IBulletinManagementDetailRepository _bulletinManagementDetailRepository;

        private readonly BulletinManagementMasterManager _bulletinManagementMasterManager;
        private readonly IServicePointsAppService _servicePointsAppService;
        private readonly IIdentityUserAppService _IdentityUserAppService;
        private readonly IPairingRulesAppService _pairingRulesAppService;
        private readonly ICrossRateBulletinMasterRepository _crossRateBulletinMasterRepository;
        private readonly ICurrenciesAppService _currenciesAppService;
        private readonly ISpreadRuleRepository _spreadRuleRepository;

        public BulletinManagementMastersAppService(IBulletinManagementMasterRepository bulletinManagementMasterRepository, IBulletinManagementDetailRepository bulletinManagementDetailRepository, BulletinManagementMasterManager bulletinManagementMasterManager, IServicePointsAppService servicePointsAppService, IIdentityUserAppService identityUserAppService, IPairingRulesAppService pairingRulesAppService, ICrossRateBulletinMasterRepository crossRateBulletinMasterRepository, ICurrenciesAppService currenciesAppService, ISpreadRuleRepository spreadRuleRepository)
        {
            _bulletinManagementMasterRepository = bulletinManagementMasterRepository;
            _bulletinManagementDetailRepository = bulletinManagementDetailRepository;
            _bulletinManagementMasterManager = bulletinManagementMasterManager;
            _servicePointsAppService = servicePointsAppService;
            _IdentityUserAppService = identityUserAppService;
            _pairingRulesAppService = pairingRulesAppService;
            _crossRateBulletinMasterRepository = crossRateBulletinMasterRepository;
            _currenciesAppService = currenciesAppService;
            _spreadRuleRepository = spreadRuleRepository;
        }

        public virtual async Task<PagedResultDto<BulletinManagementMasterDto>> GetListAsync(GetBulletinManagementMastersInput input)
        {
            var totalCount = await _bulletinManagementMasterRepository.GetCountAsync(input.FilterText, input.BulletinNumber, input.BulletinName, input.BulletinDateMin, input.BulletinDateMax, input.Notes, input.ServicePointName, input.ServicePointId, input.CurrencyPairingRuleName, input.CurrencyPairingRuleId, input.CrossRateBulletinName, input.CrossRateBulletinId, input.SpreadRuleName, input.SpreadRuleId, input.PublishByUserName, input.PublishByUserId, input.PublishDateMin, input.PublishDateMax);
            var items = await _bulletinManagementMasterRepository.GetListAsync(input.FilterText, input.BulletinNumber, input.BulletinName, input.BulletinDateMin, input.BulletinDateMax, input.Notes, input.ServicePointName, input.ServicePointId, input.CurrencyPairingRuleName, input.CurrencyPairingRuleId, input.CrossRateBulletinName, input.CrossRateBulletinId, input.SpreadRuleName, input.SpreadRuleId, input.PublishByUserName, input.PublishByUserId, input.PublishDateMin, input.PublishDateMax, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<BulletinManagementMasterDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<BulletinManagementMaster>, List<BulletinManagementMasterDto>>(items.OrderByDescending(x => x.BulletinNumber).ToList())

            };
        }

        public virtual async Task<BulletinManagementMasterDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<BulletinManagementMaster, BulletinManagementMasterDto>(await _bulletinManagementMasterRepository.GetAsync(id));
        }



        public virtual async Task<BulletinManagementMasterDto> CreateAsync(BulletinManagementMasterCreateDto input)
        {

            var bulletinManagementMaster = await _bulletinManagementMasterManager.CreateAsync(
            input.BulletinNumber, input.BulletinName, input.BulletinDate, input.Notes, input.ServicePointName, input.CurrencyPairingRuleName, input.CrossRateBulletinName, input.SpreadRuleName, input.PublishByUserName, input.ServicePointId, input.CurrencyPairingRuleId, input.CrossRateBulletinId, input.SpreadRuleId, input.PublishByUserId, input.PublishDate
            );

            return ObjectMapper.Map<BulletinManagementMaster, BulletinManagementMasterDto>(bulletinManagementMaster);
        }

        /// <summary>
        /// Generates a preview bulletin of currency exchange rates with bid/ask prices.
        /// </summary>
        /// <param name="request">Request DTO containing parameters for bulletin generation.</param>
        /// <returns>A list of <see cref="BulletinManagementDetailDto"/> containing currency pairs and rates.</returns>
        [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Preview)]
        public async Task<List<BulletinManagementDetailDto>> PreviewBulletin(RequestPreviewBulletinDto request)
        {
            await CheckIfTheUserIsActive();
            Guid? spreadRuleId = null;
            if (request.ServicePointId.HasValue)
            {
                request.IsGlobal = false;
                var servicePoint = await _servicePointsAppService.GetAsync(request.ServicePointId!.Value);
                if (servicePoint.SpreadRuleId.HasValue)
                {
                    spreadRuleId = servicePoint.SpreadRuleId.Value;
                }
                else
                {
                    spreadRuleId = null;
                }
            }

            var pairingRuleTask = await GetParingRuleAsync();       // Gets currency pair formatting rules (e.g., "USD/EUR")
            var crossRatesTask = await GetCrossRateAsync();         // Gets exchange rates (e.g., USD->EUR rate)
            var spreadRuleTask = await GetSpreadRule(request.IsGlobal, spreadRuleId.HasValue ? new List<Guid> { spreadRuleId.Value } : null);      // Gets bid/ask spread rules (e.g., +/- 0.5%)
            var localCurrencyTask = await GetLocalCurrency();       // Gets the system's base currency (e.g., "SYP")
            CheckParingRuleCurrencyExistInSpreadRuleAndCrossRateRuleCurrency(crossRatesTask, pairingRuleTask, spreadRuleTask, localCurrencyTask);

            var rateMap = crossRatesTask
                .CrossRateBulletinDetails
                .ToDictionary(x => x.QuoteCurrencyCode, StringComparer.OrdinalIgnoreCase);


            var detail = pairingRuleTask.PairingRuleDetail
                .Select(detail => CreateDto(detail, rateMap, spreadRuleTask.First().SpreadRuleDetails, localCurrencyTask.Code, Guid.Empty))
                .OrderBy(d => d.DisplayOrder)
                .ToList();
            return ObjectMapper.Map<List<BulletinManagementDetail>, List<BulletinManagementDetailDto>>(detail);

        }

        private void CheckParingRuleCurrencyExistInSpreadRuleAndCrossRateRuleCurrency(CrossRateBulletinMaster rateBulletinMaster, PairingWithDetailes pairingRuleTask, List<SpreadRule> spreadRuleTask, CurrencyDto localCurrencyTask)
        {
            var list = pairingRuleTask.PairingRuleDetail
                .SelectMany(x => new[] { x.BaseCode, x.QuoteCode })
                .Distinct().Where(c => c != localCurrencyTask.Code);

            var list2 = spreadRuleTask.First().SpreadRuleDetails.Select(x => x.CurrencyCode);
            var list3 = rateBulletinMaster.CrossRateBulletinDetails.SelectMany(x => new[] { x.BaseCurrencyCode, x.QuoteCurrencyCode })
                .Distinct().Where(c => c != localCurrencyTask.Code);
            bool allCurrenciesExist = list.All(currency => list2.Contains(currency));
            bool allCurrenciesExistInCross = list.All(currency => list3.Contains(currency));

            if (!allCurrenciesExist) throw new UserFriendlyException(L["OneOrMoreCurrencyNotExistInSpreadRule"]);
            if (!allCurrenciesExistInCross) throw new UserFriendlyException(L["OneOrMoreCurrencyNotExistInCrossRate"]);

        }

        /// <summary>
        /// Creates a DTO for a currency pair with calculated bid/ask rates.
        /// </summary>
        /// <param name="detail">Pairing rule details (e.g., "USD/EUR", display order).</param>
        /// <param name="rateMap">Dictionary of cross rates for fast lookup.</param>
        /// <param name="spreads">Bid/ask spread rules to apply.</param>
        /// <param name="localCode">Local currency code (e.g., "SYP").</param>
        /// <returns>A configured <see cref="BulletinManagementDetailDto"/>.</returns>
        private BulletinManagementDetail CreateDto(
            PairingRuleDetailDto detail,
            Dictionary<string, CrossRateBulletinDetail> rateMap,
            List<SpreadRuleDetail> spreads,
            string localCode, Guid MasterId)
        {
            // Split currency pair (e.g., "USD/SYP" => ["USD", "SYP"])
            var parts = detail.PairingFormat.Split('/');
            var baseCcy = parts[0];    // Base currency (left side, e.g., "USD")
            var quoteCcy = parts[1];   // Quote currency (right side, e.g., "SYP")

            double cashBid, cashAsk;

            // Determine rate calculation method based on whether the quote currency is local
            var finalValues = new BullitinDto();
            if (quoteCcy.Equals(localCode, StringComparison.OrdinalIgnoreCase))
                // Case 1: Quote is local (e.g., "USD/SYP" => calculate USD->SYP rate)
                finalValues = CalculateQuoteIsLocal(baseCcy, rateMap, spreads, localCode);
            else
                // Case 2: Base is local (e.g., "SYP/USD" => calculate SYP->USD rate)
                finalValues = CalculateBaseIsLocal(quoteCcy, rateMap, spreads, localCode);

            // Return the DTO with formatted currency pair, display order, and calculated rates
            return new BulletinManagementDetail
            {
                CurrencyPair = detail.PairingFormat,  // e.g., "USD/SYP"
                DisplayOrder = detail.DisplayOrder,    // Sorting priority
                CashBid = Round(finalValues.CashBid),                    // Calculated bid price
                CashAsk = Round(finalValues.CashAsk),               // Calculated ask price
                AccountAsk = Round(finalValues.AccountAsk),
                AccountBid = Round(finalValues.AccountBid),
                BulletinManagementMasterId = MasterId,
                CurrencyBaseCode = detail.BaseCode,
                CurrencyBaseId = detail.BaseId,
                CurrencyQuoteCode = detail.QuoteCode,
                CurrencyQuoteId = detail.QuoteId,

            };
        }
        /// <summary>
        /// Handles the case: Pairing = Base/Local
        /// e.g. USD/SYP or EUR/SYP
        /// </summary>
        /// <summary>
        /// Handles the case: Pairing = Base/Local
        /// e.g. USD/SYP or EUR/SYP
        /// </summary>
        private BullitinDto CalculateQuoteIsLocal(
            string baseCcy,
            Dictionary<string, CrossRateBulletinDetail> rateMap,
            List<SpreadRuleDetail> spreads,
            string localCode)
        {
            var spreadsForCash = spreads.First(x => x.Type == SpreadRuleType.OnCash && x.CurrencyCode == baseCcy);
            var spreadsForAccount = spreads.First(x => x.Type == SpreadRuleType.OnAccount && x.CurrencyCode == baseCcy);
            if (!rateMap.TryGetValue(localCode, out var usdToLocal))
                throw new InvalidOperationException($"Missing cross rate for USD/{localCode}");

            if (baseCcy.Equals("USD", StringComparison.OrdinalIgnoreCase))
            {
                double mid = usdToLocal.CrossRateValue;

                return new BullitinDto
                {
                    CashAsk = mid + (double)spreadsForCash.AskSpread,
                    CashBid = mid - (double)spreadsForCash.BidSpread,
                    AccountAsk = mid + (double)spreadsForAccount.AskSpread,
                    AccountBid = mid - (double)spreadsForAccount.BidSpread
                };

            }
            if (!rateMap.TryGetValue(baseCcy, out var baseToUsd) && baseCcy != "USD")
                throw new InvalidOperationException($"Missing cross rate for {baseCcy}/USD");

            double chained = usdToLocal.CrossRateValue / baseToUsd.CrossRateValue;
            return new BullitinDto
            {
                CashAsk = chained + (double)spreadsForCash.AskSpread,
                CashBid = chained - (double)spreadsForCash.BidSpread,
                AccountAsk = chained + (double)spreadsForAccount.AskSpread,
                AccountBid = chained - (double)spreadsForAccount.BidSpread
            };

        }

        /// <summary>
        /// Handles the case: Pairing = Local/Quote
        /// e.g. SYP/USD or SYP/EUR
        /// </summary>
        private BullitinDto CalculateBaseIsLocal(
                    string quoteCcy,
                    Dictionary<string, CrossRateBulletinDetail> rateMap,
                     List<SpreadRuleDetail> spreads,
                    string localCode)
        {
            var spreadsForCash = spreads.First(x => x.Type == SpreadRuleType.OnCash && x.CurrencyCode == quoteCcy);
            var spreadsForAccount = spreads.First(x => x.Type == SpreadRuleType.OnAccount && x.CurrencyCode == quoteCcy);
            // 1) Lookup USD ? Local
            if (!rateMap.TryGetValue(localCode, out var usdToLocal))
                throw new InvalidOperationException($"Missing cross rate for USD/{localCode}");

            if (quoteCcy.Equals("USD", StringComparison.OrdinalIgnoreCase))
            {
                double Rate = 1 / usdToLocal.CrossRateValue;
                return new BullitinDto
                {
                    CashAsk = Rate + ((double)spreadsForCash.AskSpread * Rate),
                    CashBid = Rate - ((double)spreadsForCash.BidSpread * Rate),
                    AccountAsk = Rate + ((double)spreadsForAccount.AskSpread * Rate),
                    AccountBid = Rate - ((double)spreadsForAccount.BidSpread * Rate)
                };
            }

            // 2) Lookup Quote ? USD
            if (!rateMap.TryGetValue(quoteCcy, out var quoteToUsd))
                throw new InvalidOperationException($"Missing cross rate for {quoteCcy}/USD");



            double finalRate = quoteToUsd.CrossRateValue / usdToLocal.CrossRateValue;
            return new BullitinDto
            {
                CashAsk = finalRate + ((double)spreadsForCash.AskSpread * finalRate),
                CashBid = finalRate - ((double)spreadsForCash.BidSpread * finalRate),
                AccountAsk = finalRate + ((double)spreadsForAccount.AskSpread * finalRate),
                AccountBid = finalRate - ((double)spreadsForAccount.BidSpread * finalRate)
            };

        }

        private async Task<List<SpreadRule>> GetSpreadRule(bool isGlobal, List<Guid>? SpreadRuleIds)
        {
            var rules = await _spreadRuleRepository.GetSpreadRulesByGlobalAndIdsAsync(isGlobal, SpreadRuleIds);

            if (rules == null) throw new UserFriendlyException(L["ERROR:SpreadRuleIsNull"]);

            if (!rules.Any(x => x.Scope == SpreadRuleScope.Global) && isGlobal) throw new UserFriendlyException(L["ERROR:NoGlobalSpreadRule"]);
            return rules;
        }

        private async Task<CurrencyDto> GetLocalCurrency()
        {
            return await _currenciesAppService.GetLocalCurrency();
        }

        private async Task<CrossRateBulletinMaster> GetCrossRateAsync() => await _crossRateBulletinMasterRepository.GetLastCrossApprovedAsync() ?? throw new UserFriendlyException(L["ERROR:CroosRatingIsNull"]);

        private async Task<PairingWithDetailes> GetParingRuleAsync()
        {
            var paringRule = await _pairingRulesAppService.GetLastEffectiveRule();
            if (paringRule == null)
            {
                throw new UserFriendlyException(L["ERROR:ParingRuleIsNull"]);
            }
            return paringRule;
        }

        [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Publish)]
        public async Task Publish(bool IsGlobal, List<Guid> ServicePointIds)
        {
            if (IsGlobal == false && ServicePointIds.Count == 0)
                return;

            await CheckIfTheUserIsActive();

            var pairingRuleTask = await GetParingRuleAsync();       // Gets currency pair formatting rules (e.g., "USD/EUR")
            var crossRatesTask = await GetCrossRateAsync();         // Gets exchange rates (e.g., USD->EUR rate)
            var localCurrencyTask = await GetLocalCurrency();       // Gets the system's base currency (e.g., "SYP")
            var servicePoints = await _servicePointsAppService.GetAllActiveServicePointsLookupAsync();
            servicePoints = servicePoints.Where(x => ServicePointIds.Contains(x.Id)).ToList();
            var spreadRuleIds = servicePoints.Where(x => x.SpreadRuleId.HasValue).Select(x => x.SpreadRuleId!.Value).ToList();
            var listOfSpreadRules = await GetSpreadRule(IsGlobal, spreadRuleIds);      // Gets bid/ask spread rules (e.g., +/- 0.5%)

            var rateMap = crossRatesTask
               .CrossRateBulletinDetails
               .ToDictionary(x => x.QuoteCurrencyCode, StringComparer.OrdinalIgnoreCase);
            var lastNoForMaster = await _bulletinManagementMasterRepository.GenerateNewNo();
            if (IsGlobal)
            {
                var master = new BulletinManagementMaster();
                var spreadRule = listOfSpreadRules.Where(x => x.Scope == SpreadRuleScope.Global).First();
                master.FillTheBaseInfo(lastNoForMaster.ToString(), GenerateMasterName(lastNoForMaster), "");
                master.FillCrossRateInfo(crossRatesTask.Name, crossRatesTask.Id);
                master.FillParingRuleInfo(pairingRuleTask.PairingRule.Name, pairingRuleTask.PairingRule.Id);
                master.FillSpreadRuleInfo(spreadRule.RuleName, spreadRule.Id);
                master.FillPublishUserInfo(GetFullUserName(), GeTUserId());
                master.IsGlobal = true;

                master = await _bulletinManagementMasterManager.CreateMasterAsync(master);

                var detail = pairingRuleTask.PairingRuleDetail
               .Select(detail => CreateDto(detail, rateMap, spreadRule.SpreadRuleDetails, localCurrencyTask.Code, master.Id))
               .OrderBy(d => d.DisplayOrder)
               .ToList();
                await _bulletinManagementDetailRepository.InsertManyAsync(detail);
                lastNoForMaster += 1;

            }

            foreach (var servicePoint in servicePoints)
            {
                var master = new BulletinManagementMaster();
                var spreadRule = listOfSpreadRules.First(x => x.Id == servicePoint.SpreadRuleId);
                master.FillTheBaseInfo(lastNoForMaster.ToString(), GenerateMasterName(lastNoForMaster, servicePoint.Name), "");
                master.FillServicePointInfo(servicePoint.Name, servicePoint.Id);

                master.FillCrossRateInfo(crossRatesTask.Name, crossRatesTask.Id);
                master.FillParingRuleInfo(pairingRuleTask.PairingRule.Name, pairingRuleTask.PairingRule.Id);
                master.FillSpreadRuleInfo(spreadRule.RuleName, spreadRule.Id);
                master.FillPublishUserInfo(GetFullUserName(), GeTUserId());
                master.IsGlobal = false;

                master = await _bulletinManagementMasterManager.CreateMasterAsync(master);

                var detail = pairingRuleTask.PairingRuleDetail
               .Select(detail => CreateDto(detail, rateMap, listOfSpreadRules.First(x => x.Id == spreadRule.Id).SpreadRuleDetails, localCurrencyTask.Code, master.Id))
               .OrderBy(d => d.DisplayOrder)
               .ToList();
                await _bulletinManagementDetailRepository.InsertManyAsync(detail);
                lastNoForMaster += 1;

            }

        }

        public async Task<List<ServicePointDto>> GetAllServicePointHaveSpreadRuleId()
        {

            var query = await _servicePointsAppService.GetAllActiveServicePointsLookupAsync();
            return query.Where(x => x.SpreadRuleId.HasValue).ToList();
        }
        public string GenerateMasterName(int no, string? servicePointName = null)
        {
            if (servicePointName.IsNullOrWhiteSpace())
                return $"{no.ToString()} - {DateTime.Now.Date.ToShortDateString()}";
            return $"{no.ToString()} - {DateTime.Now.Date.ToShortDateString()} - {servicePointName}";

        }

        /// <summary>
        /// this method to check if the user is active or not,
        /// throw UserFriendlyException if the user is not active
        /// </summary>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task CheckIfTheUserIsActive()
        {
            // Asynchronously fetch the user information using the _identityUserAppService
            // by passing the current user's ID, which is cast to a Guid.
            if (CurrentUser.Id == null) throw new UserFriendlyException(L["thisIsNotUser"]);
            var user = await _IdentityUserAppService.GetAsync((Guid)CurrentUser.Id);

            // Check if the user's IsActive property is false.
            if (user.IsActive == false)
            {
                // If the user is not active, throw an exception with the message "thisUserIsNotActive".
                throw new UserFriendlyException(L["thisUserIsNotActive"]);
            }
        }
        double Round(double value)
        {
            return Math.Round(value, 2);
        }

        public async Task<List<BulletinManagementDetailDto>> GetListOfDetailsByMasterId(Guid masterId)
        {
            var query = (await _bulletinManagementDetailRepository.GetQueryableAsync()).Where(x => x.BulletinManagementMasterId == masterId).ToList();
            return ObjectMapper.Map<List<BulletinManagementDetail>, List<BulletinManagementDetailDto>>(query);
        }

        class BullitinDto
        {
            public double AccountAsk { get; set; }
            public double AccountBid { get; set; }
            public double CashAsk { get; set; }
            public double CashBid { get; set; }

        }
        protected string GetFullUserName()
        {
            return $"{CurrentUser.Name} {CurrentUser.SurName}";
        }
        protected Guid? GeTUserId()
        {
            return CurrentUser.Id;
        }

        public async Task<BulletinManagementMasterDto?> GetBulletinManagementWithNavigation(Guid? servicePointId = null)
        {
            BulletinManagementMaster? bulletin = new();
            if (servicePointId.HasValue)
            {
                bulletin = await _bulletinManagementMasterRepository.GetLastBulletinManagementMasterWithNavForServicePoint(servicePointId.Value);
            }
            else
            {
                bulletin = await _bulletinManagementMasterRepository.GetLastGlobalBulletinManagementMasterWithNav();
            }
            if (bulletin == null) return null;

            return ObjectMapper.Map<BulletinManagementMaster, BulletinManagementMasterDto>(bulletin);
        }
    }
}