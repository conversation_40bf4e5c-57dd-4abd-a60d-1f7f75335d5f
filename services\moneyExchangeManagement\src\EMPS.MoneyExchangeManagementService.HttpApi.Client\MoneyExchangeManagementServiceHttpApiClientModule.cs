﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Http.Client;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;

namespace EMPS.MoneyExchangeManagementService;

[DependsOn(
    typeof(MoneyExchangeManagementServiceApplicationContractsModule),
    typeof(AbpHttpClientModule))]
public class MoneyExchangeManagementServiceHttpApiClientModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddHttpClientProxies(typeof(MoneyExchangeManagementServiceApplicationContractsModule).Assembly,
            MoneyExchangeManagementServiceRemoteServiceConsts.RemoteServiceName);

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<MoneyExchangeManagementServiceHttpApiClientModule>();
        });
    }
}
