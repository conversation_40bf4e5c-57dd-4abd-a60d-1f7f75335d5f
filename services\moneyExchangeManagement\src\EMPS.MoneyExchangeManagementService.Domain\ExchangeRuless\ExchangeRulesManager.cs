using EMPS.Shared.Enum.ExchangeRules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulesManager : DomainService
    {
        private readonly IExchangeRulesRepository _exchangeRulesRepository;

        public ExchangeRulesManager(IExchangeRulesRepository exchangeRulesRepository)
        {
            _exchangeRulesRepository = exchangeRulesRepository;
        }

        public async Task<ExchangeRules> CreateAsync(
        string name, string description, DateTime activationDate, ExchangeRulesScope exchangeRuleScope,bool isReprintReceiptAllowed,double approvalLimit, double roundUpFee )
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));
            Check.NotNull(activationDate, nameof(activationDate));
            Check.NotNull(exchangeRuleScope, nameof(exchangeRuleScope));

            var exchangeRules = new ExchangeRules(
             GuidGenerator.Create(),
             name, description, activationDate, exchangeRuleScope,isReprintReceiptAllowed, approvalLimit, roundUpFee
             );

            return await _exchangeRulesRepository.InsertAsync(exchangeRules);
        }

        public async Task<ExchangeRules> UpdateAsync(
            Guid id,
            string name, string description, DateTime activationDate, ExchangeRulesScope exchangeRuleScope,bool isReprintReceiptAllowed, double approvalLimit, double roundUpFee, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNullOrWhiteSpace(name, nameof(name));
            Check.NotNull(activationDate, nameof(activationDate));
            Check.NotNull(exchangeRuleScope, nameof(exchangeRuleScope));

            var exchangeRules = await _exchangeRulesRepository.GetAsync(id);

            exchangeRules.Name = name;
            exchangeRules.IsReprintReceiptAllowed = isReprintReceiptAllowed;
            exchangeRules.ApprovalLimit = approvalLimit;
            exchangeRules.RoundUpFee = roundUpFee;
            exchangeRules.Description = description;
            exchangeRules.ActivationDate = activationDate;
            exchangeRules.ExchangeRuleScope = exchangeRuleScope;

            exchangeRules.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _exchangeRulesRepository.UpdateAsync(exchangeRules);
        }

    }
}