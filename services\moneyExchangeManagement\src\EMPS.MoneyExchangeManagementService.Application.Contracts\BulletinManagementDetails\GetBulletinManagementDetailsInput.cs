using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class GetBulletinManagementDetailsInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? CurrencyPair { get; set; }
        public double? CashBidMin { get; set; }
        public double? CashBidMax { get; set; }
        public double? CashAskMin { get; set; }
        public double? CashAskMax { get; set; }
        public double? AccountBidMin { get; set; }
        public double? AccountBidMax { get; set; }
        public double? AccountAskMin { get; set; }
        public double? AccountAskMax { get; set; }
        public int? DisplayOrderMin { get; set; }
        public int? DisplayOrderMax { get; set; }
        public Guid? BulletinManagementMasterId { get; set; }

        public GetBulletinManagementDetailsInput()
        {

        }
    }
}