using EMPS.Shared.Enum;
using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class GetSpreadRuleDetailsInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public Guid? CurrencyId { get; set; }
        public string? CurrencyCode { get; set; }
        public SpreadRuleType? Type { get; set; }
        public double? BidSpreadMin { get; set; }
        public double? BidSpreadMax { get; set; }
        public double? BidMaxDiscountMin { get; set; }
        public double? BidMaxDiscountMax { get; set; }
        public double? BidMaxMarkdownMin { get; set; }
        public double? BidMaxMarkdownMax { get; set; }
        public double? AskSpreadMin { get; set; }
        public double? AskSpreadMax { get; set; }
        public double? AskMaxDiscountMin { get; set; }
        public double? AskMaxDiscountMax { get; set; }
        public double? AskMaxMarkupMin { get; set; }
        public double? AskMaxMarkupMax { get; set; }
        public Guid? SpreadRuleId { get; set; }

        public GetSpreadRuleDetailsInput()
        {

        }
    }
}