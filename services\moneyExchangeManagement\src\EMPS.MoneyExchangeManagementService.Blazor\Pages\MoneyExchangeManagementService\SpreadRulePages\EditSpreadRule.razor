﻿@page "/EditSpreadRule/{SpreadRuleId}/{SpreadRuleName}"
@attribute [Authorize(MoneyExchangeManagementServicePermissions.SpreadRules.Default)]
@using EMPS.MoneyExchangeManagementService.SpreadRuleDetails
@using EMPS.MoneyExchangeManagementService.SpreadRules
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Microsoft.JSInterop
@using Volo.Abp
@using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@inherits MoneyExchangeManagementServiceComponentBase
@inject ISpreadRulesAppService SpreadRulesAppService
@inject ISpreadRuleDetailsAppService SpreadRuleDetailsAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager
@using EMPS.Shared.Enum
@inject IJSRuntime JSRuntime

<PageHeader Title="@(L["SpreadRules"] + " : "+ @SpreadRuleName)" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

<Card>
    <CardBody>

        <EditForm EditContext="editContext">
            <Validations @ref=EditingSpreadRulesValidations Model="@EditingSpreadRules" Mode="ValidationMode.Manual">
                <Table>
                    <TableRow>
                        <TableRowCell>
                            <Field>
                                <FieldLabel>@L["RuleName"] *</FieldLabel>
                                <TextEdit @bind-Text="@EditingSpreadRules.RuleName" ReadOnly="@IsApproved">
                                    <Feedback>
                                        <ValidationError />
                                    </Feedback>
                                </TextEdit>
                            </Field>
                        </TableRowCell>
                        <TableRowCell>

                            <Field>
                                <FieldLabel>@L["Scope"]</FieldLabel>
                                <Select TValue="SpreadRuleScope" @bind-SelectedValue="@EditingSpreadRules.Scope" ReadOnly="@IsApproved">
                                    @foreach (var itemValue in Enum.GetValues(typeof(SpreadRuleScope)))
                                    {
                                        <SelectItem TValue="SpreadRuleScope" Value="@((SpreadRuleScope) itemValue)">
                                            @L[$"Enum:SpreadRuleScope.{((SpreadRuleScope)itemValue).ToString("d")}"]
                                        </SelectItem>
                                    }
                                </Select>
                            </Field>


                        </TableRowCell>
                    </TableRow>

                    <TableRow>
                        <TableRowCell>
                            <Field>
                                <FieldLabel>@L["ActivationDate"]</FieldLabel>
                                <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingSpreadRules.ActivationDate" ReadOnly="@IsApproved">
                                    <Feedback>
                                        <ValidationError />
                                    </Feedback>
                                </DateEdit>
                            </Field>
                        </TableRowCell>
                        <TableRowCell>
                            <Field>
                                <FieldLabel>@L["Description"]</FieldLabel>
                                <MemoEdit @bind-Text="@EditingSpreadRules.Description" ReadOnly="@IsApproved">
                                    <Feedback>
                                        <ValidationError />
                                    </Feedback>
                                </MemoEdit>
                            </Field>
                        </TableRowCell>

                    </TableRow>

                    @if (@IsApproved)
                    {
                        <TableRow>
                            <TableRowCell>
                                <Field>
                                    <FieldLabel>@L["ApprovalDateTime"]</FieldLabel>
                                    <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingSpreadRules.ApprovedDateTime" ReadOnly="@IsApproved">
                                     <Feedback>
                                         <ValidationError />
                                     </Feedback>
                                 </DateEdit>
                             </Field>
                         </TableRowCell>
                         <TableRowCell>
                             <Field>
                                 <FieldLabel>@L["ApprovedByName"]</FieldLabel>
                                    <TextEdit @bind-Text="@EditingSpreadRules.ApprovedByUserName" ReadOnly="@IsApproved">
                                     <Feedback>
                                         <ValidationError />
                                     </Feedback>
                                 </TextEdit>
                             </Field>
                         </TableRowCell>
                     </TableRow>

                    }
                    <TableRow>
                        <TableRowCell>
                            <Field hidden="@IsApproved">
                                <SubmitButton Type=ButtonType.Submit Clicked="UpdateSpreadRulesAsync" />
                            </Field>
                        </TableRowCell>
                    </TableRow>
                </Table>
            </Validations>
        </EditForm>
    </CardBody>
</Card>


@code {

    [Parameter]
    public string SpreadRuleId { get; set; }
    [Parameter]
    public string SpreadRuleName { get; set; }

    [Parameter] public EventCallback<bool> OnDataUpdated { get; set; }


    private bool CanCreateSpreadRules { get; set; }
    private bool CanEditSpreadRules { get; set; }
    private bool CanDeleteSpreadRules { get; set; }
    private Validations EditingSpreadRulesValidations { get; set; } = new();
    private Guid EditingSpreadRulesId { get; set; }



    public bool IsApproved { get; set; }
    private int retryAttempts = 3;
    private SpreadRuleDto SpreadRules { get; set; }
    protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
    protected PageToolbar Toolbar { get; } = new PageToolbar();
    protected string SelectedCreateTab = "SpreadRules-create-tab";
    protected string SelectedEditTab = "SpreadRules-edit-tab";
    private SpreadRuleUpdateDto EditingSpreadRules = new SpreadRuleUpdateDto(); // Initialize your data model
    private EditContext editContext;
    private IReadOnlyList<LookupDto<Guid>> CustomerClassificationsCollection { get; set; } = new List<LookupDto<Guid>>();

    protected override async Task OnInitializedAsync()
    {

        editContext = new EditContext(EditingSpreadRules);
        SpreadRules = await SpreadRulesAppService.GetAsync(Guid.Parse(SpreadRuleId));
        IsApproved = SpreadRules.IsApproved;
        SpreadRuleName = SpreadRules.RuleName;
        EditingSpreadRules = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRules);
        EditingSpreadRulesId = SpreadRules.Id;
        await SetToolbarItemsAsync();
        await SetBreadcrumbItemsAsync();
        await SetPermissionsAsync();

    }
    protected virtual async ValueTask SetToolbarItemsAsync()
    {
        if (!IsApproved)
        {

            Toolbar.AddButton(L["Approve"], async () =>
            {
                var confirmed = await UiMessageService.Confirm(L["ApproveConfirmationMessage"]);
                if (confirmed)
                {
                    await ApproveEntityAsync(Guid.Parse(SpreadRuleId));
                    IsApproved = true;
                }
            }, IconName.Check, requiredPolicyName: MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Create);
            await InvokeAsync(StateHasChanged);
        }


        await ValueTask.CompletedTask;
    }

    protected virtual ValueTask SetBreadcrumbItemsAsync()
    {
        BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:SpreadRules"]));
        return ValueTask.CompletedTask;
    }


    private async Task SetPermissionsAsync()
    {
        CanCreateSpreadRules = await AuthorizationService
            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Create);
        CanEditSpreadRules = await AuthorizationService
                        .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Edit);
        CanDeleteSpreadRules = await AuthorizationService
                        .IsGrantedAsync(MoneyExchangeManagementServicePermissions.SpreadRules.Delete);
    }

    private async Task ApproveEntityAsync(Guid id)
    {
        try
        {
            var SpreadRule = await SpreadRulesAppService.GetAsync(id);
            if (SpreadRule == null)
                throw new UserFriendlyException("ThisSpreadRuleNotFound");
            if (SpreadRule.IsApproved == true)
                throw new UserFriendlyException("ThisSpreadRuleAlreadyApproved");
            EditingSpreadRulesId = id;
            EditingSpreadRules = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRule);
            EditingSpreadRules.ApprovedDateTime = DateTime.Now;
            EditingSpreadRules.ApprovedByUserId = CurrentUser.Id;
            EditingSpreadRules.IsApproved = true;
            EditingSpreadRules.ApprovedByUserName = CurrentUser.Name;
            var customerAfterApproved = await SpreadRulesAppService.UpdateAsync(EditingSpreadRulesId, EditingSpreadRules);
            await UiMessageService.Success(L["SpreadRuleApprovedSuccessfully"]);
            IsApproved = true;

            StateHasChanged();
        }
        catch (Exception)
        {

            throw;
        }
    }


    private async Task UpdateSpreadRulesAsync()
    {
        if (await EditingSpreadRulesValidations.ValidateAll() == false)
            return;
        SpreadRules = await SpreadRulesAppService.GetAsync(EditingSpreadRulesId);
        IsApproved = SpreadRules.IsApproved;
        EditingSpreadRulesId = SpreadRules.Id;
        var customerAfterSaved = await SpreadRulesAppService.UpdateAsync(EditingSpreadRulesId, EditingSpreadRules);
        SpreadRuleName = EditingSpreadRules.RuleName;
        SpreadRules = await SpreadRulesAppService.GetAsync(EditingSpreadRulesId);
        EditingSpreadRules = ObjectMapper.Map<SpreadRuleDto, SpreadRuleUpdateDto>(SpreadRules);
        await UiMessageService.Success(L["SpreadRuleCreatedSuccessfully"]);

        await InvokeAsync(StateHasChanged);
    }
}