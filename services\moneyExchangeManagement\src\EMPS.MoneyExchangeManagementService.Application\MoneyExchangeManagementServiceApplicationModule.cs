﻿using EMPS.CompanyService;
using EMPS.CompanyService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application;
using Volo.Abp.AutoMapper;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.FileSystem;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService;

[DependsOn(
    typeof(MoneyExchangeManagementServiceDomainModule),
    typeof(MoneyExchangeManagementServiceApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule),
        typeof(AbpIdentityHttpApiClientModule),

     typeof(AbpBlobStoringFileSystemModule),
    typeof(CompanyServiceHttpApiClientModule)


    )]
public class MoneyExchangeManagementServiceApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<MoneyExchangeManagementServiceApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<MoneyExchangeManagementServiceApplicationModule>(validate: true);
        });
        var Config = context.Services.GetConfiguration();

        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.ConfigureDefault(c =>
            {
                c.UseFileSystem(f => f.BasePath = Config.GetValue<string>("BlobStorageFolder"));
            });
        });
    }
}
