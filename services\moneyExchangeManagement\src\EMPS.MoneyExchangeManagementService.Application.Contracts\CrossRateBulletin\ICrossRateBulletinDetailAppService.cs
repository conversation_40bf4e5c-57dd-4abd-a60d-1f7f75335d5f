using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    public interface ICrossRateBulletinDetailAppService : ICrudAppService<
        CrossRateBulletinDetailDto,
        Guid,
        GetCrossRateBulletinDetailListDto,
        CreateUpdateCrossRateBulletinDetailDto
    >
    {
        Task<ListResultDto<CrossRateBulletinDetailDto>> GetListByMasterIdAsync(Guid masterId);
        Task<List<CrossRateBulletinDetailDto>> ReFetchDetailsFromProviderAsync(Guid masterId);

    }
}