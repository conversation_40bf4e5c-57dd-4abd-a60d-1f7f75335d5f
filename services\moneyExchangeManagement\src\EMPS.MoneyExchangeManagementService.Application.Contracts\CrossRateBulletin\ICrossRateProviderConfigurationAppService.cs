using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin
{
    /// <summary>
    /// Application service interface for managing CrossRateProviderConfiguration
    /// </summary>
    public interface ICrossRateProviderConfigurationAppService : IApplicationService
    {
        /// <summary>
        /// Gets the current provider configuration (singleton)
        /// </summary>
        /// <returns>The current configuration or null if not configured</returns>
        Task<CrossRateProviderConfigurationDto?> GetAsync();
        
        /// <summary>
        /// Creates or updates the provider configuration (singleton)
        /// </summary>
        /// <param name="input">Configuration data</param>
        /// <returns>The updated configuration</returns>
        Task<CrossRateProviderConfigurationDto> CreateOrUpdateAsync(CreateUpdateCrossRateProviderConfigurationDto input);
        
        /// <summary>
        /// Checks if the provider configuration exists
        /// </summary>
        /// <returns>True if configuration exists, false otherwise</returns>
        Task<bool> ExistsAsync();
    }
}
