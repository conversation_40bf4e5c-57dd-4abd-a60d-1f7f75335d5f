using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public interface IBulletinManagementMasterRepository : IRepository<BulletinManagementMaster, Guid>
    {
        Task<int> GenerateNewNo();
        Task<BulletinManagementMaster?> GetLastBulletinManagementMasterWithNavForServicePoint(Guid servicePointId);
        Task<BulletinManagementMaster?> GetLastGlobalBulletinManagementMasterWithNav();

        Task<List<BulletinManagementMaster>> GetListAsync(
            string filterText = null,
            string bulletinNumber = null,
            string bulletinName = null,
            DateTime? bulletinDateMin = null,
            DateTime? bulletinDateMax = null,
            string notes = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string currencyPairingRuleName = null,
            Guid? currencyPairingRuleId = null,
            string crossRateBulletinName = null,
            Guid? crossRateBulletinId = null,
            string spreadRuleName = null,
            Guid? spreadRuleId = null,
            string publishByUserName = null,
            Guid? publishByUserId = null,
            DateTime? publishDateMin = null,
            DateTime? publishDateMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string bulletinNumber = null,
            string bulletinName = null,
            DateTime? bulletinDateMin = null,
            DateTime? bulletinDateMax = null,
            string notes = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string currencyPairingRuleName = null,
            Guid? currencyPairingRuleId = null,
            string crossRateBulletinName = null,
            Guid? crossRateBulletinId = null,
            string spreadRuleName = null,
            Guid? spreadRuleId = null,
            string publishByUserName = null,
            Guid? publishByUserId = null,
            DateTime? publishDateMin = null,
            DateTime? publishDateMax = null,
            CancellationToken cancellationToken = default);
    }
}