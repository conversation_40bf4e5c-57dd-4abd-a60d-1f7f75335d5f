﻿using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeManagementService;

/* Domain tests are configured to use the EF Core provider.
 * You can switch to MongoDB, however your domain tests should be
 * database independent anyway.
 */
[DependsOn(
    typeof(MoneyExchangeManagementServiceEntityFrameworkCoreTestModule)
    )]
public class MoneyExchangeManagementServiceDomainTestModule : AbpModule
{

}
