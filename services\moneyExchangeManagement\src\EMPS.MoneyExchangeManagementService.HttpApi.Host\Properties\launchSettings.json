{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "https://localhost:45244", "sslPort": 45244}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "EMPS.MoneyExchangeManagementService.HttpApi.Host": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": true, "applicationUrl": "https://localhost:45244", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}