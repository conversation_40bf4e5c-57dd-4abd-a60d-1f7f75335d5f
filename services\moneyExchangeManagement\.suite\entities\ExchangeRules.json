{"Id": "5eed6910-8c80-4177-aef9-680ebce78226", "Name": "ExchangeRules", "OriginalName": "ExchangeRules", "NamePlural": "ExchangeRuless", "DatabaseTableName": "ExchangeRuless", "Namespace": "ExchangeRuless", "BaseClass": "FullAuditedAggregateRoot", "MenuIcon": "file-alt", "PrimaryKeyType": "Guid", "IsMultiTenant": false, "CheckConcurrency": true, "ShouldCreateUserInterface": true, "ShouldCreateBackend": true, "ShouldExportExcel": true, "ShouldAddMigration": true, "ShouldUpdateDatabase": true, "CreateTests": true, "Properties": [{"Id": "b22d3dbe-4f92-4b50-b478-5bd4d86dcc90", "Name": "Name", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": true, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "cd1c30e8-b563-4968-acbe-3ddcdd92264c", "Name": "Description", "Type": "string", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": true, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "7029e189-9959-47b8-978f-9e2614af506e", "Name": "ActivationDate", "Type": "DateTime", "EnumType": "", "EnumNamespace": "", "EnumAngularImport": "shared/enums", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": null, "IsSelected": true, "OrdinalIndex": 0}, {"Id": "33a8fef9-c591-4476-a3b4-46caeb04ee61", "Name": "ExchangeRuleScope", "Type": "enum", "EnumType": "ExchangeRulesScope", "EnumNamespace": "EMPS.Shared.Enum.ExchangeRules", "EnumAngularImport": "shared/enums/exchange-rules-scope", "EnumFilePath": null, "DefaultValue": null, "IsNullable": false, "IsRequired": false, "IsTextArea": false, "MinLength": null, "MaxLength": null, "SortOrder": 0, "SortType": 0, "Regex": "", "EmailValidation": false, "ShowOnList": true, "ShowOnCreateModal": true, "ShowOnEditModal": true, "ReadonlyOnEditModal": false, "EnumValues": {"global": 1, "ServicePoint": 2}, "IsSelected": true, "OrdinalIndex": 0}], "NavigationProperties": [], "NavigationConnections": [], "PhysicalFileName": "ExchangeRules.json"}