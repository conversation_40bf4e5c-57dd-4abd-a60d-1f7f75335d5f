using Shouldly;
using System;
using System.Linq;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Xunit;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulesRepositoryTests : MoneyExchangeManagementServiceEntityFrameworkCoreTestBase
    {
        private readonly IExchangeRulesRepository _exchangeRulesRepository;

        public ExchangeRulesRepositoryTests()
        {
            _exchangeRulesRepository = GetRequiredService<IExchangeRulesRepository>();
        }

        [Fact]
        public async Task GetListAsync()
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                var result = await _exchangeRulesRepository.GetListAsync(
                    name: "bda84c4e10ec43acae467c7cdb3195f8b669bcb91ce84b6dbc845e38db75ec556dff7a9a863c44ea83b1b4",
                    description: "5c9f89beb4544f15a4",
                    exchangeRuleScope: default
                );

                // Assert
                result.Count.ShouldBe(1);
                result.FirstOrDefault().ShouldNotBe(null);
                result.First().Id.ShouldBe(Guid.Parse("35f65c71-4bfc-4a18-9dc2-4d89bff6cac2"));
            });
        }

        [Fact]
        public async Task GetCountAsync()
        {
            // Arrange
            await WithUnitOfWorkAsync(async () =>
            {
                // Act
                var result = await _exchangeRulesRepository.GetCountAsync(
                    name: "451ac4e4f7b64b48b75a048",
                    description: "e3e7011fb7924f6d8e1a197751630a94ce2b1f3c6cd84da19a42",
                    exchangeRuleScope: default
                );

                // Assert
                result.ShouldBe(1);
            });
        }
    }
}