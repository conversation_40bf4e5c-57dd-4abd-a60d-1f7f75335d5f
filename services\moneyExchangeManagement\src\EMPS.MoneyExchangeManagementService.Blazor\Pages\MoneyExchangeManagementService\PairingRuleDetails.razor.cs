using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.PairingRuleDetails;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.Shared;
using Microsoft.AspNetCore.Components;
using EMPS.MoneyExchangeManagementService.PairingRules;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService
{
    public partial class PairingRuleDetails
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();
        private IReadOnlyList<PairingRuleDetailWithNavigationPropertiesDto> PairingRuleDetailList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreatePairingRuleDetail { get; set; }
        private bool CanEditPairingRuleDetail { get; set; }
        private bool CanDeletePairingRuleDetail { get; set; }
        private PairingRuleDetailCreateDto NewPairingRuleDetail { get; set; }
        private Validations NewPairingRuleDetailValidations { get; set; } = new();
        private PairingRuleDetailUpdateDto EditingPairingRuleDetail { get; set; }
        private Validations EditingPairingRuleDetailValidations { get; set; } = new();
        private Guid EditingPairingRuleDetailId { get; set; }
        private Modal CreatePairingRuleDetailModal { get; set; } = new();
        private Modal EditPairingRuleDetailModal { get; set; } = new();
        private GetPairingRuleDetailsInput Filter { get; set; }
        private DataGridEntityActionsColumn<PairingRuleDetailWithNavigationPropertiesDto> EntityActionsColumn { get; set; } = new();
        protected string SelectedCreateTab = "pairingRuleDetail-create-tab";
        protected string SelectedEditTab = "pairingRuleDetail-edit-tab";
        private IReadOnlyList<LookupDto<Guid>> PairingRulesCollection { get; set; } = new List<LookupDto<Guid>>();

        [Inject]
        private IPairingRulesAppService PairingRulesAppService { get; set; }
        bool oninit = false;

        [Parameter]
        public string? PairingRuleId { get; set; }

        private PairingRuleDto? pairingRuleDto { get; set; }

        public PairingRuleDetails()
        {
            NewPairingRuleDetail = new PairingRuleDetailCreateDto();
            EditingPairingRuleDetail = new PairingRuleDetailUpdateDto();
            Filter = new GetPairingRuleDetailsInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            PairingRuleDetailList = new List<PairingRuleDetailWithNavigationPropertiesDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
            pairingRuleDto = await PairingRulesAppService.GetAsync(Guid.Parse(PairingRuleId));


        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:PairingRuleDetails"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["ExportToExcel"], async () => { await DownloadAsExcelAsync(); }, IconName.Download);

            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreatePairingRuleDetail = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Create);
            CanEditPairingRuleDetail = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Edit);
            CanDeletePairingRuleDetail = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeManagementServicePermissions.PairingRuleDetails.Delete);
        }

        private async Task GetPairingRuleDetailsAsync()
        {
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = "PairingRuleDetail.DisplayOrder";
            Guid ruleId;
            if (Guid.TryParse(PairingRuleId, out ruleId))
            {
                Filter.PairingRuleId = ruleId;
            }

            var result = await PairingRuleDetailsAppService.GetListAsync(Filter);
            PairingRuleDetailList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetPairingRuleDetailsAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task DownloadAsExcelAsync()
        {
            var token = (await PairingRuleDetailsAppService.GetDownloadTokenAsync()).Token;
            var remoteService = await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("MoneyExchangeManagementService") ??
            await RemoteServiceConfigurationProvider.GetConfigurationOrDefaultOrNullAsync("Default");
            NavigationManager.NavigateTo($"{remoteService?.BaseUrl.EnsureEndsWith('/') ?? string.Empty}api/money-exchange-management-service/pairing-rule-details/as-excel-file?DownloadToken={token}&FilterText={Filter.FilterText}", forceLoad: true);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<PairingRuleDetailWithNavigationPropertiesDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetPairingRuleDetailsAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreatePairingRuleDetailModalAsync()
        {
            NewPairingRuleDetail = new PairingRuleDetailCreateDto
            {

                PairingRuleId = PairingRulesCollection.Select(i => i.Id).FirstOrDefault(),

            };
            await NewPairingRuleDetailValidations.ClearAll();
            await CreatePairingRuleDetailModal.Show();
        }

        private async Task CloseCreatePairingRuleDetailModalAsync()
        {
            NewPairingRuleDetail = new PairingRuleDetailCreateDto
            {

                PairingRuleId = PairingRulesCollection.Select(i => i.Id).FirstOrDefault(),

            };
            await CreatePairingRuleDetailModal.Hide();
        }

        private async Task OpenEditPairingRuleDetailModalAsync(PairingRuleDetailWithNavigationPropertiesDto input)
        {
            var pairingRuleDetail = await PairingRuleDetailsAppService.GetWithNavigationPropertiesAsync(input.PairingRuleDetail.Id);

            EditingPairingRuleDetailId = pairingRuleDetail.PairingRuleDetail.Id;
            EditingPairingRuleDetail = ObjectMapper.Map<PairingRuleDetailDto, PairingRuleDetailUpdateDto>(pairingRuleDetail.PairingRuleDetail);
            await EditingPairingRuleDetailValidations.ClearAll();
            await EditPairingRuleDetailModal.Show();
        }

        private async Task DeletePairingRuleDetailAsync(PairingRuleDetailWithNavigationPropertiesDto input)
        {
            await PairingRuleDetailsAppService.DeleteAsync(input.PairingRuleDetail.Id);
            await GetPairingRuleDetailsAsync();
        }

        private async Task CreatePairingRuleDetailAsync()
        {
            try
            {
                await PairingRuleDetailsAppService.CreateAllActiveCurrenciesPairings(Guid.Parse(PairingRuleId));
                await GetPairingRuleDetailsAsync();
                await CloseCreatePairingRuleDetailModalAsync();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditPairingRuleDetailModalAsync()
        {
            await EditPairingRuleDetailModal.Hide();
        }

        private async Task UpdatePairingRuleDetailAsync()
        {
            try
            {
                if (await EditingPairingRuleDetailValidations.ValidateAll() == false)
                {
                    return;
                }

                await PairingRuleDetailsAppService.UpdateAsync(EditingPairingRuleDetailId, EditingPairingRuleDetail);
                await GetPairingRuleDetailsAsync();
                await EditPairingRuleDetailModal.Hide();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private void OnSelectedCreateTabChanged(string name)
        {
            SelectedCreateTab = name;
        }

        private void OnSelectedEditTabChanged(string name)
        {
            SelectedEditTab = name;
        }





        private async Task UpAPI(PairingRuleDetailWithNavigationPropertiesDto context)
        {
            await PairingRuleDetailsAppService.MoveUp(context.PairingRuleDetail.Id, Guid.Parse(PairingRuleId));

            await GetPairingRuleDetailsAsync();
        }

        private async Task DownAPI(PairingRuleDetailWithNavigationPropertiesDto context)
        {
            await PairingRuleDetailsAppService.MoveDown(context.PairingRuleDetail.Id, Guid.Parse(PairingRuleId));

            await GetPairingRuleDetailsAsync();
        }

        private async Task SwitchAPI(PairingRuleDetailWithNavigationPropertiesDto context)
        {
            await PairingRuleDetailsAppService.SwitchPairings(context.PairingRuleDetail.Id, Guid.Parse(PairingRuleId));

            await GetPairingRuleDetailsAsync();
        }

    }
}
