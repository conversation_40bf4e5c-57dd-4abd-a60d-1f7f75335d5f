using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public interface IPairingRuleDetailRepository : IRepository<PairingRuleDetail, Guid>
    {
        Task<PairingRuleDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(
    Guid id,
    CancellationToken cancellationToken = default
);

        Task<List<PairingRuleDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? pairingRuleId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<List<PairingRuleDetail>> GetListAsync(
                    string filterText = null,
                    Guid? baseId = null,
                    string baseCode = null,
                    Guid? quoteId = null,
                    string quoteCode = null,
                    string pairingFormat = null,
                    int? displayOrderMin = null,
                    int? displayOrderMax = null,
                    string sorting = null,
                    int maxResultCount = int.MaxValue,
                    int skipCount = 0,
                    CancellationToken cancellationToken = default
                );

        Task<long> GetCountAsync(
            string filterText = null,
            Guid? baseId = null,
            string baseCode = null,
            Guid? quoteId = null,
            string quoteCode = null,
            string pairingFormat = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? pairingRuleId = null,
            CancellationToken cancellationToken = default);
    }
}