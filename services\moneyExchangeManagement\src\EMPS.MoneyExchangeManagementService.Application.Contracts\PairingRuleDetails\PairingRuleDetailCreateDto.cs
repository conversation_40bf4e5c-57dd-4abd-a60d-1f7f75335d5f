using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.PairingRuleDetails
{
    public class PairingRuleDetailCreateDto
    {
        [Required]
        public Guid BaseId { get; set; }
        [Required]
        public string BaseCode { get; set; }
        [Required]
        public Guid QuoteId { get; set; }
        [Required]
        public string QuoteCode { get; set; }
        public string? PairingFormat { get; set; }
        public int DisplayOrder { get; set; }
        public Guid PairingRuleId { get; set; }
    }
}