using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class BulletinManagementDetailManager : DomainService
    {
        private readonly IBulletinManagementDetailRepository _bulletinManagementDetailRepository;

        public BulletinManagementDetailManager(IBulletinManagementDetailRepository bulletinManagementDetailRepository)
        {
            _bulletinManagementDetailRepository = bulletinManagementDetailRepository;
        }

        public async Task<BulletinManagementDetail> CreateAsync(
        Guid bulletinManagementMasterId, string currencyPair, double cashBid, double cashAsk, double accountBid, double accountAsk, int displayOrder)
        {
            Check.NotNull(bulletinManagementMasterId, nameof(bulletinManagementMasterId));
            Check.NotNullOrWhiteSpace(currencyPair, nameof(currencyPair));

            var bulletinManagementDetail = new BulletinManagementDetail(
             GuidGenerator.Create(),
             bulletinManagementMasterId, currencyPair, cashBid, cashAsk, accountBid, accountAsk, displayOrder
             );

            return await _bulletinManagementDetailRepository.InsertAsync(bulletinManagementDetail);
        }

        public async Task<BulletinManagementDetail> UpdateAsync(
            Guid id,
            Guid bulletinManagementMasterId, string currencyPair, double cashBid, double cashAsk, double accountBid, double accountAsk, int displayOrder, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(bulletinManagementMasterId, nameof(bulletinManagementMasterId));
            Check.NotNullOrWhiteSpace(currencyPair, nameof(currencyPair));

            var bulletinManagementDetail = await _bulletinManagementDetailRepository.GetAsync(id);

            bulletinManagementDetail.BulletinManagementMasterId = bulletinManagementMasterId;
            bulletinManagementDetail.CurrencyPair = currencyPair;
            bulletinManagementDetail.CashBid = cashBid;
            bulletinManagementDetail.CashAsk = cashAsk;
            bulletinManagementDetail.AccountBid = accountBid;
            bulletinManagementDetail.AccountAsk = accountAsk;
            bulletinManagementDetail.DisplayOrder = displayOrder;

            bulletinManagementDetail.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _bulletinManagementDetailRepository.UpdateAsync(bulletinManagementDetail);
        }

    }
}