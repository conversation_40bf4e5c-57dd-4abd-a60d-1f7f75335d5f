using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public class BulletinManagementDetailDto : FullAuditedEntityDto<Guid>, IHasConcurrencyStamp
    {
        public string CurrencyPair { get; set; }
        public double CashBid { get; set; }
        public double CashAsk { get; set; }
        public double AccountBid { get; set; }
        public double AccountAsk { get; set; }
        public int DisplayOrder { get; set; }
        public Guid BulletinManagementMasterId { get; set; }
        public  string? CurrencyBaseCode { get; set; }

        public  Guid? CurrencyBaseId { get; set; }
        public  string? CurrencyQuoteCode { get; set; }

        public  Guid? CurrencyQuoteId { get; set; }

        public string ConcurrencyStamp { get; set; }
    }
}