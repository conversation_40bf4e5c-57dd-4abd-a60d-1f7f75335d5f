using EMPS.CompanyService.ServicePoints;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public interface IBulletinManagementMastersAppService : IApplicationService
    {
        Task<PagedResultDto<BulletinManagementMasterDto>> GetListAsync(GetBulletinManagementMastersInput input);
        Task<BulletinManagementMasterDto?> GetBulletinManagementWithNavigation(Guid? servicePointId = null);
        Task<BulletinManagementMasterDto> GetAsync(Guid id);

        Task<List<BulletinManagementDetailDto>> PreviewBulletin(RequestPreviewBulletinDto request);
        Task Publish(bool IsGlobal, List<Guid> ServicePointIds);
        Task<List<ServicePointDto>> GetAllServicePointHaveSpreadRuleId();
        Task<List<BulletinManagementDetailDto>> GetListOfDetailsByMasterId(Guid masterId);
    }
}