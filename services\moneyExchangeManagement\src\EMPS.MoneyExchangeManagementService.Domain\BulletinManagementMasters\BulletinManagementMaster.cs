using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;
using System.Data;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementMasters
{
    public class BulletinManagementMaster : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string BulletinNumber { get; set; }

        [NotNull]
        public virtual string BulletinName { get; set; }

        public virtual DateTime BulletinDate { get; set; }

        [CanBeNull]
        public virtual string? Notes { get; set; }

        [CanBeNull]
        public virtual string? ServicePointName { get; set; }

        public virtual Guid? ServicePointId { get; set; }
        public bool IsGlobal { get; set; }

        public List<BulletinManagementDetail> Details { get; set; }

        [CanBeNull]
        public virtual string? CurrencyPairingRuleName { get; set; }

        public virtual Guid? CurrencyPairingRuleId { get; set; }

        [CanBeNull]
        public virtual string? CrossRateBulletinName { get; set; }

        public virtual Guid? CrossRateBulletinId { get; set; }

        [CanBeNull]
        public virtual string? SpreadRuleName { get; set; }

        public virtual Guid? SpreadRuleId { get; set; }

        [CanBeNull]
        public virtual string? PublishByUserName { get; set; }

        public virtual Guid? PublishByUserId { get; set; }

        public virtual DateTime? PublishDate { get; set; }

        public BulletinManagementMaster()
        {

        }

        public BulletinManagementMaster(Guid id, string bulletinNumber, string bulletinName, DateTime bulletinDate, string notes, string servicePointName, string currencyPairingRuleName, string crossRateBulletinName, string spreadRuleName, string publishByUserName, Guid? servicePointId = null, Guid? currencyPairingRuleId = null, Guid? crossRateBulletinId = null, Guid? spreadRuleId = null, Guid? publishByUserId = null, DateTime? publishDate = null)
        {

            Id = id;
            Check.NotNull(bulletinNumber, nameof(bulletinNumber));
            Check.NotNull(bulletinName, nameof(bulletinName));
            BulletinNumber = bulletinNumber;
            BulletinName = bulletinName;
            BulletinDate = bulletinDate;
            Notes = notes;
            ServicePointName = servicePointName;
            CurrencyPairingRuleName = currencyPairingRuleName;
            CrossRateBulletinName = crossRateBulletinName;
            SpreadRuleName = spreadRuleName;
            PublishByUserName = publishByUserName;
            ServicePointId = servicePointId;
            CurrencyPairingRuleId = currencyPairingRuleId;
            CrossRateBulletinId = crossRateBulletinId;
            SpreadRuleId = spreadRuleId;
            PublishByUserId = publishByUserId;
            PublishDate = publishDate;
        }



        public void FillPublishUserInfo(string? publishByUserName, Guid? publishByUserId)
        {
            PublishByUserName = publishByUserName;
            PublishByUserId = publishByUserId;
            PublishDate = DateTime.Now;
        }

        public void FillSpreadRuleInfo(string? spreadRuleName, Guid? spreadRuleId)
        {
            SpreadRuleName = spreadRuleName;
            SpreadRuleId = spreadRuleId;
        }

        public void FillCrossRateInfo(string? crossRateBulletinName, Guid? crossRateBulletinId)
        {
            CrossRateBulletinName = crossRateBulletinName;
            CrossRateBulletinId = crossRateBulletinId;
        }

        public void FillParingRuleInfo(string? currencyPairingRuleName, Guid? currencyPairingRuleId)
        {
            CurrencyPairingRuleName = currencyPairingRuleName;
            CurrencyPairingRuleId = currencyPairingRuleId;
        }

        public void FillServicePointInfo(string? servicePointName, Guid? servicePointId)
        {
            ServicePointName = servicePointName;
            ServicePointId = servicePointId;
        }

        public void FillTheBaseInfo(string bulletinNumber, string bulletinName, string? notes)
        {
            BulletinNumber = bulletinNumber;
            BulletinName = bulletinName;
            BulletinDate = DateTime.Now;
            Notes = notes;
        }
    }
}