@page "/MoneyExchangeManagementService/BulletinManagement"

@attribute [Authorize(MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Default)]
@using Blazorise.LoadingIndicator
@using EMPS.MoneyExchangeManagementService.BulletinManagementMasters
@using EMPS.MoneyExchangeManagementService.BulletinManagementDetails
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using EMPS.CompanyService.ServicePoints
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions

@inherits MoneyExchangeManagementServiceComponentBase
@inject IBulletinManagementMastersAppService BulletinManagementMastersAppService
@inject IUiMessageService UiMessageService

@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["BulletinManagement"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">
</PageHeader>

@* ************************* CUSTOM STYLES ************************* *@
<style>
    .custom-datagrid .table-striped tbody tr:nth-of-type(odd) {
        background-color: #ffffff;
    }

    .custom-datagrid .table-striped tbody tr:nth-of-type(even) {
        background-color: #f8f9fa;
    }

    .custom-datagrid .table-bordered th,
    .custom-datagrid .table-bordered td {
        border: 1px solid #dee2e6;
    }

    .custom-datagrid .table thead th {
        background-color: #e9ecef;
        border-bottom: 2px solid #dee2e6;
    }

    .service-point-item.selected {
        background-color: #0d6efd !important;
        color: white !important;
    }

    .service-point-item {
        transition: background-color 0.2s ease;
    }

    .service-point-button {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        color: inherit !important;
        transition: all 0.2s ease;
    }

        .service-point-button:hover {
            background: rgba(0, 123, 255, 0.1) !important;
            color: inherit !important;
            transform: scale(1.02);
        }

        .service-point-button:active {
            background: rgba(0, 123, 255, 0.2) !important;
            transform: scale(0.98);
        }

    .global-bulletin-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        background-color: #ffffff;
        transition: background-color 0.2s ease;
    }

        .global-bulletin-item.selected {
            background-color: #0d6efd !important;
            color: white !important;
        }

    .scrollable-list {
        direction: ltr;
    }

        .scrollable-list .list-group-item {
            direction: ltr;
        }

    /* Preview indicator styles */
    .preview-indicator {
        background-color: #e3f2fd;
        border: 2px solid #2196f3;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

        .preview-indicator .preview-icon {
            color: #2196f3;
            font-size: 1.1rem;
        }

        .preview-indicator .preview-text {
            color: #1976d2;
            font-weight: 500;
            margin: 0;
        }

    /* Currently previewing styles for items */
    .global-bulletin-item.currently-previewing {
        border: 2px solid #2196f3 !important;
        background-color: #e3f2fd !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    }

    .service-point-item.currently-previewing {
        border: 2px solid #2196f3 !important;
        background-color: #e3f2fd !important;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    }

    /* Ensure preview styling takes precedence over selection styling */
    .global-bulletin-item.currently-previewing,
    .service-point-item.currently-previewing {
        color: #1976d2 !important;
    }
</style>
@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="BulletinManagementMasterSearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText" Autofocus="true" Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="BulletinManagementMasterSearchForm" Clicked="GetBulletinManagementMastersAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="BulletinManagementMasterDto" Data="BulletinManagementMasterList" ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount" ShowPager="true" Responsive="true" PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="BulletinManagementMasterDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="BulletinManagementMasterDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="BulletinManagementMasterDto" Text="@L["ShowDetail"]"
                                          Clicked="async () => await OpenShowDetailModalAsync(context)" Icon="fas fa-eye" />
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>

                <DataGridColumn TItem="BulletinManagementMasterDto" Field="BulletinNumber"
                                Caption="@L["BulletinNumber"]">
                </DataGridColumn>

                <DataGridColumn TItem="BulletinManagementMasterDto" Field="BulletinName" Caption="@L["BulletinName"]">
                    <DisplayTemplate>
                        @L["BulletinNo"] @(context.BulletinName)
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="BulletinManagementMasterDto" Field="BulletinDate" Caption="@L["BulletinDate"]">
                    <DisplayTemplate>
                        @context.BulletinDate.ToShortDateString()
                    </DisplayTemplate>
                </DataGridColumn>

                <DataGridColumn TItem="BulletinManagementMasterDto" Field="ServicePointName"
                                Caption="@L["ServicePointName"]">
                </DataGridColumn>



                <DataGridColumn TItem="BulletinManagementMasterDto" Field="CurrencyPairingRuleName"
                                Caption="@L["CurrencyPairingRuleName"]">
                </DataGridColumn>



                <DataGridColumn TItem="BulletinManagementMasterDto" Field="CrossRateBulletinName"
                                Caption="@L["CrossRateBulletinName"]">
                </DataGridColumn>



                <DataGridColumn TItem="BulletinManagementMasterDto" Field="SpreadRuleName"
                                Caption="@L["SpreadRuleName"]">
                </DataGridColumn>



                <DataGridColumn TItem="BulletinManagementMasterDto" Field="PublishByUserName"
                                Caption="@L["PublishByUserName"]">
                </DataGridColumn>



                <DataGridColumn TItem="BulletinManagementMasterDto" Field="PublishDate" Caption="@L["PublishDate"]">
                    <DisplayTemplate>
                        @(context.PublishDate.HasValue ? context.PublishDate.Value.ToShortDateString() : string.Empty)
                    </DisplayTemplate>
                </DataGridColumn>

            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>
@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreateBulletinModal" Closing="@CreateBulletinModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true" Size="ModalSize.ExtraLarge">
        <Form id="CreateBulletinForm">
            <ModalHeader>
                <ModalTitle>@L["CurrentBulletinDate"]: @CurrentBulletinDate.ToShortDateString()</ModalTitle>
                <CloseButton Clicked="CloseCreateBulletinModalAsync" />
            </ModalHeader>
            <ModalBody>

                <Divider />

                <Row>
                    @* ************************* RIGHT COLUMN (25%) ************************* *@
                    <Column ColumnSize="ColumnSize.Is3">
                        <Card>
                            <CardBody>
                                @* Global Bulletin Item - Outside ListGroup *@
                                <div class="@($"global-bulletin-item {(IsGlobalBulletinSelected ? "selected" : "")}")">
                                    <div class="d-flex align-items-center w-100 p-3">

                                        <Button Color="Color.Light" Clicked="@OnGlobalBulletinClicked"
                                                Class="ms-2 flex-grow-1 text-start service-point-button">
                                            @L["GlobalBulletin"]
                                        </Button>
                                        <Check TValue="bool" Checked="@IsGlobalBulletinSelected"
                                               CheckedChanged="@OnGlobalBulletinCheckChanged" />
                                    </div>
                                </div>

                                <TextEdit Placeholder="@L["FilterServicePoints"]" Text="@ServicePointFilter"
                                          TextChanged="@OnFilterTextChanged" />

                                <Divider />

                                <ListGroup Class="scrollable-list" Style="max-height: 400px; overflow-y: auto;">

                                    @* Select All Item *@
                                    <ListGroupItem Class="@($"service-point-item {(IsAllServicePointsSelected ? "selected" : "")}")"
                                                   Style="border: 1px solid #dee2e6;">
                                        <div class="d-flex align-items-center w-100">
                                            <Check TValue="bool" Checked="@IsAllServicePointsSelected"
                                                   CheckedChanged="@OnSelectAllServicePointsChanged" />
                                            <Label Class="ms-2 flex-grow-1 text-start">@L["SelectAll"]</Label>
                                        </div>
                                    </ListGroupItem>

                                    @* Service Points *@
                                    @foreach (var servicePoint in FilteredServicePoints)
                                    {
                                        <ListGroupItem Class="@($"service-point-item {(IsServicePointSelected(servicePoint) ? "selected" : "")}")"
                                                       Style="border: 1px solid #dee2e6;">
                                            <div class="d-flex align-items-center w-100">
                                                <Check TValue="bool" Checked="@IsServicePointSelected(servicePoint)"
                                                       CheckedChanged="@(v => OnServicePointSelectionChanged(servicePoint, v))" />
                                                <Button Color="Color.Light"
                                                        Clicked="@(() => OnServicePointClicked(servicePoint))"
                                                        Class="ms-2 flex-grow-1 text-start service-point-button">
                                                    @servicePoint.Name
                                                </Button>
                                            </div>
                                        </ListGroupItem>
                                    }
                                </ListGroup>
                            </CardBody>
                        </Card>
                    </Column>

                    @* ************************* LEFT COLUMN (75%) ************************* *@
                    <Column ColumnSize="ColumnSize.Is9">

                        @* Preview Context Indicator *@
                        @if (!string.IsNullOrEmpty(CurrentPreviewContext))
                        {
                            <div class="preview-indicator">
                                <Icon Name="IconName.Eye" Class="preview-icon" />
                                <p class="preview-text">@CurrentPreviewContext</p>
                            </div>
                        }

                        <Card>
                            <CardBody>
                                <LoadingIndicator @ref="loadingIndicator">
                                    <div class="custom-datagrid">
                                        <DataGrid TItem="BulletinManagementDetailDto"
                                                  Data="@BulletinDetails.OrderBy(x => x.DisplayOrder)" Responsive="true"
                                                  PageSize="BulletinDetails.Count" ShowPager="false"
                                                  Class="table table-bordered table-striped">
                                            <DataGridColumns>
                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CurrencyPair"
                                                                Caption="@L["CurrencyPair"]">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CashBid"
                                                                Caption="@L["CashBid"]">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CashAsk"
                                                                Caption="@L["CashAsk"]">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="AccountBid"
                                                                Caption="@L["AccountBid"]">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="AccountAsk"
                                                                Caption="@L["AccountAsk"]">
                                                </DataGridColumn>
                                            </DataGridColumns>
                                        </DataGrid>
                                    </div>
                                </LoadingIndicator>
                            </CardBody>
                        </Card>

                    </Column>
                </Row>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary" Clicked="CloseCreateBulletinModalAsync">
                    @L["Cancel"]
                </Button>
                <Button Color="Color.Primary" Clicked="PublishBulletinAsync"
                        RequiredPolicyName="@MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Publish">
                    @L["Publish"]
                </Button>
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@* ************************* SHOW DETAIL MODAL ************************* *@
<Modal @ref="ShowDetailModal" Closing="@ShowDetailModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true" Size="ModalSize.ExtraLarge">
        <ModalHeader>
            <ModalTitle>@L["BulletinDetails"]</ModalTitle>
            <CloseButton Clicked="CloseShowDetailModalAsync" />
        </ModalHeader>
        <ModalBody>
            <Tabs @ref="DetailTabs" SelectedTab="@SelectedDetailTab" SelectedTabChanged="@OnSelectedDetailTabChanged">
                <Items>
                    <Tab Name="master">@L["Master"]</Tab>
                    <Tab Name="details">@L["Details"]</Tab>
                </Items>
                <Content>
                    <TabPanel Name="master">
                        @if (SelectedBulletinMaster != null)
                        {
                            <Card>
                                <CardBody>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["BulletinNumber"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.BulletinNumber"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["BulletinName"]</FieldLabel>
                                                <TextEdit Text="@($"{L["BulletinNo"]} {SelectedBulletinMaster.BulletinName}")"
                                                          ReadOnly="true" />

                                            </Field>
                                        </Column>
                                    </Row>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["BulletinDate"]</FieldLabel>
                                                <DateEdit @bind-Date="@SelectedBulletinMaster.BulletinDate"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["ServicePointName"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.ServicePointName"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                    </Row>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["CurrencyPairingRuleName"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.CurrencyPairingRuleName"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["CrossRateBulletinName"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.CrossRateBulletinName"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                    </Row>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["SpreadRuleName"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.SpreadRuleName"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["PublishByUserName"]</FieldLabel>
                                                <TextEdit @bind-Text="@SelectedBulletinMaster.PublishByUserName"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                    </Row>
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is6">
                                            <Field>
                                                <FieldLabel>@L["PublishDate"]</FieldLabel>
                                                <DateEdit @bind-Date="@SelectedBulletinMaster.PublishDate"
                                                          ReadOnly="true" />
                                            </Field>
                                        </Column>
                                    </Row>
                                </CardBody>
                            </Card>
                        }
                    </TabPanel>
                    <TabPanel Name="details">
                        @if (SelectedBulletinDetails != null)
                        {
                            <Card>
                                <CardBody>
                                    <div class="custom-datagrid">
                                        <DataGrid TItem="BulletinManagementDetailDto"
                                                  Data="@SelectedBulletinDetails.OrderBy(x => x.DisplayOrder)" Responsive="true"
                                                  ShowPager="false" Editable="false" Class="table table-bordered table-striped">
                                            <DataGridColumns>
                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CurrencyPair"
                                                                Caption="@L["CurrencyPair"]" Editable="false">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CashBid"
                                                                Caption="@L["CashBid"]" Editable="false">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="CashAsk"
                                                                Caption="@L["CashAsk"]" Editable="false">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="AccountBid"
                                                                Caption="@L["AccountBid"]" Editable="false">
                                                </DataGridColumn>

                                                <DataGridColumn TItem="BulletinManagementDetailDto" Field="AccountAsk"
                                                                Caption="@L["AccountAsk"]" Editable="false">
                                                </DataGridColumn>


                                            </DataGridColumns>
                                        </DataGrid>
                                    </div>
                                </CardBody>
                            </Card>
                        }
                    </TabPanel>
                </Content>
            </Tabs>
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseShowDetailModalAsync">
                @L["Close"]
            </Button>
        </ModalFooter>
    </ModalContent>
</Modal>
