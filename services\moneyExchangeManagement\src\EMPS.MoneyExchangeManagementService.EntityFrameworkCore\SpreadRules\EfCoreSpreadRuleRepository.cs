using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class EfCoreSpreadRuleRepository : EfCoreRepository<MoneyExchangeManagementServiceDbContext, SpreadRule, Guid>, ISpreadRuleRepository
    {
        public EfCoreSpreadRuleRepository(IDbContextProvider<MoneyExchangeManagementServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }


        public async Task<SpreadRule> GetWithDetailsAsync(Guid id, SpreadRuleType type)
        {
            var query = await GetQueryableAsync();
            query = query.Include(x => x.SpreadRuleDetails.Where(y => y.Type == type));
            return await query.FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<List<SpreadRule>> GetListAsync(
            string filterText = null,
            string ruleName = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            SpreadRuleScope? scope = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, ruleName, activationDateMin, activationDateMax, scope, description, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? SpreadRuleConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string ruleName = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            SpreadRuleScope? scope = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, ruleName, activationDateMin, activationDateMax, scope, description, isApproved, approvedByUserId, approvedByUserName, approvedDateTimeMin, approvedDateTimeMax, isArchived, archivedByUserId, archivedByUserName, archivedDateTimeMin, archivedDateTimeMax);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<SpreadRule> ApplyFilter(
            IQueryable<SpreadRule> query,
            string filterText,
            string ruleName = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            SpreadRuleScope? scope = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.RuleName.Contains(filterText) || e.Description.Contains(filterText) ||
                    e.ApprovedByUserName.Contains(filterText) || e.ArchivedByUserName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(ruleName), e => e.RuleName.Contains(ruleName))
                    .WhereIf(activationDateMin.HasValue, e => e.ActivationDate >= activationDateMin.Value)
                    .WhereIf(activationDateMax.HasValue, e => e.ActivationDate <= activationDateMax.Value)
                    .WhereIf(scope.HasValue, e => e.Scope == scope)
                    .WhereIf(!string.IsNullOrWhiteSpace(description), e => e.Description.Contains(description))
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserId.ToString()), e => e.ApprovedByUserId.ToString().Equals(approvedByUserId))
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserName), e => e.ApprovedByUserName.Contains(approvedByUserName))
                    .WhereIf(approvedDateTimeMin.HasValue, e => e.ApprovedDateTime >= approvedDateTimeMin.Value)
                    .WhereIf(approvedDateTimeMax.HasValue, e => e.ApprovedDateTime <= approvedDateTimeMax.Value)
                    .WhereIf(isArchived.HasValue, e => e.IsArchived == isArchived)
                    .WhereIf(!string.IsNullOrWhiteSpace(archivedByUserId.ToString()), e => e.ArchivedByUserId.ToString().Equals(archivedByUserId))
                    .WhereIf(!string.IsNullOrWhiteSpace(archivedByUserName), e => e.ArchivedByUserName.Contains(archivedByUserName))
                    .WhereIf(archivedDateTimeMin.HasValue, e => e.ArchivedDateTime >= archivedDateTimeMin.Value)
                    .WhereIf(archivedDateTimeMax.HasValue, e => e.ArchivedDateTime <= archivedDateTimeMax.Value);
        }

        public async Task<List<SpreadRule>?> GetSpreadRulesByGlobalAndIdsAsync(bool isGlobal, List<Guid>? spreadRuleIds)
        {
            if (spreadRuleIds == null && isGlobal == false)
                return null;

            var spreadRules = new List<SpreadRule>();
            var currentTime = DateTime.Now;

            if (isGlobal)
            {
                var globalSpreadRule = (await GetQueryableAsync())
                    .Where(x => x.IsApproved && x.ActivationDate <= currentTime && x.Scope == SpreadRuleScope.Global)
                    .OrderByDescending(c => c.ActivationDate)
                    .Include(x => x.SpreadRuleDetails)
                    .FirstOrDefault();

                if (globalSpreadRule != null)
                {
                    spreadRules.Add(globalSpreadRule);
                }
            }

            if (spreadRuleIds != null && spreadRuleIds.Any())
            {
                spreadRuleIds = spreadRuleIds.Distinct().ToList();
                var customSpreadRules = (await GetQueryableAsync())
                    .Where(x => x.IsApproved &&
                           spreadRuleIds.Contains(x.Id) &&
                           x.ActivationDate <= currentTime)
                    .Include(x => x.SpreadRuleDetails)
                    .ToList();

                spreadRules.AddRange(customSpreadRules);
            }

            return spreadRules;
        }
    }
}