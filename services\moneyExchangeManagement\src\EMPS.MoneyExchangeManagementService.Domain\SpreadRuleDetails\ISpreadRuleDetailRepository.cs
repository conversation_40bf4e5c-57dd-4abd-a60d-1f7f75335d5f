using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public interface ISpreadRuleDetailRepository : IRepository<SpreadRuleDetail, Guid>
    {
        Task<SpreadRuleDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(
    Guid id,
    CancellationToken cancellationToken = default
);

        Task<List<SpreadRuleDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            Guid? spreadRuleId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<List<SpreadRuleDetail>> GetListAsync(
                    string filterText = null,
                    Guid? currencyId = null,
                    string currencyCode = null,
                    SpreadRuleType? type = null,
                    double? bidSpreadMin = null,
                    double? bidSpreadMax = null,
                    double? bidMaxDiscountMin = null,
                    double? bidMaxDiscountMax = null,
                    double? bidMaxMarkdownMin = null,
                    double? bidMaxMarkdownMax = null,
                    double? askSpreadMin = null,
                    double? askSpreadMax = null,
                    double? askMaxDiscountMin = null,
                    double? askMaxDiscountMax = null,
                    double? askMaxMarkupMin = null,
                    double? askMaxMarkupMax = null,
                    string sorting = null,
                    int maxResultCount = int.MaxValue,
                    int skipCount = 0,
                    CancellationToken cancellationToken = default
                );

        Task<long> GetCountAsync(
            string filterText = null,
            Guid? currencyId = null,
            string currencyCode = null,
            SpreadRuleType? type = null,
            double? bidSpreadMin = null,
            double? bidSpreadMax = null,
            double? bidMaxDiscountMin = null,
            double? bidMaxDiscountMax = null,
            double? bidMaxMarkdownMin = null,
            double? bidMaxMarkdownMax = null,
            double? askSpreadMin = null,
            double? askSpreadMax = null,
            double? askMaxDiscountMin = null,
            double? askMaxDiscountMax = null,
            double? askMaxMarkupMin = null,
            double? askMaxMarkupMax = null,
            Guid? spreadRuleId = null,
            CancellationToken cancellationToken = default);
    }
}