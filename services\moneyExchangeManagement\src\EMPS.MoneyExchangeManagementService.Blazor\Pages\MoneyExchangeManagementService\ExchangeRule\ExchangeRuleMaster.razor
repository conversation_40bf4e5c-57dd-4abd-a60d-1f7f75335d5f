@page "/ExchangeRuleMaster/{ExchangeRuleID}"

@attribute [Authorize(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default)]

@using EMPS.MoneyExchangeManagementService.Permissions
@using EMPS.FinancialManagementService.Permissions
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator
@using Volo.Abp;
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using EMPS.Shared.Enum.ExchangeRules
@using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails
@inherits MoneyExchangeManagementServiceComponentBase
@inject IExchangeRuleDetailsAppService ExchangeRuleDetailsAppService

<LoadingIndicator @bind-Visible="@LoadingVisiblity">
    <Tabs SelectedTab=@TabName RenderMode=TabsRenderMode.LazyReload TabPosition=TabPosition.Top FullWidth="true"
          SelectedTabChanged="@((Name) => {TabName =Name; StateHasChanged();})">
        <Items>
            <Tab Name="EditCreditLimitRulesTab"><Icon Name="@("fa fa-edit")" /> @L["EditCreditLimitRules"]</Tab>
            <Tab Name="ExchangeRuleDetailTab"><Icon Name="@("fas fa-details")" /> @L["ExchangeRuleDetailTab"]</Tab>
        </Items>
        <Content>
            <TabPanel Name="EditCreditLimitRulesTab">
                @if (ExchangeRuleID != null)
                {
                    @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default).Result == true)
                    {
                        <EditExchangeRules ExchangeRuleID="@ExchangeRuleID"></EditExchangeRules>
                    }
                }
            </TabPanel>
            <TabPanel Name="ExchangeRuleDetailTab">
                @if (ExchangeRuleID != null)
                {
                    @if (AuthorizationService.IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuless.Default).Result == true)
                    {
                        <ExchangeRuleDetailsMaster ExchangeRuleID="@ExchangeRuleID"></ExchangeRuleDetailsMaster>
                    }
                }
            </TabPanel>
            
        </Content>
    </Tabs>
</LoadingIndicator>
@code {
    [Parameter]
    public string ExchangeRuleID { get; set; }

    public string TabName { get; set; }

    string selectedTab = "EditCreditLimitRulesTab";
    private bool LoadingVisiblity;

    private Task OnSelectedTabChanged(string name)
    {
        LoadingVisiblity = true;
        selectedTab = name;

        LoadingVisiblity = false;
        return Task.CompletedTask;
    }

    protected override async Task OnInitializedAsync()
    {
        LoadingVisiblity = true;

        base.OnInitialized();
        TabName = selectedTab;
        LoadingVisiblity = false;
    }
}
