﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class EditFieldsMaster : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApprovalLimit",
                table: "ExchangeRuleDetails");

            migrationBuilder.DropColumn(
                name: "RoundUpFee",
                table: "ExchangeRuleDetails");

            migrationBuilder.AddColumn<double>(
                name: "ApprovalLimit",
                table: "ExchangeRuless",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "RoundUpFee",
                table: "ExchangeRuless",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApprovalLimit",
                table: "ExchangeRuless");

            migrationBuilder.DropColumn(
                name: "RoundUpFee",
                table: "ExchangeRuless");

            migrationBuilder.AddColumn<double>(
                name: "ApprovalLimit",
                table: "ExchangeRuleDetails",
                type: "float",
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<double>(
                name: "RoundUpFee",
                table: "ExchangeRuleDetails",
                type: "float",
                nullable: false,
                defaultValue: 0.0);
        }
    }
}
