using System;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;

namespace EMPS.MoneyExchangeManagementService.ExchangeRuless
{
    public class ExchangeRulessDataSeedContributor : IDataSeedContributor, ISingletonDependency
    {
        private bool IsSeeded = false;
        private readonly IExchangeRulesRepository _exchangeRulesRepository;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public ExchangeRulessDataSeedContributor(IExchangeRulesRepository exchangeRulesRepository, IUnitOfWorkManager unitOfWorkManager)
        {
            _exchangeRulesRepository = exchangeRulesRepository;
            _unitOfWorkManager = unitOfWorkManager;

        }

        public async Task SeedAsync(DataSeedContext context)
        {
            if (IsSeeded)
            {
                return;
            }

        

            await _unitOfWorkManager.Current.SaveChangesAsync();

            IsSeeded = true;
        }
    }
}