﻿// <auto-generated />
using System;
using EMPS.MoneyExchangeManagementService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    [DbContext(typeof(MoneyExchangeManagementServiceDbContext))]
    [Migration("20250524121115_Added_ExchangeRuleDetail")]
    partial class AddedExchangeRuleDetail
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.ExchangeRuleDetails.ExchangeRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AllowedToBuy")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToBuy");

                    b.Property<bool>("AllowedToSell")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSell");

                    b.Property<bool>("AllowedToSellBelowCenterCost")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSellBelowCenterCost");

                    b.Property<bool>("AllowedToSellBelowCompanyCost")
                        .HasColumnType("bit")
                        .HasColumnName("AllowedToSellBelowCompanyCost");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyID")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyID");

                    b.Property<string>("CurrencyName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyName");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("ExchangeRuleDetailType")
                        .HasColumnType("int")
                        .HasColumnName("ExchangeRuleDetailType");

                    b.Property<string>("ExchangeRuleMasterID")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExchangeRuleMasterID");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<double>("MaxAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MaxAmountToBuy");

                    b.Property<double>("MaxAmountToSell")
                        .HasColumnType("float")
                        .HasColumnName("MaxAmountToSell");

                    b.Property<double>("MaxDailyAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MaxDailyAmountToBuy");

                    b.Property<string>("MaxDailyAmountToSell")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("MaxDailyAmountToSell");

                    b.Property<double>("MinAmountToBuy")
                        .HasColumnType("float")
                        .HasColumnName("MinAmountToBuy");

                    b.Property<double>("MinAmountToSell")
                        .HasColumnType("float")
                        .HasColumnName("MinAmountToSell");

                    b.HasKey("Id");

                    b.ToTable("ExchangeRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.ExchangeRuless.ExchangeRules", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ActivationDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ActivationDate");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<int>("ExchangeRuleScope")
                        .HasColumnType("int")
                        .HasColumnName("ExchangeRuleScope");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Name");

                    b.HasKey("Id");

                    b.ToTable("ExchangeRuless", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRuleDetails.SpreadRuleDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("AskMaxDiscount")
                        .HasColumnType("float")
                        .HasColumnName("AskMaxDiscount");

                    b.Property<double>("AskMaxMarkup")
                        .HasColumnType("float")
                        .HasColumnName("AskMaxMarkup");

                    b.Property<double>("AskSpread")
                        .HasColumnType("float")
                        .HasColumnName("AskSpread");

                    b.Property<double>("BidMaxDiscount")
                        .HasColumnType("float")
                        .HasColumnName("BidMaxDiscount");

                    b.Property<double>("BidMaxMarkdown")
                        .HasColumnType("float")
                        .HasColumnName("BidMaxMarkdown");

                    b.Property<double>("BidSpread")
                        .HasColumnType("float")
                        .HasColumnName("BidSpread");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyCode");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("SpreadRuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("Type");

                    b.HasKey("Id");

                    b.HasIndex("SpreadRuleId");

                    b.ToTable("SpreadRuleDetails", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRules.SpreadRule", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActivationDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ActivationDate");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedByUserId");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByUserName");

                    b.Property<DateTime>("ApprovedDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovedDateTime");

                    b.Property<Guid?>("ArchivedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ArchivedByUserId");

                    b.Property<string>("ArchivedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ArchivedByUserName");

                    b.Property<DateTime>("ArchivedDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ArchivedDateTime");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Description");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("bit")
                        .HasColumnName("IsArchived");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("RuleName");

                    b.Property<int>("Scope")
                        .HasColumnType("int")
                        .HasColumnName("Scope");

                    b.Property<DateTime?>("UnArchivedByDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UnArchivedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UnArchivedByUserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SpreadRules", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeManagementService.SpreadRuleDetails.SpreadRuleDetail", b =>
                {
                    b.HasOne("EMPS.MoneyExchangeManagementService.SpreadRules.SpreadRule", null)
                        .WithMany()
                        .HasForeignKey("SpreadRuleId")
                        .OnDelete(DeleteBehavior.NoAction);
                });
#pragma warning restore 612, 618
        }
    }
}
