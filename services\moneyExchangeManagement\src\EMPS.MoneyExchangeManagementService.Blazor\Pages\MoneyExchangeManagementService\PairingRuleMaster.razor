﻿@attribute [Authorize(MoneyExchangeManagementServicePermissions.PairingRules.Default)]
@using EMPS.MoneyExchangeManagementService.PairingRules
@using EMPS.MoneyExchangeManagementService.Localization
@using EMPS.MoneyExchangeManagementService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeManagementService.Permissions
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService
@inherits MoneyExchangeManagementServiceComponentBase
@inject IPairingRulesAppService PairingRulesAppService
@inject IUiMessageService UiMessageService
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider
@inject NavigationManager NavigationManager

<PageHeader Title="@L["PairingRule"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>
@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Field>
            <FieldLabel>@L["Name"] *</FieldLabel>
            <TextEdit @bind-Text="@PairingRule.Name" Disabled="PairingRule.IsApproved">

            </TextEdit>
        </Field>


        <Field>
            <FieldLabel>@L["EffectiveDate"]</FieldLabel>
            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@PairingRule.EffectiveDate"
                Disabled="PairingRule.IsApproved">

            </DateEdit>
        </Field>

        <Field>
            <FieldLabel>@L["Description"]</FieldLabel>
            <TextEdit @bind-Text="@PairingRule.Description" Multiline="true" Rows="4" Disabled="PairingRule.IsApproved">
            </TextEdit>
        </Field>


    </CardBody>
    <CardFooter>
        <SubmitButton Clicked="UpdatePairingRuleAsync" />
    </CardFooter>
</Card>
