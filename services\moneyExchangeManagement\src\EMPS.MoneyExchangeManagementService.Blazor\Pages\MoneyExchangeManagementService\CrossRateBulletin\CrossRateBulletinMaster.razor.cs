using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EMPS.MoneyExchangeManagementService.Permissions;
using Blazorise;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using Microsoft.AspNetCore.Authorization;
using EMPS.MoneyExchangeManagementService.Localization;
using EMPS.MoneyExchangeManagementService.Application.Contracts.CrossRateBulletin;
using Microsoft.AspNetCore.Components;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.CrossRateBulletin
{
    public partial class CrossRateBulletinMaster
    {
        [Inject] protected ICrossRateProviderConfigurationAppService ConfigurationAppService { get; set; }

        protected PageToolbar Toolbar { get; set; } = new();
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new();
        protected Modal DetailsModal { get; set; } = new();
        protected CrossRateBulletinDetail crossRateBulletinDetailRef { get; set; }
        protected CrossRateBulletinMasterDto SelectedCrossRateBulletinMaster { get; set; }

        // Configuration properties
        protected CrossRateProviderConfigurationDto Configuration { get; set; } = new();
        protected CreateUpdateCrossRateProviderConfigurationDto ConfigurationInput { get; set; } = new();
        protected bool IsConfigurationLoading { get; set; } = false;
        protected bool IsConfigurationSaving { get; set; } = false;

        protected bool CanApprove;
        protected bool CanArchive;
        protected bool CanCreate;
        protected bool CanDelete;
        protected bool CanManageConfiguration;

        public CrossRateBulletinMaster()
        {
            GetListInput.IsArchived = false;
            LocalizationResource = typeof(MoneyExchangeManagementServiceResource);

        }

        protected override async Task OnInitializedAsync()
        {
            await SetBreadcrumbItemsAsync();
            await SetToolbarItemsAsync();
            await SetPermissionsAsync();
            await LoadConfigurationAsync();
            await base.OnInitializedAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Clear();
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CrossRateBulletinMaster"]));
            return ValueTask.CompletedTask;
        }
        private async Task SetUpCreateModalAndOpen()
        {
            await OpenCreateModalAsync();
            NewEntity.Name = DateTime.Now.ToShortDateString();
        }
        protected virtual ValueTask SetToolbarItemsAsync()
        {

            Toolbar.AddButton(
                L["CreateNew"],
                async () => { await SetUpCreateModalAndOpen(); },
                icon: IconName.Add,
                requiredPolicyName: MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create
            );
            return ValueTask.CompletedTask;
        }

        protected async Task SetPermissionsAsync()
        {
            CanCreate = await AuthorizationService.IsGrantedAnyAsync(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Create);
            CanApprove = await AuthorizationService.IsGrantedAnyAsync(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Approve);
            CanArchive = await AuthorizationService.IsGrantedAnyAsync(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Archive);
            CanDelete = await AuthorizationService.IsGrantedAnyAsync(MoneyExchangeManagementServicePermissions.CrossRateBulletinMaster.Delete);
            CanManageConfiguration = await AuthorizationService.IsGrantedAnyAsync(MoneyExchangeManagementServicePermissions.CrossRateProviderConfiguration.Manage);
        }

        protected async Task ApproveAsync(Guid id)
        {
            await AppService.ApproveAsync(id);
            await Notify.Success(L["ApprovedSuccessfully"]);
            await GetEntitiesAsync();

        }
        protected async Task ArchiveAsync(Guid id)
        {
            await AppService.ArchiveAsync(id);
            await Notify.Success(L["ArchivedSuccessfully"]);

            await GetEntitiesAsync();
        }
        protected async Task UnarchiveAsync(Guid id)
        {
            await AppService.UnarchiveAsync(id);
            await Notify.Success(L["UnArchivedSuccessfully"]);
            await GetEntitiesAsync();
        }

        protected async Task OpenDetailsModalAsync(CrossRateBulletinMasterDto crossRateBulletinMaster)
        {
            SelectedCrossRateBulletinMaster = crossRateBulletinMaster;
            if (crossRateBulletinDetailRef != null)
            {

                await crossRateBulletinDetailRef.RefreshDetails(crossRateBulletinMaster.Id);
            }
            await DetailsModal.Show();
        }

        // Configuration methods
        protected async Task LoadConfigurationAsync()
        {
            if (!CanManageConfiguration) return;

            try
            {
                IsConfigurationLoading = true;
                var config = await ConfigurationAppService.GetAsync();
                if (config != null)
                {
                    Configuration = config;
                    ConfigurationInput.ProviderBaseUrl = config.ProviderBaseUrl;
                    ConfigurationInput.ProviderAccessToken = config.ProviderAccessToken;
                    ConfigurationInput.RequestBaseCurrency = config.RequestBaseCurrency;
                }
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
            finally
            {
                IsConfigurationLoading = false;
            }
        }

        protected async Task SaveConfigurationAsync()
        {
            if (!CanManageConfiguration) return;

            try
            {
                IsConfigurationSaving = true;
                var result = await ConfigurationAppService.CreateOrUpdateAsync(ConfigurationInput);
                Configuration = result;
                await Notify.Success(L["ConfigurationSavedSuccessfully"]);
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
            finally
            {
                IsConfigurationSaving = false;
            }
        }
    }
}