namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin.CrossRateProvider
{
    /// <summary>
    /// Response data from cross limit providers
    /// </summary>
    public class CrossLimitProviderResponse
    {
        /// <summary>
        /// Source/Base currency code (e.g., USD, EUR)
        /// </summary>
        public string SourceCurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// Quote currency code (e.g., JPY, GBP)
        /// </summary>
        public string QuoteCurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// Cross rate value between the source and quote currencies
        /// </summary>
        public double CrossRateValue { get; set; }
    }
}
