using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.ExchangeRuleDetails;
using EMPS.MoneyExchangeManagementService.ExchangeRuless;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.Shared.Enum.ExchangeRules;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService.ExchangeRule
{
    public partial class ExchangeRuleDetails
    {
        [Parameter]
        public string ExchangeRuleID { get; set; }
        [Parameter]
        public ExchangeRuleDetailsType exchangeRuleDetailsType { get; set; }
        public ExchangeRulesDto ExchangeRule { get; private set; } = new ExchangeRulesDto();
        public string ExchangeRuleName { get; set; }
        
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar {get; set;} = new PageToolbar();
        private IReadOnlyList<ExchangeRuleDetailDto> ExchangeRuleDetailList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreateExchangeRuleDetail { get; set; }
        private bool CanEditExchangeRuleDetail { get; set; }
        private bool CanDeleteExchangeRuleDetail { get; set; }
        private bool CanApproveExchangeRuleDetail { get; set; }
        private bool CanArchiveExchangeRuleDetail { get; set; }
        private bool CanUnArchiveExchangeRuleDetail { get; set; }
        private bool CanDuplicateExchangeRuleDetail { get; set; }
        private ExchangeRuleDetailCreateDto NewExchangeRuleDetail { get; set; }
        private Validations NewExchangeRuleDetailValidations { get; set; } = new();
        private ExchangeRuleDetailUpdateDto EditingExchangeRuleDetail { get; set; }
        private Validations EditingExchangeRuleDetailValidations { get; set; } = new();
        private Guid EditingExchangeRuleDetailId { get; set; }
        private Modal CreateExchangeRuleDetailModal { get; set; } = new();
        private Modal EditExchangeRuleDetailModal { get; set; } = new();
        private GetExchangeRuleDetailsInput Filter { get; set; }
        private DataGridEntityActionsColumn<ExchangeRuleDetailDto> EntityActionsColumn { get; set; } = new();
        
        public ExchangeRuleDetails()
        {
            NewExchangeRuleDetail = new ExchangeRuleDetailCreateDto();
            EditingExchangeRuleDetail = new ExchangeRuleDetailUpdateDto();
            Filter = new GetExchangeRuleDetailsInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            ExchangeRuleDetailList = new List<ExchangeRuleDetailDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
            await GetExchangeRuleNameAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ExchangeRuleDetails"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            //Toolbar.AddButton(L["NewExchangeRuleDetail"], async () =>
            //{
            //    await OpenCreateExchangeRuleDetailModalAsync();
            //}, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Create);

            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateExchangeRuleDetail = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Create);
            CanEditExchangeRuleDetail = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Edit);
            CanDeleteExchangeRuleDetail = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeManagementServicePermissions.ExchangeRuleDetails.Delete);
        }

        private async Task GetExchangeRuleNameAsync()
        {
            if (!string.IsNullOrEmpty(ExchangeRuleID))
            {
                 ExchangeRule = await ExchangeRulessAppService.GetAsync(Guid.Parse(ExchangeRuleID));
                 ExchangeRuleName = ExchangeRule.Name;
            }
        }

        private async Task GetExchangeRuleDetailsAsync()
        {
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;
            Filter.ExchangeRuleMasterID = Guid.Parse(ExchangeRuleID);
            Filter.ExchangeRuleDetailType = exchangeRuleDetailsType ;

            var result = await ExchangeRuleDetailsAppService.GetListAsync(Filter);
            ExchangeRuleDetailList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetExchangeRuleDetailsAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<ExchangeRuleDetailDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetExchangeRuleDetailsAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreateExchangeRuleDetailModalAsync()
        {
            NewExchangeRuleDetail = new ExchangeRuleDetailCreateDto
            {
                ExchangeRuleMasterID = Guid.Parse(ExchangeRuleID),
                ExchangeRuleDetailType = ExchangeRuleDetailsType.InCash
            };
            
            await NewExchangeRuleDetailValidations.ClearAll();
            await CreateExchangeRuleDetailModal.Show();
        }

        private async Task CloseCreateExchangeRuleDetailModalAsync()
        {
            NewExchangeRuleDetail = new ExchangeRuleDetailCreateDto();
            await CreateExchangeRuleDetailModal.Hide();
        }

        private async Task OpenEditExchangeRuleDetailModalAsync(ExchangeRuleDetailDto input)
        {
            var exchangeRuleDetail = await ExchangeRuleDetailsAppService.GetAsync(input.Id);
            
            EditingExchangeRuleDetailId = exchangeRuleDetail.Id;
            EditingExchangeRuleDetail = ObjectMapper.Map<ExchangeRuleDetailDto, ExchangeRuleDetailUpdateDto>(exchangeRuleDetail);
            await EditingExchangeRuleDetailValidations.ClearAll();
            await EditExchangeRuleDetailModal.Show();
        }

        private async Task DeleteExchangeRuleDetailAsync(ExchangeRuleDetailDto input)
        {
            await ExchangeRuleDetailsAppService.DeleteAsync(input.Id);
            await GetExchangeRuleDetailsAsync();
        }

        private async Task CreateExchangeRuleDetailAsync()
        {
            try
            {
                var validate = true;
                if (validate)
                {
                    await ExchangeRuleDetailsAppService.CreateAsync(NewExchangeRuleDetail);
                    await GetExchangeRuleDetailsAsync();
                    await CloseCreateExchangeRuleDetailModalAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditExchangeRuleDetailModalAsync()
        {
            await EditExchangeRuleDetailModal.Hide();
        }

        private async Task UpdateExchangeRuleDetailAsync()
        {
            try
            {
                var validate = true;
                if (validate)
                {
                    await ExchangeRuleDetailsAppService.UpdateAsync(EditingExchangeRuleDetailId, EditingExchangeRuleDetail);
                    await GetExchangeRuleDetailsAsync();
                    await CloseEditExchangeRuleDetailModalAsync();
                }
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }
    }
}
