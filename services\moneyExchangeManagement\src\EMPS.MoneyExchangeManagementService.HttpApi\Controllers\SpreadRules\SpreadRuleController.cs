using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using Volo.Abp.Content;
using EMPS.MoneyExchangeManagementService.Shared;
using System.Collections.Generic;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using EMPS.Shared.Enum;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    [RemoteService(Name = "MoneyExchangeManagementService")]
    [Area("MoneyExchangeManagementService")]
    [ControllerName("SpreadRule")]
    [Route("api/money-exchange-management-service/spread-rules")]
    public class SpreadRuleController : AbpController, ISpreadRulesAppService
    {
        private readonly ISpreadRulesAppService _spreadRulesAppService;

        public SpreadRuleController(ISpreadRulesAppService spreadRulesAppService)
        {
            _spreadRulesAppService = spreadRulesAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<SpreadRuleDto>> GetListAsync(GetSpreadRulesInput input)
        {
            return _spreadRulesAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<SpreadRuleDto> GetAsync(Guid id)
        {
            return _spreadRulesAppService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<SpreadRuleDto> CreateAsync(SpreadRuleCreateDto input)
        {
            return _spreadRulesAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<SpreadRuleDto> UpdateAsync(Guid id, SpreadRuleUpdateDto input)
        {
            return _spreadRulesAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _spreadRulesAppService.DeleteAsync(id);
        }

        [HttpGet]
        [Route("as-excel-file")]
        public virtual Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleExcelDownloadDto input)
        {
            return _spreadRulesAppService.GetListAsExcelFileAsync(input);
        }

        [HttpGet]
        [Route("download-token")]
        public Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            return _spreadRulesAppService.GetDownloadTokenAsync();
        }
        [HttpGet]
        [Route("ApproveAsync")]
        public Task<SpreadRuleDto> ApproveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            return _spreadRulesAppService.ApproveAsync(id, input);
        }
        [HttpGet]
        [Route("ArchiveAsync")]
        public Task<SpreadRuleDto> ArchiveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            return _spreadRulesAppService.ArchiveAsync(id, input);
        }
        [HttpGet]
        [Route("UnArchiveAsync")]
        public Task<SpreadRuleDto> UnArchiveAsync(Guid id, SpreadRuleUpdateDto input)
        {
            return _spreadRulesAppService.UnArchiveAsync(id, input);
        }
        [HttpGet]
        [Route("CreateDuplicateAsync")]
        public Task<SpreadRuleDto> CreateDuplicateAsync(SpreadRuleDto input)
        {
            return _spreadRulesAppService.CreateDuplicateAsync(input);
        }
        [HttpGet]
        [Route("GetSpreadRulesWithDetail")]
        public Task<List<SpreadRuleDetailWithNavigationPropertiesDto>> GetSpreadRulesWithDetail(bool isGlobal, List<Guid?> spreadRuleIds)
        {
            return _spreadRulesAppService.GetSpreadRulesWithDetail(isGlobal, spreadRuleIds);
        }
        [HttpGet("GetWithDetailsAsync")]
        public Task<SpreadRuleDto> GetWithDetailsAsync(Guid id, SpreadRuleType type)
        {
            return _spreadRulesAppService.GetWithDetailsAsync(id, type);
        }
    }
}