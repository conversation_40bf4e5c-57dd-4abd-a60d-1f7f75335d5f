using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.MoneyExchangeManagementService.SpreadRules;
using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;
using MiniExcelLibs;
using Volo.Abp.Content;
using Volo.Abp.Authorization;
using Volo.Abp.Caching;
using Microsoft.Extensions.Caching.Distributed;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{

    [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Default)]
    public class SpreadRuleDetailsAppService : MoneyExchangeManagementServiceAppService, ISpreadRuleDetailsAppService
    {
        private readonly IDistributedCache<SpreadRuleDetailExcelDownloadTokenCacheItem, string> _excelDownloadTokenCache;
        private readonly ISpreadRuleDetailRepository _spreadRuleDetailRepository;
        private readonly SpreadRuleDetailManager _spreadRuleDetailManager;
        private readonly IRepository<SpreadRule, Guid> _spreadRuleRepository;

        public SpreadRuleDetailsAppService(ISpreadRuleDetailRepository spreadRuleDetailRepository, SpreadRuleDetailManager spreadRuleDetailManager, IDistributedCache<SpreadRuleDetailExcelDownloadTokenCacheItem, string> excelDownloadTokenCache, IRepository<SpreadRule, Guid> spreadRuleRepository)
        {
            _excelDownloadTokenCache = excelDownloadTokenCache;
            _spreadRuleDetailRepository = spreadRuleDetailRepository;
            _spreadRuleDetailManager = spreadRuleDetailManager; _spreadRuleRepository = spreadRuleRepository;
        }

        public virtual async Task<PagedResultDto<SpreadRuleDetailWithNavigationPropertiesDto>> GetListAsync(GetSpreadRuleDetailsInput input)
        {
            var totalCount = await _spreadRuleDetailRepository.GetCountAsync(input.FilterText, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpreadMin, input.BidSpreadMax, input.BidMaxDiscountMin, input.BidMaxDiscountMax, input.BidMaxMarkdownMin, input.BidMaxMarkdownMax, input.AskSpreadMin, input.AskSpreadMax, input.AskMaxDiscountMin, input.AskMaxDiscountMax, input.AskMaxMarkupMin, input.AskMaxMarkupMax, input.SpreadRuleId);
            var items = await _spreadRuleDetailRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpreadMin, input.BidSpreadMax, input.BidMaxDiscountMin, input.BidMaxDiscountMax, input.BidMaxMarkdownMin, input.BidMaxMarkdownMax, input.AskSpreadMin, input.AskSpreadMax, input.AskMaxDiscountMin, input.AskMaxDiscountMax, input.AskMaxMarkupMin, input.AskMaxMarkupMax, input.SpreadRuleId, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<SpreadRuleDetailWithNavigationPropertiesDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<SpreadRuleDetailWithNavigationProperties>, List<SpreadRuleDetailWithNavigationPropertiesDto>>(items)
            };
        }

        public virtual async Task<SpreadRuleDetailWithNavigationPropertiesDto> GetWithNavigationPropertiesAsync(Guid id)
        {
            return ObjectMapper.Map<SpreadRuleDetailWithNavigationProperties, SpreadRuleDetailWithNavigationPropertiesDto>
                (await _spreadRuleDetailRepository.GetWithNavigationPropertiesAsync(id));
        }

        public virtual async Task<SpreadRuleDetailDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<SpreadRuleDetail, SpreadRuleDetailDto>(await _spreadRuleDetailRepository.GetAsync(id));
        }

        public virtual async Task<PagedResultDto<LookupDto<Guid>>> GetSpreadRuleLookupAsync(LookupRequestDto input)
        {
            var query = (await _spreadRuleRepository.GetQueryableAsync())
                .WhereIf(!string.IsNullOrWhiteSpace(input.Filter),
                    x => x.RuleName != null &&
                         x.RuleName.Contains(input.Filter));

            var lookupData = await query.PageBy(input.SkipCount, input.MaxResultCount).ToDynamicListAsync<SpreadRule>();
            var totalCount = query.Count();
            return new PagedResultDto<LookupDto<Guid>>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<SpreadRule>, List<LookupDto<Guid>>>(lookupData)
            };
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _spreadRuleDetailRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Create)]
        public virtual async Task<SpreadRuleDetailDto> CreateAsync(SpreadRuleDetailCreateDto input)
        {

            var spreadRuleDetail = await _spreadRuleDetailManager.CreateAsync(
            input.SpreadRuleId, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpread, input.BidMaxDiscount, input.BidMaxMarkdown, input.AskSpread, input.AskMaxDiscount, input.AskMaxMarkup
            );

            return ObjectMapper.Map<SpreadRuleDetail, SpreadRuleDetailDto>(spreadRuleDetail);
        }

        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Edit)]
        public virtual async Task<SpreadRuleDetailDto> UpdateAsync(Guid id, SpreadRuleDetailUpdateDto input)
        {

            var spreadRuleDetail = await _spreadRuleDetailManager.UpdateAsync(
            id,
            input.SpreadRuleId, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpread, input.BidMaxDiscount, input.BidMaxMarkdown, input.AskSpread, input.AskMaxDiscount, input.AskMaxMarkup, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<SpreadRuleDetail, SpreadRuleDetailDto>(spreadRuleDetail);
        }

        [AllowAnonymous]
        public virtual async Task<IRemoteStreamContent> GetListAsExcelFileAsync(SpreadRuleDetailExcelDownloadDto input)
        {
            var downloadToken = await _excelDownloadTokenCache.GetAsync(input.DownloadToken);
            if (downloadToken == null || input.DownloadToken != downloadToken.Token)
            {
                throw new AbpAuthorizationException("Invalid download token: " + input.DownloadToken);
            }

            var spreadRuleDetails = await _spreadRuleDetailRepository.GetListWithNavigationPropertiesAsync(input.FilterText, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpreadMin, input.BidSpreadMax, input.BidMaxDiscountMin, input.BidMaxDiscountMax, input.BidMaxMarkdownMin, input.BidMaxMarkdownMax, input.AskSpreadMin, input.AskSpreadMax, input.AskMaxDiscountMin, input.AskMaxDiscountMax, input.AskMaxMarkupMin, input.AskMaxMarkupMax);
            var items = spreadRuleDetails.Select(item => new
            {
                CurrencyId = item.SpreadRuleDetail.CurrencyId,
                CurrencyCode = item.SpreadRuleDetail.CurrencyCode,
                Type = item.SpreadRuleDetail.Type,
                BidSpread = item.SpreadRuleDetail.BidSpread,
                BidMaxDiscount = item.SpreadRuleDetail.BidMaxDiscount,
                BidMaxMarkdown = item.SpreadRuleDetail.BidMaxMarkdown,
                AskSpread = item.SpreadRuleDetail.AskSpread,
                AskMaxDiscount = item.SpreadRuleDetail.AskMaxDiscount,
                AskMaxMarkup = item.SpreadRuleDetail.AskMaxMarkup,

                SpreadRule = item.SpreadRule?.RuleName,

            });

            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsAsync(items);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new RemoteStreamContent(memoryStream, "SpreadRuleDetails.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        public async Task<DownloadTokenResultDto> GetDownloadTokenAsync()
        {
            var token = Guid.NewGuid().ToString("N");

            await _excelDownloadTokenCache.SetAsync(
                token,
                new SpreadRuleDetailExcelDownloadTokenCacheItem { Token = token },
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(30)
                });

            return new DownloadTokenResultDto
            {
                Token = token
            };
        }
        [Authorize(MoneyExchangeManagementServicePermissions.SpreadRuleDetails.Edit)]

        public async Task<SpreadRuleDto> UpdateSpreadRuleRowAsync(Guid spreadRuleDetailId, SpreadRuleDetailUpdateDto input)
        {
            var spreadRule= await _spreadRuleRepository.GetAsync((Guid)input.SpreadRuleId);
            if (spreadRule.IsApproved)
                throw new UserFriendlyException(L["ThisRuleIsApproved"]);

            var spreadRuleDetail = await _spreadRuleDetailManager.UpdateAsync(
            spreadRuleDetailId,
            input.SpreadRuleId, input.CurrencyId, input.CurrencyCode, input.Type, input.BidSpread, input.BidMaxDiscount, input.BidMaxMarkdown, input.AskSpread, input.AskMaxDiscount, input.AskMaxMarkup, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<SpreadRule, SpreadRuleDto>(spreadRule);
        }
    }
}