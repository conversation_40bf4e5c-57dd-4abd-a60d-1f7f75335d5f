using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public interface ISpreadRuleRepository : IRepository<SpreadRule, Guid>
    {
        Task<SpreadRule> GetWithDetailsAsync(Guid id, SpreadRuleType type);
        Task<List<SpreadRule>?> GetSpreadRulesByGlobalAndIdsAsync(bool isGlobal, List<Guid>? spreadRuleIds);
        Task<List<SpreadRule>> GetListAsync(
            string filterText = null,
            string ruleName = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            SpreadRuleScope? scope = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string ruleName = null,
            DateTime? activationDateMin = null,
            DateTime? activationDateMax = null,
            SpreadRuleScope? scope = null,
            string description = null,
            bool? isApproved = null,
            Guid? approvedByUserId = null,
            string approvedByUserName = null,
            DateTime? approvedDateTimeMin = null,
            DateTime? approvedDateTimeMax = null,
            bool? isArchived = null,
            Guid? archivedByUserId = null,
            string archivedByUserName = null,
            DateTime? archivedDateTimeMin = null,
            DateTime? archivedDateTimeMax = null,
            CancellationToken cancellationToken = default);
    }
}