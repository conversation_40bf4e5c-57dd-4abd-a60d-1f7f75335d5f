namespace EMPS.MoneyExchangeManagementService.Blazor.Menus;

public class MoneyExchangeManagementServiceMenus
{
    public const string Prefix = "MoneyExchangeManagementService";

    public const string BulletinManagementMasters = Prefix + ".BulletinManagementMasters";

    public const string BulletinManagementDetails = Prefix + ".BulletinManagementDetails";

    public const string BulletinManagement = Prefix + ".BulletinManagement";



    public const string CrossRateBulletinMaster = Prefix + ".CrossRateBulletinMaster";
    public const string CustomModalDemo = Prefix + ".CustomModalDemo";


    public const string PairingRules = Prefix + ".PairingRules";

    public const string PairingRuleDetails = Prefix + ".PairingRuleDetails";

    public const string SpreadRules = Prefix + ".SpreadRules";

    public const string SpreadRulesDetails = Prefix + ".SpreadRulesDetails";

    public const string ExchangeRuless = Prefix + ".ExchangeRuless";


}