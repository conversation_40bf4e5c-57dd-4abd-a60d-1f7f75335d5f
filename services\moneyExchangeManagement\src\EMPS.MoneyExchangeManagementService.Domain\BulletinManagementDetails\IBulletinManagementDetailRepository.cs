using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeManagementService.BulletinManagementDetails
{
    public interface IBulletinManagementDetailRepository : IRepository<BulletinManagementDetail, Guid>
    {
        Task<BulletinManagementDetailWithNavigationProperties> GetWithNavigationPropertiesAsync(
    Guid id,
    CancellationToken cancellationToken = default
);

        Task<List<BulletinManagementDetailWithNavigationProperties>> GetListWithNavigationPropertiesAsync(
            string filterText = null,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? bulletinManagementMasterId = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<List<BulletinManagementDetail>> GetListAsync(
                    string filterText = null,
                    string currencyPair = null,
                    double? cashBidMin = null,
                    double? cashBidMax = null,
                    double? cashAskMin = null,
                    double? cashAskMax = null,
                    double? accountBidMin = null,
                    double? accountBidMax = null,
                    double? accountAskMin = null,
                    double? accountAskMax = null,
                    int? displayOrderMin = null,
                    int? displayOrderMax = null,
                    string sorting = null,
                    int maxResultCount = int.MaxValue,
                    int skipCount = 0,
                    CancellationToken cancellationToken = default
                );

        Task<long> GetCountAsync(
            string filterText = null,
            string currencyPair = null,
            double? cashBidMin = null,
            double? cashBidMax = null,
            double? cashAskMin = null,
            double? cashAskMax = null,
            double? accountBidMin = null,
            double? accountBidMax = null,
            double? accountAskMin = null,
            double? accountAskMax = null,
            int? displayOrderMin = null,
            int? displayOrderMax = null,
            Guid? bulletinManagementMasterId = null,
            CancellationToken cancellationToken = default);
    }
}