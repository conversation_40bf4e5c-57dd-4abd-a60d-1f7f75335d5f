﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeManagementService.Migrations
{
    /// <inheritdoc />
    public partial class AddedBulletinManagementMaster : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BulletinManagementMasters",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BulletinNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BulletinName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BulletinDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ServicePointName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ServicePointId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CurrencyPairingRuleName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CurrencyPairingRuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CrossRateBulletinName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CrossRateBulletinId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SpreadRuleName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SpreadRuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BulletinManagementMasters", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BulletinManagementDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CurrencyPair = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CashBid = table.Column<double>(type: "float", nullable: false),
                    CashAsk = table.Column<double>(type: "float", nullable: false),
                    AccountBid = table.Column<double>(type: "float", nullable: false),
                    AccountAsk = table.Column<double>(type: "float", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    BulletinManagementMasterId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BulletinManagementDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BulletinManagementDetails_BulletinManagementMasters_BulletinManagementMasterId",
                        column: x => x.BulletinManagementMasterId,
                        principalTable: "BulletinManagementMasters",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_BulletinManagementDetails_BulletinManagementMasterId",
                table: "BulletinManagementDetails",
                column: "BulletinManagementMasterId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BulletinManagementDetails");

            migrationBuilder.DropTable(
                name: "BulletinManagementMasters");
        }
    }
}
