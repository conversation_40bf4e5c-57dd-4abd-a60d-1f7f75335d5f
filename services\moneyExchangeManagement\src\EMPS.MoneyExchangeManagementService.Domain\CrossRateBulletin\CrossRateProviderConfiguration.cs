using System;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;

namespace EMPS.MoneyExchangeManagementService.CrossRateBulletin
{
    /// <summary>
    /// Configuration entity for external cross rate providers
    /// This is a singleton entity - only one record should exist in the database
    /// </summary>
    public class CrossRateProviderConfiguration : FullAuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// The API endpoint URL for the external provider
        /// </summary>
        public string ProviderBaseUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Authentication token for the provider API
        /// </summary>
        public string ProviderAccessToken { get; set; } = string.Empty;
        
        /// <summary>
        /// The base currency to use in provider requests
        /// </summary>
        public string RequestBaseCurrency { get; set; } = string.Empty;
    }
}
