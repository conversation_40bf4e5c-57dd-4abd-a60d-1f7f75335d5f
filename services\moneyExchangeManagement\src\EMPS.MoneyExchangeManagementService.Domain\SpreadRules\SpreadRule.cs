using EMPS.Shared.Enum;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;
using EMPS.MoneyExchangeManagementService.SpreadRuleDetails;

namespace EMPS.MoneyExchangeManagementService.SpreadRules
{
    public class SpreadRule : FullAuditedAggregateRoot<Guid>
    {
        [NotNull]
        public virtual string RuleName { get; set; }

        public virtual DateTime? ActivationDate { get; set; }

        public virtual SpreadRuleScope Scope { get; set; }

        [CanBeNull]
        public virtual string? Description { get; set; }

        public virtual bool IsApproved { get; set; }

        [CanBeNull]
        public virtual Guid? ApprovedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ApprovedByUserName { get; set; }

        public virtual DateTime? ApprovedDateTime { get; set; }

        public virtual bool IsArchived { get; set; }

        [CanBeNull]
        public virtual Guid? ArchivedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ArchivedByUserName { get; set; }

        public virtual DateTime? ArchivedDateTime { get; set; }
        public virtual Guid? UnArchivedByUserId { get; set; }
        public virtual string? UnArchivedByUserName { get; set; }
        public virtual DateTime? UnArchivedByDate { get; set; }
        public List<SpreadRuleDetail>SpreadRuleDetails { get; set; }   

        public SpreadRule()
        {

        }


        public SpreadRule(Guid id, string ruleName, string? description, SpreadRuleScope scope, DateTime? activationDate, 
            bool isApproved, Guid? approvedByUserId, string? approvedByUserName, DateTime? approvedDateTime,
            bool isArchived, Guid? archivedByUserId, string? archivedByUserName, Guid? unArchivedByUserId, 
            string? unArchivedByUserName, DateTime? archivedDateTime, DateTime? unArchivedByDate) : base(id)
        {
            Id = id;
            Check.NotNull(ruleName, nameof(ruleName));
            RuleName = ruleName;
            Scope = scope;
            Description = description;
            IsApproved = isApproved;
            ApprovedByUserId = approvedByUserId;
            ApprovedByUserName = approvedByUserName;
            ApprovedDateTime = approvedDateTime;
            IsArchived = isArchived;
            ArchivedByUserId = archivedByUserId;
            ArchivedByUserName = archivedByUserName;
            ArchivedDateTime = archivedDateTime;
            ActivationDate = activationDate;
            UnArchivedByUserId = unArchivedByUserId;
            UnArchivedByUserName = unArchivedByUserName;
            UnArchivedByDate = archivedDateTime;

        }
    }
}