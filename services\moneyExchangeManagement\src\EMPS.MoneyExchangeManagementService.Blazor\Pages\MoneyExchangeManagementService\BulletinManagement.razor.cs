using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeManagementService.BulletinManagementMasters;
using EMPS.MoneyExchangeManagementService.BulletinManagementDetails;
using EMPS.MoneyExchangeManagementService.Permissions;
using EMPS.MoneyExchangeManagementService.Shared;
using EMPS.CompanyService.ServicePoints;
using Blazorise.LoadingIndicator;

namespace EMPS.MoneyExchangeManagementService.Blazor.Pages.MoneyExchangeManagementService
{
    public partial class BulletinManagement
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }

        // Current bulletin date
        private DateTime CurrentBulletinDate { get; set; } = DateTime.Now;

        // Service points
        private List<ServicePointDto> ServicePoints { get; set; } = new List<ServicePointDto>();
        private List<ServicePointDto> FilteredServicePoints { get; set; } = new List<ServicePointDto>();
        private List<Guid> SelectedServicePointIds { get; set; } = new List<Guid>();
        private string ServicePointFilter { get; set; } = string.Empty;
        private GetBulletinManagementMastersInput Filter { get; set; }

        // Bulletin details
        private List<BulletinManagementDetailDto> BulletinDetails { get; set; } = new List<BulletinManagementDetailDto>();

        // Modal
        private Modal CreateBulletinModal { get; set; } = new();
        private BulletinManagementMasterCreateDto NewBulletin { get; set; } = new();

        // Show Detail Modal
        private Modal ShowDetailModal { get; set; } = new();
        private Tabs DetailTabs { get; set; } = new();
        private string SelectedDetailTab { get; set; } = "master";
        private BulletinManagementMasterDto SelectedBulletinMaster { get; set; }
        private List<BulletinManagementDetailDto> SelectedBulletinDetails { get; set; } = new();

        // Global bulletin flags
        private bool IsGlobalBulletin { get; set; } = false; // For preview (left side)
        private bool IsGlobalBulletinSelected { get; set; } = false; // For wholesale selection

        // Preview state tracking (separate from selection)
        private Guid? CurrentPreviewServicePointId { get; set; } = null;
        private string CurrentPreviewContext { get; set; } = string.Empty;

        // Select all flag
        private bool IsAllServicePointsSelected { get; set; } = false;
        public LoadingIndicator loadingIndicator;

        private IReadOnlyList<BulletinManagementMasterDto> BulletinManagementMasterList { get; set; }
        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<BulletinManagementMasterDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetBulletinManagementMastersAsync();
            await InvokeAsync(StateHasChanged);
        }
        private async Task GetBulletinManagementMastersAsync()
        {
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;

            var result = await BulletinManagementMastersAppService.GetListAsync(Filter);
            BulletinManagementMasterList = result.Items;
            TotalCount = (int)result.TotalCount;
        }
        private DataGridEntityActionsColumn<BulletinManagementMasterDto> EntityActionsColumn { get; set; } = new();

        public BulletinManagement()
        {
            BulletinManagementMasterList = new List<BulletinManagementMasterDto>();
            Filter = new GetBulletinManagementMastersInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            NewBulletin = new BulletinManagementMasterCreateDto
            {
                BulletinDate = DateTime.Now,
                PublishDate = DateTime.Now
            };
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:BulletinManagement"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            Toolbar.AddButton(L["NewBulletin"], async () =>
            {
                await OpenCreateBulletinModalAsync();
            }, IconName.Add, requiredPolicyName: MoneyExchangeManagementServicePermissions.BulletinManagementMasters.Preview);

            return ValueTask.CompletedTask;
        }

        private async Task LoadServicePointsAsync()
        {
            try
            {
                ServicePoints = await BulletinManagementMastersAppService.GetAllServicePointHaveSpreadRuleId();
                FilteredServicePoints = ServicePoints.ToList();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                await UiMessageService.Error(ex.Message);
            }
        }

        private async Task OnFilterTextChanged(string value)
        {
            ServicePointFilter = value;
            await FilterServicePoints();
        }

        private async Task FilterServicePoints()
        {
            if (string.IsNullOrWhiteSpace(ServicePointFilter))
            {
                FilteredServicePoints = ServicePoints.ToList();
            }
            else
            {
                FilteredServicePoints = ServicePoints
                    .Where(sp => sp.Name.Contains(ServicePointFilter, StringComparison.OrdinalIgnoreCase))
                    .ToList();
            }
            await InvokeAsync(StateHasChanged);
        }

        private bool IsServicePointSelected(ServicePointDto servicePoint)
        {
            return SelectedServicePointIds.Contains(servicePoint.Id);
        }

        // Helper methods for preview state
        private bool IsServicePointCurrentlyPreviewing(ServicePointDto servicePoint)
        {
            return !IsGlobalBulletin && CurrentPreviewServicePointId.HasValue &&
                   CurrentPreviewServicePointId.Value == servicePoint.Id;
        }

        private bool IsGlobalBulletinCurrentlyPreviewing()
        {
            return IsGlobalBulletin && CurrentPreviewServicePointId == null;
        }

        // Method for clicking on service point (changes preview, doesn't affect selection)
        private async Task OnServicePointClicked(ServicePointDto servicePoint)
        {
            IsGlobalBulletin = false;
            CurrentPreviewServicePointId = servicePoint.Id;
            CurrentPreviewContext = servicePoint.Name;
            await LoadBulletinDetailsAsync(servicePoint.Id);
        }

        // Method for checkbox selection (affects wholesale selection only)
        private async Task OnServicePointSelectionChanged(ServicePointDto servicePoint, bool isSelected)
        {
            if (isSelected && !SelectedServicePointIds.Contains(servicePoint.Id))
            {
                SelectedServicePointIds.Add(servicePoint.Id);
            }
            else if (!isSelected && SelectedServicePointIds.Contains(servicePoint.Id))
            {
                SelectedServicePointIds.Remove(servicePoint.Id);
            }

            // Update the select all checkbox state
            UpdateSelectAllState();
            await InvokeAsync(StateHasChanged);
        }

        // Method for select all checkbox
        private async Task OnSelectAllServicePointsChanged(bool isSelected)
        {
            if (isSelected)
            {
                SelectedServicePointIds = ServicePoints.Select(sp => sp.Id).ToList();
            }
            else
            {
                SelectedServicePointIds.Clear();
            }

            IsAllServicePointsSelected = isSelected;
            await InvokeAsync(StateHasChanged);
        }

        // Helper method to update select all state
        private void UpdateSelectAllState()
        {
            IsAllServicePointsSelected = ServicePoints.Count > 0 &&
                                       SelectedServicePointIds.Count == ServicePoints.Count;
        }

        // Method for clicking Global Bulletin button (changes preview)
        private async Task OnGlobalBulletinClicked()
        {
            IsGlobalBulletin = true;
            CurrentPreviewServicePointId = null;
            CurrentPreviewContext = L["GlobalBulletin"];
            await LoadBulletinDetailsAsync();
        }

        // Method for Global Bulletin checkbox (affects wholesale selection)
        private async Task OnGlobalBulletinCheckChanged(bool isChecked)
        {
            IsGlobalBulletinSelected = isChecked;
            await InvokeAsync(StateHasChanged);
        }

        // Overloaded method for loading bulletin details
        private async Task LoadBulletinDetailsAsync(Guid? servicePointId = null)
        {
            try
            {
                await loadingIndicator.Show();
                var request = new RequestPreviewBulletinDto
                {
                    IsGlobal = IsGlobalBulletin,
                    ServicePointId = servicePointId ?? (IsGlobalBulletin ? null : SelectedServicePointIds.FirstOrDefault())
                };
                BulletinDetails = await BulletinManagementMastersAppService.PreviewBulletin(request);

                await loadingIndicator.Hide();
                await InvokeAsync(StateHasChanged);

            }
            catch (Exception ex)
            {
                await loadingIndicator.Hide();
                await UiMessageService.Error(ex.Message);

            }
        }

        private async Task OpenCreateBulletinModalAsync()
        {
            NewBulletin = new BulletinManagementMasterCreateDto
            {
                BulletinDate = DateTime.Now,
                PublishDate = null
            };
            await LoadServicePointsAsync();

            SelectedServicePointIds = new List<Guid>();
            IsGlobalBulletinSelected = false;
            IsAllServicePointsSelected = false;
            BulletinDetails = new List<BulletinManagementDetailDto>();

            // Reset preview state when opening modal
            IsGlobalBulletin = false;
            CurrentPreviewServicePointId = null;
            CurrentPreviewContext = string.Empty;

            await CreateBulletinModal.Show();
        }

        private async Task CloseCreateBulletinModalAsync()
        {
            await CreateBulletinModal.Hide();
        }

        private async Task PublishBulletinAsync()
        {
            try
            {
                var servicePointIds = new List<Guid>();

                if (SelectedServicePointIds.Any())
                {
                    servicePointIds = SelectedServicePointIds.ToList();
                }


                await BulletinManagementMastersAppService.Publish(IsGlobalBulletinSelected, servicePointIds);
                await UiMessageService.Success(L["BulletinPublishedSuccessfully"]);
                await CloseCreateBulletinModalAsync();

                // Refresh the main grid
                await GetBulletinManagementMastersAsync();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                await UiMessageService.Error(ex.Message);
            }
        }

        // Show Detail Modal Methods
        private async Task OpenShowDetailModalAsync(BulletinManagementMasterDto bulletinMaster)
        {
            try
            {
                // Get the full bulletin master details
                SelectedBulletinMaster = await BulletinManagementMastersAppService.GetAsync(bulletinMaster.Id);

                // Get the bulletin details
                SelectedBulletinDetails = await BulletinManagementMastersAppService.GetListOfDetailsByMasterId(bulletinMaster.Id);

                // Reset to master tab
                SelectedDetailTab = "master";

                await ShowDetailModal.Show();
            }
            catch (Exception ex)
            {
                await UiMessageService.Error(ex.Message);
            }
        }

        private async Task CloseShowDetailModalAsync()
        {
            await ShowDetailModal.Hide();
            SelectedBulletinMaster = null;
            SelectedBulletinDetails.Clear();
        }

        private async Task OnSelectedDetailTabChanged(string tabName)
        {
            SelectedDetailTab = tabName;
            await InvokeAsync(StateHasChanged);
        }
    }
}
