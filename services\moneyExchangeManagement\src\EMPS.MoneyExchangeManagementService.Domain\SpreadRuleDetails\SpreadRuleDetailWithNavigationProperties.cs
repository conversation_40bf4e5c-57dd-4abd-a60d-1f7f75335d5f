using EMPS.MoneyExchangeManagementService.SpreadRules;

using System;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeManagementService.SpreadRuleDetails
{
    public class SpreadRuleDetailWithNavigationProperties
    {
        public SpreadRuleDetail SpreadRuleDetail { get; set; }

        public SpreadRule SpreadRule { get; set; }
        public string? ValidationMessage { get; set; }

        public bool IsBidSpreadInvalid { get; set; }
        public bool IsAskSpreadInvalid { get; set; }
        public bool IsBidMaxDiscountInvalid { get; set; }
        public bool IsBidMaxMarkdownInvalid { get; set; }
        public bool IsAskMaxDiscountInvalid { get; set; }
        public bool IsAskMaxMarkupInvalid { get; set; }


    }
}